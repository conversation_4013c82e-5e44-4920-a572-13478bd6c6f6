package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.facishare.paas.appframework.core.model.RequestContext.Attributes.*;

/**
 * Created by zhouwr on 2017/12/8
 */
public class ActionContextExt {

    public static final String NOT_VALIDATE = "not_validate";
    public static final String IS_SPECIFY_TIME = "isSpecifyTime";
    public static final String MASTER_DETAIL = "masterDetail";
    // 是否允许清空相关团队
    public static final String IS_ALLOW_DELETE_ALL_TEAM_MEMBER = "is_allow_delete_all_members";
    // 是否更新没传入的字段
    public static final String ABSENT_FIELD_NOT_CHANGE = "absent_field_not_change";
    // 指定创建人
    @Deprecated
    public static final String IS_SPECIFY_CREATED_BY = "isSpecifyCreatedBy";
    // 指定创建人
    public static final String DESIGNATED_CREATED_BY = "designatedCreatedBy";
    // 是否upsert
    public static final String IS_UPSERT = "is_upsert";
    // 是否使用本位币进行统计
    public static final String COUNT_WITH_FUNCTIONAL_CURRENCY = "count_with_functional_currency";
    public static final String SKIP_VERSION_CHANGE = "skip_version_change";

    //查询数据以后实时计算落地的计算字段
    public static final String CALCULATE_FORMULA = "calculateFormula";

    //查询数据以后实时计算落地的引用字段
    public static final String CALCULATE_QUOTE = "calculateQuote";

    //是否将引用字段转换成页面展示的格式
    public static final String CONVERT_QUOTE_FOR_VIEW = "convertQuoteForView";

    //查询数据以后实时计算统计字段
    public static final String CALCULATE_COUNT = "calculateCount";

    //是否从数据库计算统计字段
    public static final String CALCULATE_COUNT_FIELD_FROM_DB = "calculate_count_field_from_db";

    //是否返回富文本信息
    public static final String SEARCH_RICH_TEXT_EXTRA = "search_rich_text_extra";

    // 保留数据中的所有多语字段
    public static final String KEEP_ALL_MULTI_LANG_VALUE = "keep_all_multi_lang_value";
    // 针对部分数据需要全量更新， 跳过 remove 没变更的数据
    public static final String SKIP_REMOVE_NOT_CHANGE_DATA = "skip_remove_not_change_data";
    public static final String ENABLE_REAL_TIME_CALCULATE_DATA_AUTH = "enableRealTimeCalculateDataAuth";

    public static final String UNIQUE_CHECK_RETURN_EXISTS_ID = "unique_check_return_existsId";

    /**
     * 跳过填充额外信息的标识
     */
    public static final String SKIP_FILL_EXTRA_INFO = "skip_fill_extra_info";

    @Getter
    @Delegate
    private IActionContext context;

    private ActionContextExt(IActionContext context) {
        this.context = context;
    }

    public static ActionContextExt of(IActionContext context) {
        return new ActionContextExt(context);
    }

    public static ActionContextExt of(User user) {
        return of(user, RequestContextManager.getContext());
    }

    public static ActionContextExt of(User user, RequestContext requestContext) {
        ActionContext context = new ActionContext();
        ActionContextExt contextExt = new ActionContextExt(context);
        context.setEnterpriseId(user.getTenantId());
        context.setUserId(user.getUserId());
        context.setPrivilegeCheck(false);
        if (user.isOutUser()) {
            context.setOutEnterpriseId(user.getOutTenantId());
            context.setOutUserId(user.getOutUserId());
            context.setAppId(RequestUtil.getAppId());
            context.setUpstreamOwnerId(user.getUpstreamOwnerIdOrUserId());
        }

        if (requestContext != null) {
            context.setAppId(requestContext.getAppId());
            context.setInvokerHost(requestContext.getPeerHost());
            context.setInvokerName(requestContext.getPeerName());
            contextExt.setX_peerName(requestContext.getX_peerName());

            if (Objects.nonNull(requestContext.getEventId())) {
                contextExt.setEventId(requestContext.getEventId());
            }
            if (Objects.nonNull(requestContext.getAttribute(TRIGGER_FLOW))) {
                contextExt.setTriggerFlow(requestContext.getAttribute(TRIGGER_FLOW));
            }
            if (Objects.nonNull(requestContext.getAttribute(TRIGGER_WORK_FLOW))) {
                contextExt.setTriggerWorkFlow(requestContext.getAttribute(TRIGGER_WORK_FLOW));
            }
            if (Objects.nonNull(requestContext.getAttribute(NOT_VALIDATE))) {
                contextExt.setNotValidate(requestContext.getAttribute(NOT_VALIDATE));
            }
            //忽略必填字段的校验
            if (Objects.nonNull(requestContext.getAttribute(ActionContextKey.SKIP_REQUIRED_VALIDATE))) {
                contextExt.setSkipRequiredValidate(requestContext.getAttribute(ActionContextKey.SKIP_REQUIRED_VALIDATE));
            }
            if (Objects.nonNull(requestContext.getAttribute(IS_SPECIFY_TIME))) {
                contextExt.setIsSpecifyTime(requestContext.getAttribute(IS_SPECIFY_TIME));
            }
            if (Objects.nonNull(requestContext.getAttribute(IS_ALLOW_DELETE_ALL_TEAM_MEMBER))) {
                contextExt.setIsAllowDeleteAllTeamMembers(requestContext.getAttribute(IS_ALLOW_DELETE_ALL_TEAM_MEMBER));
            }
            if (Objects.nonNull(requestContext.getAttribute(ABSENT_FIELD_NOT_CHANGE))) {
                contextExt.setIsAllowDeleteAllTeamMembers(requestContext.getAttribute(ABSENT_FIELD_NOT_CHANGE));
            }
            if (Objects.nonNull(requestContext.getAttribute(IS_UPSERT))) {
                contextExt.setMTUpsert(requestContext.getAttribute(IS_UPSERT));
            }
            if (Objects.nonNull(requestContext.getOutIdentityType())) {
                context.setIdentityType(requestContext.getOutIdentityType());
            }
            if (Objects.nonNull(requestContext.getAttribute(ES_RECENT_UPDATE))) {
                contextExt.set(ActionContextKey.ES_REDIS_RECENT_UPDATE_CHECK, requestContext.getAttribute(ES_RECENT_UPDATE));
            }
            if (Objects.nonNull(requestContext.getAttribute(RequestContext.Attributes.INDUSTRY_DATASOURCE))) {
                contextExt.set(RequestContext.Attributes.INDUSTRY_DATASOURCE, requestContext.getAttribute(RequestContext.Attributes.INDUSTRY_DATASOURCE));
            }
            if (Objects.nonNull(requestContext.getAttribute(RequestContext.Attributes.INDUSTRY_SUPPLY_TAG))) {
                contextExt.set(RequestContext.Attributes.INDUSTRY_SUPPLY_TAG, requestContext.getAttribute(RequestContext.Attributes.INDUSTRY_SUPPLY_TAG));
            }
            if (Objects.nonNull(requestContext.getAttribute(RequestContext.Attributes.UPDATE_ORIGIN_SOURCE))) {
                contextExt.set(ActionContextKey.UPDATE_ORIGIN_SOURCE, requestContext.getAttribute(RequestContext.Attributes.UPDATE_ORIGIN_SOURCE));
            }
            //查询对象描述以后不做拷贝
            if (Boolean.TRUE.equals(requestContext.getAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY))) {
                contextExt.doNotDeepCopyDescribe();
            }
            // 从上下文中获取,本次查询是否要保留支持多语言的字段中各语言的数据
            if (Objects.nonNull(requestContext.getAttribute(KEEP_ALL_MULTI_LANG_VALUE))) {
                contextExt.setKeepAllMultiLangValue(requestContext.getAttribute(KEEP_ALL_MULTI_LANG_VALUE));
            }
            // 针对findbyid或findbyids id只有一条，既查询值返回一条数据的情况不做order by
            if (Objects.nonNull(requestContext.getAttribute(ActionContextKey.APPEND_ORDER_BY))) {
                contextExt.set(ActionContextKey.APPEND_ORDER_BY, requestContext.getAttribute(ActionContextKey.APPEND_ORDER_BY));
            }
            if (Objects.nonNull(requestContext.getAttribute(RequestContext.Attributes.ES_CURSOR_SEARCH))) {
                contextExt.set(ActionContextKey.ES_CURSOR_SEARCH, requestContext.getAttribute(RequestContext.Attributes.ES_CURSOR_SEARCH));
            }
            if (Objects.nonNull(requestContext.getAttribute(RequestContext.Attributes.SKIP_IMMUTABLE_FIELD_VALIDATE))) {
                contextExt.setSkipImmutableFieldValidate(Boolean.TRUE.equals(requestContext.getAttribute(SKIP_IMMUTABLE_FIELD_VALIDATE)));
            }
            if (Objects.nonNull(requestContext.getAttribute(DESIGNATED_CREATED_BY))) {
                contextExt.setDesignatedCreatedBy(Boolean.TRUE.equals(requestContext.getAttribute(DESIGNATED_CREATED_BY)));
            }
            if (Objects.nonNull(requestContext.getAttribute(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE))) {
                contextExt.setIgnoreBaseVisibleRange(Boolean.TRUE.equals(requestContext.getAttribute(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE)));
            }
            if (Objects.nonNull(requestContext.getAttribute(ActionContextKey.IGNORE_PUBLIC_EMPLOYEE_ENTERPRISE_RELATION_DEFAULT_FILTER))) {
                contextExt.set(ActionContextKey.IGNORE_PUBLIC_EMPLOYEE_ENTERPRISE_RELATION_DEFAULT_FILTER,
                        requestContext.getAttribute(ActionContextKey.IGNORE_PUBLIC_EMPLOYEE_ENTERPRISE_RELATION_DEFAULT_FILTER));
            }
        }

        //接口的请求设置batch=false，用于将mq放入实时队列
        contextExt.setBatch(RequestUtil.isBatch());

        Lang lang = Objects.isNull(requestContext) || Objects.isNull(requestContext.getLang()) ? Lang.zh_CN : requestContext.getLang();
        contextExt.setLang(lang);
        return contextExt;
    }

    public static ActionContextExt of(String tenantId) {
        ActionContextExt contextExt = new ActionContextExt(new ActionContext());
        contextExt.setEnterpriseId(tenantId);
        return contextExt;
    }

    public ActionContextExt set(String key, Object value) {
        context.put(key, value);
        return this;
    }

    public ActionContextExt dbType(String dbType) {
        setDbType(dbType);
        return this;
    }

    public ActionContextExt pgDbType() {
        return dbType(IActionContext.DBTYPE_PG);
    }

    public ActionContextExt allowUpdateInvalid(boolean allowUpdateInvalid) {
        setAllowUpdateInvalid(allowUpdateInvalid);
        return this;
    }

    public ActionContextExt setEventId(String eventId) {
        return set("eventId", eventId);
    }

    public ActionContextExt setTriggerFlow(Boolean triggerFlow) {
        return set(TRIGGER_FLOW, triggerFlow);
    }

    public ActionContextExt setTriggerWorkFlow(Boolean triggerWorkFlow) {
        return set(TRIGGER_WORK_FLOW, triggerWorkFlow);
    }

    public ActionContextExt setNotValidate(Boolean notValidate) {
        return set(NOT_VALIDATE, notValidate);
    }

    public ActionContextExt setSkipRequiredValidate(boolean skipRequiredValidate) {
        return set(ActionContextKey.SKIP_REQUIRED_VALIDATE, skipRequiredValidate);
    }

    public ActionContextExt setIsTool(Boolean isTool) {
        return set("isTool", isTool);
    }

    public ActionContextExt setIsSpecifyTime(Boolean isSpecifyTime) {
        return set("specify_time", isSpecifyTime);
    }

    public ActionContextExt setSpecifyCreateTime(Boolean isSpecifyTime) {
        return set("specify_create_time", isSpecifyTime);
    }

    public ActionContextExt setDiffWithoutFormat(Boolean diffWithoutFormat) {
        return set("diff_without_format", diffWithoutFormat);
    }

    public ActionContextExt setIsAllowDeleteAllTeamMembers(Boolean isAllowDeleteAllTeamMembers) {
        return set(IS_ALLOW_DELETE_ALL_TEAM_MEMBER, isAllowDeleteAllTeamMembers);
    }

    public ActionContextExt setSkipRemoveNotChangeData(Boolean skipRemoveNotChangeData) {
        return set(SKIP_REMOVE_NOT_CHANGE_DATA, skipRemoveNotChangeData);
    }

    public ActionContextExt setBatch(boolean batch) {
        return set("batch", batch);
    }

    public ActionContextExt setRecalculateAggregateFields(boolean isRecalculateAggregateFields) {
        return set("recalculateAggregateFields", isRecalculateAggregateFields);
    }

    public ActionContextExt setAllowUpdateQuoteField(boolean isAllowUpdateQuoteField) {
        return set("allow_update_quote_field", isAllowUpdateQuoteField);
    }

    public ActionContextExt skipRelevantTeam() {
        return set("skip_relevantTeam", true);
    }

    public ActionContextExt setSkipRelevantTeam(boolean skipRelevantTeam) {
        return set("skip_relevantTeam", skipRelevantTeam);
    }

    /**
     * 设置是否跳过填充额外信息
     *
     * @return
     */
    public ActionContextExt setSkipFillExtraInfo() {
        return set(SKIP_FILL_EXTRA_INFO, true);
    }

    public String getEventId() {
        return (String) get("eventId");
    }

    public Boolean getTriggerFlow() {
        return (Boolean) get(TRIGGER_FLOW);
    }

    public ActionContextExt setLang(Lang lang) {
        this.context.setLang(lang.getValue());
        return this;
    }

    public ActionContextExt setLang(String lang) {
        this.context.setLang(lang);
        return this;
    }

    public ActionContextExt setMasterDetail(boolean masterDetail) {
        set(MASTER_DETAIL, masterDetail);
        return this;
    }

    public ActionContextExt disableDeepQuote() {
        return set("need_deep_quote", false);
    }

    public ActionContextExt esSearchSkipRecentUpdateCheck() {
        return set(ActionContextKey.ES_SEARCH_SKIP_RECENT_UPDATE_CHECK, true);
    }

    public ActionContextExt esRedisRecentUpdateCheck() {
        return set(ActionContextKey.ES_REDIS_RECENT_UPDATE_CHECK, true);
    }

    public ActionContextExt setMTUpsert(boolean mtUpsert) {
        return set(ActionContextKey.MT_UPSERT, mtUpsert);
    }

    public ActionContextExt setCountWithFunctionalCurrency(boolean countWithFunctionalCurrency) {
        return set(COUNT_WITH_FUNCTIONAL_CURRENCY, countWithFunctionalCurrency);
    }

    public ActionContextExt doNotDeepCopyDescribe() {
        return set(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
    }

    public ActionContextExt doSkipConfig() {
        return set("skip_config", true);
    }

    public ActionContextExt doSkipVersionChange() {
        return set(SKIP_VERSION_CHANGE, true);
    }

    public ActionContextExt paginationOptimization() {
        return set(ActionContextKey.PAGINATION_OPTIMIZATION, true);
    }

    public ActionContextExt setCalculateFormula(boolean calculateFormula) {
        return set(CALCULATE_FORMULA, calculateFormula);
    }

    public boolean isCalculateFormula() {
        return Boolean.TRUE.equals(get(CALCULATE_FORMULA));
    }

    public ActionContextExt setCalculateQuote(boolean calculateQuote) {
        return set(CALCULATE_QUOTE, calculateQuote);
    }

    public boolean isCalculateQuote() {
        return Boolean.TRUE.equals(get(CALCULATE_QUOTE));
    }

    public ActionContextExt setConvertQuoteForView(boolean convertQuoteForView) {
        return set(CONVERT_QUOTE_FOR_VIEW, convertQuoteForView);
    }

    public ActionContextExt setSearchRichTextExtra(boolean searchRichTextExtra) {
        return set(SEARCH_RICH_TEXT_EXTRA, searchRichTextExtra);
    }

    public ActionContextExt setTimeoutNSecond(Integer timeoutNSecond) {
        if (Objects.nonNull(timeoutNSecond)) {
            return set(ActionContextKey.TIMEOUT_IN_SECOND, timeoutNSecond);
        }
        return this;
    }

    public boolean isConvertQuoteForView() {
        return Boolean.TRUE.equals(get(CONVERT_QUOTE_FOR_VIEW));
    }

    public ActionContextExt setCalculateCount(boolean calculateCount) {
        return set(CALCULATE_COUNT, calculateCount);
    }

    public boolean isCalculateCount() {
        return Boolean.TRUE.equals(get(CALCULATE_COUNT));
    }

    public ActionContextExt setCalculateCountFromDB(boolean calculateCountFromDB) {
        return set(CALCULATE_COUNT_FIELD_FROM_DB, calculateCountFromDB);
    }

    public boolean isCalculateCountFromDB() {
        return Boolean.TRUE.equals(get(CALCULATE_COUNT_FIELD_FROM_DB));
    }

    public ActionContextExt setX_peerName(String x_peerName) {
        set(InnerHeaders.X_PEER_NAME, x_peerName);
        return this;
    }

    public boolean isKeepAllMultiLangValue() {
        return Boolean.TRUE.equals(get(KEEP_ALL_MULTI_LANG_VALUE));
    }

    public ActionContextExt setKeepAllMultiLangValue(boolean keepAllMultiLangValue) {
        set(KEEP_ALL_MULTI_LANG_VALUE, keepAllMultiLangValue);
        return this;
    }

    public ActionContextExt setEsSearchSkipRecentUpdateCheck(boolean esSearchSkipRecentUpdateCheck) {
        set(ActionContextKey.ES_SEARCH_SKIP_RECENT_UPDATE_CHECK, esSearchSkipRecentUpdateCheck);
        return this;
    }

    public ActionContextExt setObjectDataSource(String source) {
        set(ActionContextKey.OBJECT_DATA_SOURCE, source);
        return this;
    }

    public void setUpdateIgnoreNotExistsData(boolean updateIgnoreNotExistsData) {
        set(ActionContextKey.UPDATE_IGNORE_NOT_EXISTS_DATA, updateIgnoreNotExistsData);
    }

    public ActionContextExt setActionType(String actionType) {
        if (StringUtils.isBlank(actionType)) {
            return this;
        }
        return set(ActionContextKey.ACTION_TYPE, actionType);
    }

    public ActionContextExt setIgnoreBaseVisibleRange(boolean ignoreBaseVisibleRange) {
        return set(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, ignoreBaseVisibleRange);
    }

    public ActionContextExt setSkipImmutableFieldValidate(boolean skipImmutableFieldValidate) {
        return set(SKIP_IMMUTABLE_FIELD_VALIDATE, skipImmutableFieldValidate);
    }

    //不更新最后修改时间
    public ActionContextExt skipUpdateLastModifiedTime() {
        return set(ActionContextKey.SKIP_UPDATE_LAST_MODIFIED_TIME, true);
    }

    public ActionContextExt setEnableRealTimeCalculateDataAuth(boolean enableRealTimeCalculateDataAuth) {
        return set(ENABLE_REAL_TIME_CALCULATE_DATA_AUTH, enableRealTimeCalculateDataAuth);
    }

    public ActionContextExt setMasterDetailRealTimeAuthCalculate(boolean realTimeCalculateDetailAuth) {
        return set(ActionContextKey.MASTER_DETAIL_REAL_TIME_AUTH_CALCULATE, realTimeCalculateDetailAuth);
    }

    public ActionContextExt setDesignatedCreatedBy(boolean isDesignatedCreatedBy) {
        return set(DESIGNATED_CREATED_BY, isDesignatedCreatedBy);
    }

    public ActionContextExt setUniqueCheckReturnExistsId(boolean enableUniqueCheckResult) {
        return set(UNIQUE_CHECK_RETURN_EXISTS_ID, enableUniqueCheckResult);
    }

    public ActionContextExt setDefaultValueFlag() {
        this.setLang("default");
        this.set(ActionContextKey.IGNORE_I18N, true);
        return this;
    }

    public ActionContextExt setStatOnEmptyResult(String statOnEmptyResult) {
        set(IActionContext.statEmptyResultStrategy, statOnEmptyResult);
        return this;
    }
}
