package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.rest.dto.data.BatchUpdateCreateTime;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 批量更新创建时间功能的单元测试
 * 测试 ObjectDataRestV3Service.batchUpdateCreateTime 方法
 */
@ExtendWith(MockitoExtension.class)
class BatchUpdateCreateTimeServiceTest {

    @Mock
    private MetaDataFindService metaDataFindService;
    
    @Mock
    private MetaDataService metaDataService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private FieldRelationCalculateService fieldRelationCalculateService;
    
    @Mock
    private MetaDataActionService metaDataActionService;

    @InjectMocks
    private ObjectDataRestV3Service objectDataRestV3Service;

    private User testUser;
    private RequestContext testRequestContext;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = User.builder()
                .tenantId("74255")
                .userId("1000")
                .build();

        // 创建测试请求上下文
        testRequestContext = RequestContext.builder()
                .user(testUser)
                .tenantId("74255")
                .build();
    }

    @Test
    void testBatchUpdateCreateTime_Success() {
        // Given
        BatchUpdateCreateTime.Arg arg = createValidArg();
        List<IObjectData> mockResult = createMockResultList();

        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class)) {

            // Mock RequestContextManager
            mockedContextManager.when(RequestContextManager::getContext).thenReturn(testRequestContext);

            // Mock ActionContextExt
            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            IActionContext mockActionContext = mock(IActionContext.class);
            
            mockedActionContextExt.when(() -> ActionContextExt.of(testUser)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.setIsSpecifyTime(true)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            // Mock MetaDataActionService
            when(metaDataActionService.batchUpdateByFields(
                    eq(mockActionContext), 
                    any(List.class), 
                    eq(Arrays.asList("createTime"))
            )).thenReturn(mockResult);

            // When
            BatchUpdateCreateTime.Result result = objectDataRestV3Service.batchUpdateCreateTime(arg);

            // Then
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(2, result.getUpdatedCount());

            // Verify interactions
            verify(metaDataActionService, times(1)).batchUpdateByFields(
                    eq(mockActionContext), 
                    any(List.class), 
                    eq(Arrays.asList("createTime"))
            );
        }
    }

    @Test
    void testBatchUpdateCreateTime_NullArg() {
        // Given
        BatchUpdateCreateTime.Arg arg = null;

        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            mockedI18NExt.when(() -> I18NExt.text(I18NKey.PARAM_ERROR)).thenReturn("Parameter error");

            // When & Then
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                objectDataRestV3Service.batchUpdateCreateTime(arg);
            });

            assertEquals("Parameter error", exception.getMessage());
        }
    }

    @Test
    void testBatchUpdateCreateTime_EmptyDescribeApiName() {
        // Given
        BatchUpdateCreateTime.Arg arg = BatchUpdateCreateTime.Arg.builder()
                .describeApiName("")
                .dataList(createValidDataList())
                .build();

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            objectDataRestV3Service.batchUpdateCreateTime(arg);
        });

        assertEquals("describeApiName cannot be empty", exception.getMessage());
    }

    @Test
    void testBatchUpdateCreateTime_NullDescribeApiName() {
        // Given
        BatchUpdateCreateTime.Arg arg = BatchUpdateCreateTime.Arg.builder()
                .describeApiName(null)
                .dataList(createValidDataList())
                .build();

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            objectDataRestV3Service.batchUpdateCreateTime(arg);
        });

        assertEquals("describeApiName cannot be empty", exception.getMessage());
    }

    @Test
    void testBatchUpdateCreateTime_EmptyDataList() {
        // Given
        BatchUpdateCreateTime.Arg arg = BatchUpdateCreateTime.Arg.builder()
                .describeApiName("test_object__c")
                .dataList(Lists.newArrayList())
                .build();

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            objectDataRestV3Service.batchUpdateCreateTime(arg);
        });

        assertEquals("dataList cannot be empty", exception.getMessage());
    }

    @Test
    void testBatchUpdateCreateTime_NullDataList() {
        // Given
        BatchUpdateCreateTime.Arg arg = BatchUpdateCreateTime.Arg.builder()
                .describeApiName("test_object__c")
                .dataList(null)
                .build();

        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            objectDataRestV3Service.batchUpdateCreateTime(arg);
        });

        assertEquals("dataList cannot be empty", exception.getMessage());
    }

    @Test
    void testBatchUpdateCreateTime_EmptyDataId() {
        // Given
        List<BatchUpdateCreateTime.Arg.DataItem> dataList = Lists.newArrayList();
        dataList.add(BatchUpdateCreateTime.Arg.DataItem.builder()
                .id("")
                .createTime(1640995200000L)
                .build());

        BatchUpdateCreateTime.Arg arg = BatchUpdateCreateTime.Arg.builder()
                .describeApiName("test_object__c")
                .dataList(dataList)
                .build();

        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class)) {
            mockedContextManager.when(RequestContextManager::getContext).thenReturn(testRequestContext);

            // When & Then
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                objectDataRestV3Service.batchUpdateCreateTime(arg);
            });

            assertEquals("data id cannot be empty", exception.getMessage());
        }
    }

    @Test
    void testBatchUpdateCreateTime_NullCreateTime() {
        // Given
        List<BatchUpdateCreateTime.Arg.DataItem> dataList = Lists.newArrayList();
        dataList.add(BatchUpdateCreateTime.Arg.DataItem.builder()
                .id("test_id_1")
                .createTime(null)
                .build());

        BatchUpdateCreateTime.Arg arg = BatchUpdateCreateTime.Arg.builder()
                .describeApiName("test_object__c")
                .dataList(dataList)
                .build();

        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class)) {
            mockedContextManager.when(RequestContextManager::getContext).thenReturn(testRequestContext);

            // When & Then
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                objectDataRestV3Service.batchUpdateCreateTime(arg);
            });

            assertEquals("createTime cannot be null", exception.getMessage());
        }
    }

    // Helper methods
    private BatchUpdateCreateTime.Arg createValidArg() {
        return BatchUpdateCreateTime.Arg.builder()
                .describeApiName("test_object__c")
                .dataList(createValidDataList())
                .build();
    }

    private List<BatchUpdateCreateTime.Arg.DataItem> createValidDataList() {
        List<BatchUpdateCreateTime.Arg.DataItem> dataList = Lists.newArrayList();
        dataList.add(BatchUpdateCreateTime.Arg.DataItem.builder()
                .id("test_id_1")
                .createTime(1640995200000L)
                .build());
        dataList.add(BatchUpdateCreateTime.Arg.DataItem.builder()
                .id("test_id_2")
                .createTime(1640995300000L)
                .build());
        return dataList;
    }

    private List<IObjectData> createMockResultList() {
        List<IObjectData> resultList = Lists.newArrayList();
        
        IObjectData data1 = new ObjectData();
        data1.setId("test_id_1");
        data1.setDescribeApiName("test_object__c");
        data1.setCreateTime(1640995200000L);
        resultList.add(data1);

        IObjectData data2 = new ObjectData();
        data2.setId("test_id_2");
        data2.setDescribeApiName("test_object__c");
        data2.setCreateTime(1640995300000L);
        resultList.add(data2);

        return resultList;
    }
}
