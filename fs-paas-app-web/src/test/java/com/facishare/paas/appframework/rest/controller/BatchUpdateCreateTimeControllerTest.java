package com.facishare.paas.appframework.rest.controller;

import com.facishare.paas.appframework.rest.dto.data.BatchUpdateCreateTime;
import com.facishare.paas.appframework.rest.service.ObjectDataRestV3Service;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 批量更新创建时间 REST API 控制器的单元测试
 * 测试 ObjectDataRestV3Controller.batchUpdateCreateTime 接口
 */
@ExtendWith(MockitoExtension.class)
class BatchUpdateCreateTimeControllerTest {

    @Mock
    private ObjectDataRestV3Service objectDataRestV3Service;

    @InjectMocks
    private ObjectDataRestV3Controller objectDataRestV3Controller;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    void testBatchUpdateCreateTime_Success() {
        // Given
        BatchUpdateCreateTime.Arg arg = createValidArg();
        BatchUpdateCreateTime.Result expectedResult = BatchUpdateCreateTime.Result.builder()
                .success(true)
                .updatedCount(2)
                .build();

        when(objectDataRestV3Service.batchUpdateCreateTime(any(BatchUpdateCreateTime.Arg.class)))
                .thenReturn(expectedResult);

        // When
        BatchUpdateCreateTime.Result actualResult = objectDataRestV3Controller.batchUpdateCreateTime(arg);

        // Then
        assertNotNull(actualResult);
        assertTrue(actualResult.isSuccess());
        assertEquals(2, actualResult.getUpdatedCount());

        // Verify service method was called
        verify(objectDataRestV3Service, times(1)).batchUpdateCreateTime(arg);
    }

    @Test
    void testBatchUpdateCreateTime_ServiceThrowsException() {
        // Given
        BatchUpdateCreateTime.Arg arg = createValidArg();
        RuntimeException expectedException = new RuntimeException("Service error");

        when(objectDataRestV3Service.batchUpdateCreateTime(any(BatchUpdateCreateTime.Arg.class)))
                .thenThrow(expectedException);

        // When & Then
        RuntimeException actualException = assertThrows(RuntimeException.class, () -> {
            objectDataRestV3Controller.batchUpdateCreateTime(arg);
        });

        assertEquals("Service error", actualException.getMessage());
        verify(objectDataRestV3Service, times(1)).batchUpdateCreateTime(arg);
    }

    @Test
    void testBatchUpdateCreateTime_NullArg() {
        // Given
        BatchUpdateCreateTime.Arg arg = null;
        BatchUpdateCreateTime.Result expectedResult = BatchUpdateCreateTime.Result.builder()
                .success(false)
                .updatedCount(0)
                .build();

        when(objectDataRestV3Service.batchUpdateCreateTime(null))
                .thenReturn(expectedResult);

        // When
        BatchUpdateCreateTime.Result actualResult = objectDataRestV3Controller.batchUpdateCreateTime(arg);

        // Then
        assertNotNull(actualResult);
        assertFalse(actualResult.isSuccess());
        assertEquals(0, actualResult.getUpdatedCount());

        verify(objectDataRestV3Service, times(1)).batchUpdateCreateTime(null);
    }

    @Test
    void testBatchUpdateCreateTime_EmptyDataList() {
        // Given
        BatchUpdateCreateTime.Arg arg = BatchUpdateCreateTime.Arg.builder()
                .describeApiName("test_object__c")
                .dataList(Lists.newArrayList())
                .build();

        BatchUpdateCreateTime.Result expectedResult = BatchUpdateCreateTime.Result.builder()
                .success(true)
                .updatedCount(0)
                .build();

        when(objectDataRestV3Service.batchUpdateCreateTime(any(BatchUpdateCreateTime.Arg.class)))
                .thenReturn(expectedResult);

        // When
        BatchUpdateCreateTime.Result actualResult = objectDataRestV3Controller.batchUpdateCreateTime(arg);

        // Then
        assertNotNull(actualResult);
        assertTrue(actualResult.isSuccess());
        assertEquals(0, actualResult.getUpdatedCount());

        verify(objectDataRestV3Service, times(1)).batchUpdateCreateTime(arg);
    }

    @Test
    void testBatchUpdateCreateTime_SingleItem() {
        // Given
        List<BatchUpdateCreateTime.Arg.DataItem> dataList = Lists.newArrayList();
        dataList.add(BatchUpdateCreateTime.Arg.DataItem.builder()
                .id("single_test_id")
                .createTime(1640995200000L)
                .build());

        BatchUpdateCreateTime.Arg arg = BatchUpdateCreateTime.Arg.builder()
                .describeApiName("test_object__c")
                .dataList(dataList)
                .build();

        BatchUpdateCreateTime.Result expectedResult = BatchUpdateCreateTime.Result.builder()
                .success(true)
                .updatedCount(1)
                .build();

        when(objectDataRestV3Service.batchUpdateCreateTime(any(BatchUpdateCreateTime.Arg.class)))
                .thenReturn(expectedResult);

        // When
        BatchUpdateCreateTime.Result actualResult = objectDataRestV3Controller.batchUpdateCreateTime(arg);

        // Then
        assertNotNull(actualResult);
        assertTrue(actualResult.isSuccess());
        assertEquals(1, actualResult.getUpdatedCount());

        verify(objectDataRestV3Service, times(1)).batchUpdateCreateTime(arg);
    }

    @Test
    void testBatchUpdateCreateTime_MultipleItems() {
        // Given
        BatchUpdateCreateTime.Arg arg = createValidArg();
        BatchUpdateCreateTime.Result expectedResult = BatchUpdateCreateTime.Result.builder()
                .success(true)
                .updatedCount(3)
                .build();

        when(objectDataRestV3Service.batchUpdateCreateTime(any(BatchUpdateCreateTime.Arg.class)))
                .thenReturn(expectedResult);

        // When
        BatchUpdateCreateTime.Result actualResult = objectDataRestV3Controller.batchUpdateCreateTime(arg);

        // Then
        assertNotNull(actualResult);
        assertTrue(actualResult.isSuccess());
        assertEquals(3, actualResult.getUpdatedCount());

        verify(objectDataRestV3Service, times(1)).batchUpdateCreateTime(arg);
    }

    @Test
    void testBatchUpdateCreateTime_VerifyMethodDelegation() {
        // Given
        BatchUpdateCreateTime.Arg arg = createValidArg();
        BatchUpdateCreateTime.Result expectedResult = BatchUpdateCreateTime.Result.builder()
                .success(true)
                .updatedCount(2)
                .build();

        when(objectDataRestV3Service.batchUpdateCreateTime(arg))
                .thenReturn(expectedResult);

        // When
        BatchUpdateCreateTime.Result actualResult = objectDataRestV3Controller.batchUpdateCreateTime(arg);

        // Then
        assertSame(expectedResult, actualResult);
        verify(objectDataRestV3Service, times(1)).batchUpdateCreateTime(arg);
        verifyNoMoreInteractions(objectDataRestV3Service);
    }

    @Test
    void testBatchUpdateCreateTime_VerifyArgumentPassing() {
        // Given
        BatchUpdateCreateTime.Arg arg = createValidArg();
        BatchUpdateCreateTime.Result expectedResult = BatchUpdateCreateTime.Result.builder()
                .success(true)
                .updatedCount(2)
                .build();

        when(objectDataRestV3Service.batchUpdateCreateTime(arg))
                .thenReturn(expectedResult);

        // When
        objectDataRestV3Controller.batchUpdateCreateTime(arg);

        // Then
        verify(objectDataRestV3Service, times(1)).batchUpdateCreateTime(arg);
        
        // Verify the exact argument was passed
        verify(objectDataRestV3Service).batchUpdateCreateTime(argThat(passedArg -> 
            passedArg != null &&
            "test_object__c".equals(passedArg.getDescribeApiName()) &&
            passedArg.getDataList() != null &&
            passedArg.getDataList().size() == 3
        ));
    }

    // Helper methods
    private BatchUpdateCreateTime.Arg createValidArg() {
        List<BatchUpdateCreateTime.Arg.DataItem> dataList = Lists.newArrayList();
        
        dataList.add(BatchUpdateCreateTime.Arg.DataItem.builder()
                .id("test_id_1")
                .createTime(1640995200000L)
                .build());
        
        dataList.add(BatchUpdateCreateTime.Arg.DataItem.builder()
                .id("test_id_2")
                .createTime(1640995300000L)
                .build());
        
        dataList.add(BatchUpdateCreateTime.Arg.DataItem.builder()
                .id("test_id_3")
                .createTime(1640995400000L)
                .build());

        return BatchUpdateCreateTime.Arg.builder()
                .describeApiName("test_object__c")
                .dataList(dataList)
                .build();
    }
}
