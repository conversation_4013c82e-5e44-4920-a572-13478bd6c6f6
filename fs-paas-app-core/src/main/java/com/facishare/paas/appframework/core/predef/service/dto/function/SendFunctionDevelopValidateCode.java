package com.facishare.paas.appframework.core.predef.service.dto.function;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

public interface SendFunctionDevelopValidateCode {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg{
        //手机号
        String phone;
        //图形验证码
        String captchaCode;
        //标识图形验证码的id
        String captchaId;
        //区域号
        String areaCode;

        public String getAreaCode() {
            //默认返回中国大陆 +86
            return StringUtils.isEmpty(areaCode) ? "+86" : areaCode;
        }
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result{
        int errorCode;
        String errorMessage;

        public static final int SUCCESS = 200;
        public static final int NEED_CAPTCHA = 300;//需要图形验证码
        public static final int ERROR = 500;
    }
}
