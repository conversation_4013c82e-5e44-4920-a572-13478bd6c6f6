package com.facishare.paas.appframework.core.predef.service.onlinedoc;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.GetPreviewUrlVo;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.QueryFileListVo;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginAppType;
import com.facishare.paas.appframework.metadata.onlinedoc.DocLogicService;
import com.facishare.paas.appframework.metadata.onlinedoc.model.GetPreviewUrl;
import com.facishare.paas.appframework.metadata.onlinedoc.model.QueryFileList;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> create by liy on 2024/6/5
 */
@Service
@ServiceModule("onlineDoc")
public class OnlineDocService {

    @Autowired
    private DocLogicService docLogicService;

    /**
     * 获取文件列表
     * NCRM/API/v1/object/onlineDoc/service/queryFileList
     */
    @ServiceMethod("queryFileList")
    public QueryFileListVo.Result queryFileList(QueryFileListVo.Arg arg, ServiceContext context) {
        QueryFileList.File folder = null;
        if (arg.getFolder() != null) {
            folder = new QueryFileList.File();
            folder.setFileId(arg.getFolder().getFileId());
            folder.setFileType(arg.getFolder().getFileType());
        }
        QueryFileList.Group group = null;
        if (arg.getGroup() != null) {
            group = new QueryFileList.Group();
            group.setId(arg.getGroup().getId());
            group.setName(arg.getGroup().getName());
        }
        QueryFileList.Result queryResult = docLogicService.queryFileList(
                context.getUser(),
                PackagePluginAppType.ONLINE_DOC.getType(),
                arg.getPluginApiName(),
                arg.isNeedNextPage(),
                folder,
                group,
                arg.getExtraInfo());
        List<QueryFileList.File> fileList = queryResult.getFileList() == null ? Lists.newArrayList() : queryResult.getFileList();
        List<QueryFileList.Group> groupList = queryResult.getGroupList() == null ? Lists.newArrayList() : queryResult.getGroupList();
        QueryFileListVo.Result result = new QueryFileListVo.Result();
        result.setErrorCode(queryResult.getErrorCode());
        result.setErrorMessage(queryResult.getErrorMessage());
        result.setAuthUrl(queryResult.getAuthUrl());
        result.setExtraInfo(queryResult.getExtraInfo());
        result.setHasMore(queryResult.isHasMore());
        result.setFileList(fileList.stream().map(x -> {
            QueryFileListVo.FileInfo fileInfo = new QueryFileListVo.FileInfo();
            fileInfo.setFileId(x.getFileId());
            fileInfo.setFileUrl(x.getFileUrl());
            fileInfo.setFileName(x.getFileName());
            fileInfo.setFileExt(x.getFileExt());
            fileInfo.setFileType(x.getFileType());
            fileInfo.setFileSize(x.getFileSize());
            fileInfo.setCreateTime(x.getCreateTime());
            fileInfo.setUpdateTime(x.getUpdateTime());
            fileInfo.setPluginApiName(x.getPluginApiName());
            return fileInfo;
        }).collect(Collectors.toList()));
        result.setGroupList(groupList.stream().map(x -> {
            QueryFileListVo.GroupInfo groupInfo = new QueryFileListVo.GroupInfo();
            groupInfo.setId(x.getId());
            groupInfo.setName(x.getName());
            groupInfo.setCreateTime(x.getCreateTime());
            groupInfo.setUpdateTime(x.getUpdateTime());
            return groupInfo;
        }).collect(Collectors.toList()));
        return result;
    }

    @ServiceMethod("getPreviewUrl")
    public GetPreviewUrlVo.Result getPreviewUrl(GetPreviewUrlVo.Arg arg, ServiceContext context) {
        QueryFileList.File file = new QueryFileList.File();
        file.setFileId(arg.getFileInfo().getFileId());
        file.setFileUrl(arg.getFileInfo().getFileUrl());
        file.setFileType(arg.getFileInfo().getFileType());
        file.setFileSize(arg.getFileInfo().getFileSize());
        file.setFileName(arg.getFileInfo().getFileName());
        file.setCreateTime(arg.getFileInfo().getCreateTime());
        file.setUpdateTime(arg.getFileInfo().getUpdateTime());

        QueryFileList.Group group = null;
        if (arg.getGroupInfo() != null) {
            group = new QueryFileList.Group();
            group.setId(arg.getGroupInfo().getId());
            group.setName(arg.getGroupInfo().getName());
            group.setCreateTime(arg.getGroupInfo().getCreateTime());
            group.setUpdateTime(arg.getGroupInfo().getUpdateTime());
        }
        //获取预览url
        GetPreviewUrl.Result previewResult = docLogicService.getPreviewUrl(
                context.getUser(),
                PackagePluginAppType.ONLINE_DOC.getType(),
                arg.getPluginApiName(),
                group,
                file);
        //返回结果
        GetPreviewUrlVo.Result result = new GetPreviewUrlVo.Result();
        result.setErrorCode(previewResult.getErrorCode());
        result.setErrorMessage(previewResult.getErrorMessage());
        result.setPreviewUrl(previewResult.getFileUrl());
        return result;
    }
}
