package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import com.facishare.paas.appframework.metadata.dto.TownInfo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface GetTownOptions {
    @Data
    class Arg {
        //地区类型：只支持省，市，区
        private String type;
        //当前地区代码
        private String code;
    }

    @Data
    @Builder
    class Result {
        private List<TownInfo> town;
    }
}
