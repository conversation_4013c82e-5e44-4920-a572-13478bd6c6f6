package com.facishare.paas.appframework.core.predef.service.onlinedoc;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.*;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginAppType;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginDefineType;
import com.facishare.paas.appframework.metadata.FileStoreService;
import com.facishare.paas.appframework.metadata.onlinedoc.PackagePluginLogicService;
import com.facishare.paas.appframework.metadata.repository.model.PackagePluginEntity;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> create by liy on 2024/6/5
 */
@Service
@ServiceModule("packagePlugin")
public class PackagePluginService {

    private static final Logger log = LoggerFactory.getLogger(PackagePluginService.class);
    private static final String PWC_API_NAME_NONE = "NONE";
    private static final int PLUGIN_COUNT_MAX = 100;
    private static final Map<String, String> DEV_INFO_EMPTY_MAP = ImmutableMap.of("NONE", "NONE");
    private static final Map<String, Object> RUNTIME_DATA_EMPTY_MAP = ImmutableMap.of("NONE", "NONE");

    @Autowired
    private PackagePluginLogicService packagePluginLogicService;
    @Autowired
    private FileStoreService fileStoreService;

    /**
     * 获取插件列表
     * NCRM/API/v1/object/packagePlugin/service/getAllPlugin
     */
    @ServiceMethod("getAllPlugin")
    public GetAllPluginVo.Result getAllPlugin(GetAllPluginVo.Arg arg, ServiceContext context) {
        arg.validate();
        List<PackagePluginEntity> pluginList = packagePluginLogicService.getPluginList(context.getUser(),
                arg.getAppType(),
                arg.getPageNumber(),
                arg.getPageSize() + 1);
        GetAllPluginVo.Result result = new GetAllPluginVo.Result();
        result.setHasMore(pluginList.size() > arg.getPageSize());
        result.setPluginList(pluginList.subList(0, Math.min(pluginList.size(), arg.getPageSize())).stream().map(x -> {
            GetAllPluginVo.PluginItem item = new GetAllPluginVo.PluginItem();
            item.setDefineType(x.getDefineType());
            item.setPluginName(PackagePluginUtils.name2I18n(x.getPluginApiName(), x.getDefineType(), x.getName()));
            item.setIcon(x.getIcon());
            item.setAppType(x.getAppType());
            item.setAppName(I18NExt.getOrDefault(PackagePluginAppType.of(x.getAppType()).getNameKey(), x.getAppType()));
            item.setIconUrl(x.isHttpIcon() ? x.getIcon() : fileStoreService.generateCpathAccessUrl(context.getUser(), x.getIcon(), x.getIconSign()));
            item.setBinding(x.getBinding());
            item.setActive(x.getActive());
            item.setPluginApiName(x.getPluginApiName());
            item.setExtraInfo(x.getExtraInfo());
            return item;
        }).collect(Collectors.toList()));
        return result;
    }

    /**
     * 获取当前正在使用的插件列表
     */
    @ServiceMethod("getUsingPlugin")
    public GetUsingPluginVo.Result getUsingPlugin(GetUsingPluginVo.Arg arg, ServiceContext context) {
        arg.validate();
        List<PackagePluginEntity> pluginList = packagePluginLogicService.getUsingPluginList(context.getUser(),
                arg.getAppType(),
                arg.getPageNumber(),
                arg.getPageSize() + 1);
        GetUsingPluginVo.Result result = new GetUsingPluginVo.Result();
        result.setHasMore(pluginList.size() > arg.getPageSize());
        result.setPluginList(pluginList.subList(0, Math.min(pluginList.size(), arg.getPageSize())).stream().map(x -> {
            GetAllPluginVo.PluginItem item = new GetAllPluginVo.PluginItem();
            item.setDefineType(x.getDefineType());
            item.setPluginName(PackagePluginUtils.name2I18n(x.getPluginApiName(), x.getDefineType(), x.getName()));
            item.setIcon(x.getIcon());
            item.setAppType(x.getAppType());
            item.setAppName(I18NExt.getOrDefault(PackagePluginAppType.of(x.getAppType()).getNameKey(), x.getAppType()));
            item.setIconUrl(x.isHttpIcon() ? x.getIcon() : fileStoreService.generateCpathAccessUrl(context.getUser(), x.getIcon(), x.getIconSign()));
            item.setBinding(x.getBinding());
            item.setActive(x.getActive());
            item.setPluginApiName(x.getPluginApiName());
            item.setExtraInfo(x.getExtraInfo());
            return item;
        }).collect(Collectors.toList()));
        return result;
    }

    /**
     * 创建插件
     */
    @ServiceMethod("createPlugin")
    public CreatePluginVo.Result createPlugin(CreatePluginVo.Arg arg, ServiceContext context) {
        arg.validate();
        List<PackagePluginEntity> pluginList = packagePluginLogicService.getPluginList(context.getUser(), arg.getAppType(), 0, PLUGIN_COUNT_MAX);
        if (pluginList.size() >= PLUGIN_COUNT_MAX) {
            log.warn("createPlugin failed too many plugins user:{}, arg:{}", context.getUser(), arg);
            CreatePluginVo.Result result = new CreatePluginVo.Result();
            result.setSuccess(false);
            return result;
        }
        PackagePluginEntity entity = new PackagePluginEntity();
        entity.setDefineType(arg.getDefineType());
        entity.setName(arg.getName());
        entity.setIcon(arg.getIcon());
        entity.setAppType(arg.getAppType());
        entity.setPluginApiName(arg.getPluginApiName());
        entity.setExtraInfo(arg.getExtraInfo());
        entity.setActive(false);
        entity.setBinding(false);
        entity.setDevInfo(Maps.newHashMap());
        entity.setRuntimeData(Maps.newHashMap());
        PackagePluginEntity dbEntity = packagePluginLogicService.createPlugin(context.getUser(), entity);
        CreatePluginVo.Result result = new CreatePluginVo.Result();
        result.setSuccess(true);
        return result;
    }

    /**
     * 更新插件
     */
    @ServiceMethod("updatePlugin")
    public UpdatePluginVo.Result updatePlugin(UpdatePluginVo.Arg arg, ServiceContext context) {
        arg.validate();
        Set<String> fields = Sets.newHashSet();
        PackagePluginEntity entity = packagePluginLogicService.findPluginByApiName(context.getUser(), arg.getPluginApiName());
        if (StringUtils.isNotBlank(arg.getName())
                && !arg.getName().equals(entity.getName())) {
            if (arg.getName().length() > AppFrameworkConfig.getOnlineDocPluginNameLengthMax()) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_CONTENT_TOO_LONG, "name"));
            }
            entity.setName(arg.getName());
            fields.add(PackagePluginEntity.PLUGIN_ENTITY_FILED_NAME);
        }
        if (StringUtils.isNotBlank(arg.getIcon())
                && !arg.getIcon().equals(entity.getIcon())) {
            entity.setIcon(arg.getIcon());
            fields.add(PackagePluginEntity.PLUGIN_ENTITY_FILED_ICON);
        }
        if (CollectionUtils.notEmpty(arg.getExtraInfo())) {
            entity.setExtraInfo(arg.getExtraInfo());
            fields.add(PackagePluginEntity.PLUGIN_ENTITY_FILED_EXTRA_INFO);
        }
        PackagePluginEntity dbEntity = packagePluginLogicService.updatePlugin(context.getUser(), entity, Lists.newArrayList(fields));
        UpdatePluginVo.Result result = new UpdatePluginVo.Result();
        result.setSuccess(true);
        return result;
    }

    /**
     * 删除插件
     */
    @ServiceMethod("deletePlugin")
    public DeletePluginVo.Result deletePlugin(DeletePluginVo.Arg arg, ServiceContext context) {
        arg.validate();
        PackagePluginEntity entity = packagePluginLogicService.findPluginByApiName(context.getUser(), arg.getPluginApiName());
        PackagePluginEntity dbEntity = packagePluginLogicService.deletePlugin(context.getUser(), entity);
        packagePluginLogicService.notifyRemovePersonalAuth(context.getUser(), PackagePluginAppType.ONLINE_DOC.getType(), arg.getPluginApiName());
        DeletePluginVo.Result result = new DeletePluginVo.Result();
        result.setSuccess(true);
        return result;
    }

    /**
     * 停用插件
     */
    @ServiceMethod("disablePlugin")
    public DisablePluginVo.Result disablePlugin(DisablePluginVo.Arg arg, ServiceContext context) {
        arg.validate();
        PackagePluginEntity entity = packagePluginLogicService.findPluginByApiName(context.getUser(), arg.getPluginApiName());
        entity.setActive(false);
        PackagePluginEntity dbEntity = packagePluginLogicService.updatePlugin(context.getUser(), entity, Lists.newArrayList(PackagePluginEntity.PLUGIN_ENTITY_FILED_IS_ACTIVE));
        DisablePluginVo.Result result = new DisablePluginVo.Result();
        result.setSuccess(true);
        return result;
    }

    /**
     * 启用插件
     */
    @ServiceMethod("enablePlugin")
    public EnablePluginVo.Result enablePlugin(EnablePluginVo.Arg arg, ServiceContext context) {
        arg.validate();
        PackagePluginEntity entity = packagePluginLogicService.findPluginByApiName(context.getUser(), arg.getPluginApiName());
        //未绑定，则报错提示
        if (PackagePluginAppType.ONLINE_DOC.getType().equals(entity.getAppType())
                && !entity.getBinding()) {
            throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_PLUGIN_ENTITY_ERROR_NEED_BANDING));
        }
        entity.setActive(true);
        PackagePluginEntity dbEntity = packagePluginLogicService.updatePlugin(context.getUser(), entity, Lists.newArrayList(PackagePluginEntity.PLUGIN_ENTITY_FILED_IS_ACTIVE));
        EnablePluginVo.Result result = new EnablePluginVo.Result();
        result.setSuccess(true);
        return result;
    }

    /**
     * 绑定企业开发账号
     */
    @ServiceMethod("bindingCompanyDevInfo")
    public BindingCompanyDevInfoVo.Result bindingCompanyDevInfo(BindingCompanyDevInfoVo.Arg arg, ServiceContext context) {
        arg.validate();
        PackagePluginEntity entity = packagePluginLogicService.findPluginByApiName(context.getUser(), arg.getPluginApiName());
        if (PackagePluginDefineType.SYSTEM_PERSONAL.getType().equals(entity.getDefineType())) {
            throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_PLUGIN_ENTITY_ERROR_DISABLE_BINDING));
        }
        entity.setDevInfo(arg.getDevInfoMap());
        entity.setBinding(true);
        List<String> fields = Lists.newArrayList(
                PackagePluginEntity.PLUGIN_ENTITY_FILED_IS_BINDING,
                PackagePluginEntity.PLUGIN_ENTITY_FILED_DEV_INFO);
        //改为先执行函数，再保存db
        PackagePluginEntity dbEntity = packagePluginLogicService.updatePlugin(context.getUser(), entity, fields);
        BindingCompanyDevInfoVo.Result result = new BindingCompanyDevInfoVo.Result();
        result.setSuccess(true);
        return result;
    }

    /**
     * 解绑企业开发账号
     */
    @ServiceMethod("unbindingCompanyDevInfo")
    public UnbindingCompanyDevInfoVo.Result unbindingCompanyDevInfo(UnbindingCompanyDevInfoVo.Arg arg, ServiceContext context) {
        arg.validate();
        PackagePluginEntity entity = packagePluginLogicService.findPluginByApiName(context.getUser(), arg.getPluginApiName());
        if (PackagePluginDefineType.SYSTEM_PERSONAL.getType().equals(entity.getDefineType())) {
            throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_PLUGIN_ENTITY_ERROR_DISABLE_UNBINDING));
        }
        //启用状态，不可解绑
        if (PackagePluginAppType.ONLINE_DOC.getType().equals(entity.getAppType())
                && entity.getActive()) {
            throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_PLUGIN_ENTITY_ERROR_NEED_DISABLE_FIRST));
        }
        entity.setDevInfo(DEV_INFO_EMPTY_MAP);
        entity.setRuntimeData(RUNTIME_DATA_EMPTY_MAP);
        entity.setBinding(false);
        List<String> fields = Lists.newArrayList(
                PackagePluginEntity.PLUGIN_ENTITY_FILED_IS_BINDING,
                PackagePluginEntity.PLUGIN_ENTITY_FILED_DEV_INFO,
                PackagePluginEntity.PLUGIN_ENTITY_FILED_RUNTIME_DATA);
        PackagePluginEntity dbEntity = packagePluginLogicService.updatePlugin(context.getUser(), entity, fields);
        //在线文档类型，要发mq异步清空用户授权记录
        if (PackagePluginAppType.ONLINE_DOC.getType().equals(entity.getAppType())) {
            packagePluginLogicService.notifyRemovePersonalAuth(context.getUser(), entity.getAppType(), arg.getPluginApiName());
        }
        UnbindingCompanyDevInfoVo.Result result = new UnbindingCompanyDevInfoVo.Result();
        result.setSuccess(true);
        return result;
    }

    @ServiceMethod("mark2SystemType")
    public Mark2SystemTypeVo.Result mark2SystemType(Mark2SystemTypeVo.Arg arg, ServiceContext context) {
        arg.validate();
        try {
            log.info("mark2SystemType begin arg:{}", arg);
            List<String> fields = Lists.newArrayList(PackagePluginEntity.PLUGIN_ENTITY_FILED_DEFINE_TYPE);
            PackagePluginEntity entity = packagePluginLogicService.findPluginByApiName(context.getUser(), arg.getPluginApiName());
            entity.setDefineType(PackagePluginDefineType.SYSTEM.getType());
            PackagePluginEntity dbEntity = packagePluginLogicService.updatePlugin(context.getUser(), entity, fields);
            log.info("mark2SystemType end entity:{}", entity);
        } catch (Exception e) {
            log.error("mark2SystemType error ", e);
            return new Mark2SystemTypeVo.Result();
        }
        Mark2SystemTypeVo.Result result = new Mark2SystemTypeVo.Result();
        result.setSuccess(true);
        return result;
    }

    /**
     * 手动标记为wps第三方个人应用
     */
    @ServiceMethod("mark2SystemPersonalType")
    public Mark2SystemPersonalTypeVo.Result mark2SystemPersonalType(Mark2SystemPersonalTypeVo.Arg arg, ServiceContext context) {
        arg.validate();
        try {
            log.info("mark2SystemPersonalType begin arg:{}", arg);
            List<String> fields = Lists.newArrayList(
                    PackagePluginEntity.PLUGIN_ENTITY_FILED_DEFINE_TYPE,
                    PackagePluginEntity.PLUGIN_ENTITY_FILED_EXTRA_INFO,
                    PackagePluginEntity.PLUGIN_ENTITY_FILED_DEV_INFO,
                    PackagePluginEntity.PLUGIN_ENTITY_FILED_IS_BINDING);
            //
            PackagePluginEntity entity = packagePluginLogicService.findPluginByApiName(context.getUser(), arg.getPluginApiName());
            entity.setDefineType(PackagePluginDefineType.SYSTEM_PERSONAL.getType());
            entity.getExtraInfo().put(PackagePluginEntity.PLUGIN_ENTITY_FILED_PWC_API_NAME, PWC_API_NAME_NONE);
            entity.setBinding(true);
            entity.setDevInfo(JSON.parseObject(arg.getDevInfoJson(), Map.class));
            PackagePluginEntity dbEntity = packagePluginLogicService.updatePlugin(context.getUser(), entity, fields);
            log.info("mark2SystemPersonalType end entity:{}", entity);
        } catch (Exception e) {
            log.error("mark2SystemPersonalType error ", e);
            return new Mark2SystemPersonalTypeVo.Result();
        }
        Mark2SystemPersonalTypeVo.Result result = new Mark2SystemPersonalTypeVo.Result();
        result.setSuccess(true);
        return result;
    }

    @ServiceMethod("mark2CustomType")
    public Mark2CustomTypeVo.Result mark2CustomType(Mark2CustomTypeVo.Arg arg, ServiceContext context) {
        arg.validate();
        try {
            log.info("mark2CustomType begin arg:{}", arg);
            List<String> fields = Lists.newArrayList(PackagePluginEntity.PLUGIN_ENTITY_FILED_DEFINE_TYPE);
            PackagePluginEntity entity = packagePluginLogicService.findPluginByApiName(context.getUser(), arg.getPluginApiName());
            entity.setDefineType(PackagePluginDefineType.CUSTOM.getType());
            PackagePluginEntity dbEntity = packagePluginLogicService.updatePlugin(context.getUser(), entity, fields);
            log.info("mark2CustomType end entity:{}", entity);
        } catch (Exception e) {
            log.error("mark2CustomType error ", e);
            return new Mark2CustomTypeVo.Result();
        }
        Mark2CustomTypeVo.Result result = new Mark2CustomTypeVo.Result();
        result.setSuccess(true);
        return result;
    }
}
