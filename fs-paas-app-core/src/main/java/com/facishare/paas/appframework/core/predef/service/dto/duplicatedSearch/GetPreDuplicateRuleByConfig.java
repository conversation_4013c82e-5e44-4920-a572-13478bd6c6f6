package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface GetPreDuplicateRuleByConfig {

    @Data
    class Arg {
        @JsonProperty("describe_api_name")
        private String describeApiName;
        @JsonProperty("type")
        private IDuplicatedSearch.Type type;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result{
        private IDuplicatedSearch duplicatedSearch;
    }
}
