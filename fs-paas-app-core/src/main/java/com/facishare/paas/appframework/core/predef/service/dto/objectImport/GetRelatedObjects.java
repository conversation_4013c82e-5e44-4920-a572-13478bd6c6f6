package com.facishare.paas.appframework.core.predef.service.dto.objectImport;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 获取从对象
 *
 * <AUTHOR>
 * @date 2020/4/7 12:40 下午
 */
public interface GetRelatedObjects {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {

        @JsonProperty("describe_api_name")
        @JSONField(name = "describe_api_name")
        String describeApiName;
    }

    @Data
    @Builder
    class Result {
        List<ObjectDescribeDocument> describes;
    }

}
