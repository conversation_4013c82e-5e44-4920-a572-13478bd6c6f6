package com.facishare.paas.appframework.core.predef.service.dto.publicobject;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.publicobject.module.EnterpriseRelationResult;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobInvitationInfo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhaooju on 2023/12/25
 */
public interface CompleteInvite {

    @Data
    class Arg {
        private String objectApiName;
        private String token;
        private String status;

        public boolean agree() {
            return PublicObjectJobInvitationInfo.AGREE_STATUS.equals(status);
        }

        public boolean reject() {
            return PublicObjectJobInvitationInfo.REJECT_STATUS.equals(status);
        }
    }

    class Result {

    }
}
