package com.facishare.paas.appframework.core.predef.service.dto.search;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface SearchByNameWithGroup {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        List<SearchData> dataList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SearchData {
        String apiName;
        String displayName;
        List<DataInfo> dataList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class DataInfo {
        String id;
        String name;
    }
}
