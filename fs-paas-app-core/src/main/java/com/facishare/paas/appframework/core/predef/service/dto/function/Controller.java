package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by fengjy in 2020/4/13 10:24
 */
public interface Controller {
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    class Arg{
        @JSONField(name = "api_name")
        @SerializedName(value = "api_name")
        @JsonProperty(value = "api_name")
        String apiName;

        List<FunctionInfo.Parameter> parameters;
    }
}
