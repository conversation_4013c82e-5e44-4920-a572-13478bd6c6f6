package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import lombok.Data;
import org.codehaus.jackson.annotate.JsonProperty;

import java.util.List;

public interface FindAssignedLayout {

    @Data
    class Arg {
        @JSONField(name = "M1")
        String describeApiName;
        String layoutType;
        private String whatDescribeApiName;
        private String sourceInfo;
        /**
         * 应用Id
         */
        private String appId;
    }

    @Data
    class Result {
        @J<PERSON>NField(name = "M6")
        private List role_list;

        @JSONField(name = "M7")
        private List layout_list;

        @JSONField(name = "M8")
        private List record_list;
        @JsonProperty("layout_manage_group")
        private ManageGroupDTO layoutManageGroup;
    }
}
