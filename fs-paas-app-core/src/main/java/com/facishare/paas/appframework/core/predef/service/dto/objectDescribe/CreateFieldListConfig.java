package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface CreateFieldListConfig {

    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("field_list")
        @SerializedName("field_list")
        List fieldList;

        @JSO<PERSON>ield(name = "M2")
        String objectDescribeApiName;

        @JSONField(name = "M3")
        String whatApiName;

        @JSONField(name = "M4")
        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        String extendAttribute;

        @JSONField(name = "M5")
        @JsonProperty("filter_fields")
        @SerializedName("filter_fields")
        List<String> filterFields;

        @JSONField(name = "M6")
        @JsonProperty("session_key")
        @SerializedName("session_key")
        private String sessionKey;

        @JSONField(name = "filter_fields_new")
        @JsonProperty("filter_fields_new")
        @SerializedName("filter_fields_new")
        private List<CommonFilterField.FilterField> filterFieldsNew;

        private Boolean covertTopListFilter;

    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        String value;
    }
}
