package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import lombok.Builder;
import lombok.Data;

import java.util.Set;

public interface QueryDimensionRuleCodeList {

  @Data
  class Arg {

    Set<String> receiveIds;

    Integer receiveType;

    String receiveTenantId;

  }


  @Data
  @Builder
  class Result {

    boolean success;

    Set<String> ruleCodeList;

  }

}
