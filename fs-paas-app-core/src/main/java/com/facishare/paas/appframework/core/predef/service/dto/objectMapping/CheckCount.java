package com.facishare.paas.appframework.core.predef.service.dto.objectMapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

public interface CheckCount {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("source_api_name")
        @SerializedName("source_api_name")
        private String sourceApiName;

        @JSONField(name = "M2")
        @JsonProperty("target_api_name")
        @SerializedName("target_api_name")
        private String targetApiName;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        @JsonProperty("ruleCount")
        @SerializedName("ruleCount")
        private Integer ruleCount;

        @JSONField(name = "M2")
        @JsonProperty("customButtonCount")
        @SerializedName("customButtonCount")
        private long customButtonCount;
    }
}
