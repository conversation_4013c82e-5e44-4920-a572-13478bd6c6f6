package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.facishare.paas.appframework.privilege.dto.DataShareRuleGroup;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;

public interface AddOrUpdateShareRuleGroups {

  @Data
  class Arg {

    String groupId;

    Set<String> describeApiNames;

    Integer permissionType;

    Set<String> sourceDeptIds;

    Set<String> sourceEmployeeIds;

    Set<String> sourceUserGroupIds;

    Set<String> sourceRoleIds;

    Set<String> targetDeptIds;

    Set<String> targetEmployeeIds;

    Set<String> targetUserGroupIds;

    Set<String> targetRoleIds;

    Integer basedType;

    //是否包含子部门 1 包含 0/null  不包含
    Integer receiveDeptCascade;

    public DataShareRuleGroup toDataShareRuleGroup(Arg arg) {
      return DataShareRuleGroup
        .builder()
        .groupId(arg.getGroupId())
        .describeApiNames(arg.getDescribeApiNames())
        .permissionType(arg.getPermissionType())
        .sourceDeptIds(arg.getSourceDeptIds())
        .sourceEmployeeIds(arg.getSourceEmployeeIds())
        .sourceUserGroupIds(arg.getSourceUserGroupIds())
        .sourceRoleIds(arg.getSourceRoleIds())
        .targetDeptIds(arg.getTargetDeptIds())
        .targetEmployeeIds(arg.getTargetEmployeeIds())
        .targetUserGroupIds(arg.getTargetUserGroupIds())
        .targetRoleIds(arg.getTargetRoleIds())
        .basedType(arg.getBasedType())
        .receiveDeptCascade(arg.getReceiveDeptCascade())
        .build();
    }
  }


  @Builder
  @Data
  class Result {
    List<String> result;
  }
}
