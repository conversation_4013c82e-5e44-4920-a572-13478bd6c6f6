package com.facishare.paas.appframework.core.predef.service.dto.describeExtra;

import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/2/24
 */
public interface FindDescribeExtra {
    @Data
    class Arg {
        private String describeApiName;
        private String describeExtraRenderType;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private ObjectDescribeDocument describeExtra;
    }
}
