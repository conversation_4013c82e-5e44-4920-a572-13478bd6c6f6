package com.facishare.paas.appframework.core.predef.service.dto.license;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.license.dto.QuotaInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Map;
import java.util.Set;

public interface QueryLicenseFeature {
    @Data
    class Arg {
        @JSONField(name = "items")
        @JsonProperty(value = "items")
        private Set<String> items;
        /**
         * 模块编码和参数键的映射
         * key: 模块编码
         * value: 参数键集合
         */
        @JSONField(name = "moduleParaMap")
        @JsonProperty(value = "moduleParaMap")
        private Map<String, Set<String>> moduleParaMap;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "feature")
        @JsonProperty(value = "feature")
        private Map<String, Boolean> feature;
        /**
         * 配额查询结果
         * key: 模块编码
         * value: 参数键和配额信息的映射
         */
        @JSONField(name = "quota")
        @JsonProperty(value = "quota")
        private Map<String, Map<String, QuotaInfo>> quota;
    }
}
