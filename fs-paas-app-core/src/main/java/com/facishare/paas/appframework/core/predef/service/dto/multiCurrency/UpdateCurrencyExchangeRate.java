package com.facishare.paas.appframework.core.predef.service.dto.multiCurrency;

import com.facishare.paas.appframework.metadata.repository.model.MtCurrencyExchange;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface UpdateCurrencyExchangeRate {

    @Data
    class Arg {
        private List<MtCurrencyExchange> currencyExchangeArgs;
        private String toCurrencyCode;
    }

    @Data
    @Builder
    class Result {
    }
}
