package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/5
 */
public interface BindingCompanyDevInfoVo {
    @Data
    class Arg {
        /**
         * 插件apiName
         */
        private String pluginApiName;
        /**
         * 开发信息
         * appId、appKey、
         * ...等等
         */
        private Map<String, String> devInfoMap;

        public void validate() {
            if (StringUtils.isBlank(pluginApiName)) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_PLUGIN_API_NAME));
            }
            if (CollectionUtils.isEmpty(devInfoMap)) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_PLUGIN_ENTITY_ERROR_MISSING_BINDING));
            }
        }
    }

    @Data
    class Result {
        /**
         * 是否成功
         */
        private boolean success;
    }
}
