package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface GetBasicSetting {
    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private Rules toolDuplicate;
        private Rules newDuplicate;
        private int version;
        private String describeApiName;
        private String id;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Rules {
        private Boolean enable;
        private Boolean invalidNotDuplicateSearch;
        private Boolean supportImport;
        private IDuplicatedSearch.Type type;
        private IDuplicatedSearch.RulesDef pendingRules;
    }
}
