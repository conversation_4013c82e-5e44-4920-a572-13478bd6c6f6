package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

public interface FindRoleAndRecordType {

    @Data
    class Arg {
        @JSONField(name = "M1")
        String describeApiName;

        private String sourceInfo;
    }

    @Data
    class Result {
        @J<PERSON>NField(name = "M6")
        private List role_list;

        @JSONField(name = "M8")
        private List record_list;
    }
}
