package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by zhouwr on 2022/12/1
 */
public interface ExpressionDebug {

    @Data
    class Arg {
        private ExpressionDTO expressionObject;
        private String dataId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        private boolean success;
        private String errorMessage;
        private String result;
    }
}
