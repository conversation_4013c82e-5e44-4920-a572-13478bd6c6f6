package com.facishare.paas.appframework.core.predef.service.dto.function;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface UploadFunction {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String apiName;
        String name;
        String content;
        String metaXml;
        long updateTime;
        String type;
        String nameSpace;
        String returnType;
        String bindingObjectApiName;
        String description;
        String commit;
    }

    @Data
    @Builder
    class Result  {
        long updateTime;
    }
}
