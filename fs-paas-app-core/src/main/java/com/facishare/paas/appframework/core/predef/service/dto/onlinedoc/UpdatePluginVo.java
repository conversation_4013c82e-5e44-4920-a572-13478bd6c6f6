package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/19
 */
public interface UpdatePluginVo {

    @Data
    class Arg {
        /**
         * 插件apiName,不可修改
         */
        private String pluginApiName;
        /**
         * 名称
         */
        private String name;
        /**
         * 图标 CPath
         */
        private String icon;
        /**
         * 扩展信息
         */
        private Map<String, String> extraInfo = Maps.newHashMap();

        /**
         * 参数校验
         */
        public void validate() {
            if (StringUtils.isBlank(pluginApiName)) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_PLUGIN_API_NAME));
            }
        }
    }

    @Data
    class Result {
        /**
         * 是否成功
         */
        private boolean success;
    }
}
