package com.facishare.paas.appframework.core.predef.service;

import com.facishare.fsc.api.model.CreateFileShareIds;
import com.facishare.fsc.api.service.SharedFileService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@ServiceModule("file_logic")
@Component
@Slf4j
public class FileLogicService {

    @Autowired
    private SharedFileService sharedFileService;

    @Autowired
    private GDSHandler gdsHandler;

    @ServiceMethod("npath_token")
    public CreateFileShareIds.Result npath2Token(CreateFileShareIds.Arg arg, ServiceContext context)  {
        String employeeId = (Strings.isNullOrEmpty(context.getUser().getUserId()) || context.getUser().isSupperAdmin())
                ? "10000" : context.getUser().getUserId();
        arg.employeeId = Integer.parseInt(employeeId);
        arg.ea = gdsHandler.getEAByEI(context.getUser().getTenantId());

        CreateFileShareIds.Result result = new CreateFileShareIds.Result();
        try {
            result = sharedFileService.createFileShareIds(arg);
        } catch (Exception e) {
            log.error("Error in createFileShareIds, arg:{}", arg, e);
        }
        return result;
    }


}
