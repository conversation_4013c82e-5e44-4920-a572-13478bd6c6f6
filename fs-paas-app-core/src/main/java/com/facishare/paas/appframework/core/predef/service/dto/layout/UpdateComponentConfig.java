package com.facishare.paas.appframework.core.predef.service.dto.layout;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2019/6/27
 */
public interface UpdateComponentConfig {

    @Data
    class Arg {
        private boolean reset;
        private String layoutApiName;
        private String objectApiName;
        private List<Map<String, Object>> components;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
    }

}
