package com.facishare.paas.appframework.core.predef.service.dto.publicobject;

import com.facishare.paas.appframework.metadata.publicobject.dto.ConnectedEnterpriseDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicFieldDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobType;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2023/12/13
 */
public interface CreatePublicObjectJob {

    @Data
    class Arg {
        private String objectApiName;
        private List<PublicFieldDTO> fields;
        private List<ConnectedEnterpriseDTO> enterpriseInfos;

        public PublicObjectJobParamDTO toCreateJobParam(PublicObjectJobType jobType) {
            return PublicObjectJobParamDTO.builder()
                    .objectApiName(objectApiName)
                    .fields(fields)
                    .enterpriseInfos(enterpriseInfos)
                    .jobType(jobType.getType())
                    .build();

        }
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private String jobId;
    }
}
