package com.facishare.paas.appframework.core.predef.service.dto.option.dependence;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * Created by zhao<PERSON>ju on 2022/8/1
 */
public interface FindAllOptionDependence {

    @Data
    class Arg {
        private String describeApiName;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private List<FieldDependenceDTO> fieldDependenceList;
    }
}
