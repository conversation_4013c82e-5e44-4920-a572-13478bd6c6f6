package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface AddDescribeCustomField {
    @Data
    class Arg {
        @J<PERSON>NField(name = "M1")
        private String describeAPIName;

        @JSONField(name = "M2")
        private String field_describe;

        @JSONField(name = "M3")
        private String layout_list;

        @JSONField(name = "M4")
        private String group_fields;

        
        private String persistentDataCalc;
        @JsonProperty("describe_extra")
        private ObjectDescribeDocument describeExtra;

        private List<Map<String, Object>> fieldsExtra;

        @JsonProperty("i18nInfoList")
        private List<I18nInfo> i18nInfoList;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        private Map objectDescribe;

        private ObjectDescribeDocument describeExtra;

        private List<Map<String, Object>> fieldsExtra;
    }
}
