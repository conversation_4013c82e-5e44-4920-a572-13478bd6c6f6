package com.facishare.paas.appframework.core.predef.service.dto.bizconf;

import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.fxiaoke.bizconf.bean.ConfigDefPojo;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2020/7/30
 */
public interface QueryAllConfigData {

    @Data
    class Arg {
        String describeApiName;
        String bizType;
    }

    @Data
    @Builder
    class Result {
        List<BizTypeValue> bizTypeValueList;
        List<ConfigDefPojo> configDefList;
        Map<String, List<ConfigData>> configDataMap;
    }

    @Data
    @Builder
    class BizTypeValue {
        String apiName;
        String label;

        public static BizTypeValue of(IRecordTypeOption recordTypeOption) {
            return BizTypeValue.builder()
                    .apiName(recordTypeOption.getApiName())
                    .label(recordTypeOption.getLabel())
                    .build();
        }
    }
}
