package com.facishare.paas.appframework.core.predef.service;


import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.archive.ArchiveRule;
import com.facishare.paas.appframework.core.predef.service.dto.archive.ArchiveRuleOperate;
import com.facishare.paas.appframework.core.predef.service.dto.archive.FindArchiveRule;
import com.facishare.paas.appframework.metadata.ObjectArchiveService;
import com.facishare.paas.metadata.api.IObjectArchiveRuleInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.impl.ObjectArchiveRuleInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@ServiceModule("archive_rule")
@Service
public class ArchiveRuleService {
    @Resource
    private ObjectArchiveService objectArchiveService;

    @ServiceMethod("create")
    public ArchiveRule.Result create(ServiceContext context, ArchiveRule.Arg arg) {
        List<IObjectArchiveRuleInfo> ruleInfos = convertObjectArchiveRuleInfo(arg.getRuleList(), context);
        objectArchiveService.create(context.getUser(), ruleInfos);
        return new ArchiveRule.Result();
    }

    @ServiceMethod("update")
    public ArchiveRule.Result update(ServiceContext context, ArchiveRule.Arg arg) {
        List<IObjectArchiveRuleInfo> ruleInfos = convertObjectArchiveRuleInfo(arg.getRuleList(), context);
        objectArchiveService.update(context.getUser(), ruleInfos);
        return new ArchiveRule.Result();
    }

    @ServiceMethod("list")
    public FindArchiveRule.Result list(ServiceContext context, FindArchiveRule.Arg arg) {
        List<IObjectArchiveRuleInfo> ruleInfos = objectArchiveService.list(context.getUser(), arg.getRuleName(), arg.getRuleApiName());
        List<MappingRuleDocument> mappingRuleDocuments = MappingRuleDocument.fromArchiveList(ruleInfos);
        mappingRuleDocuments.forEach(m -> m.remove(IObjectMappingRuleInfo.ACTION));
        return FindArchiveRule.Result.builder()
                .ruleList(mappingRuleDocuments)
                .build();

    }

    @ServiceMethod("find")
    public FindArchiveRule.Result find(ServiceContext context, FindArchiveRule.Arg arg) {
        if (StringUtils.isEmpty(arg.getRuleApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        List<IObjectArchiveRuleInfo> ruleInfos = objectArchiveService.find(context.getUser(), arg.getRuleApiName());
        List<MappingRuleDocument> mappingRuleDocuments = MappingRuleDocument.fromArchiveList(ruleInfos);
        mappingRuleDocuments.forEach(m -> m.remove(IObjectMappingRuleInfo.ACTION));
        return FindArchiveRule.Result.builder()
                .ruleList(mappingRuleDocuments)
                .build();
    }

    @ServiceMethod("delete")
    public ArchiveRuleOperate.Result delete(ServiceContext context, ArchiveRuleOperate.Arg arg) {
        if (StringUtils.isEmpty(arg.getRuleApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        objectArchiveService.delete(context.getUser(), arg.getRuleApiName());
        return new ArchiveRuleOperate.Result();
    }

    @ServiceMethod("enable")
    public ArchiveRuleOperate.Result enable(ServiceContext context, ArchiveRuleOperate.Arg arg) {
        if (StringUtils.isEmpty(arg.getRuleApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        objectArchiveService.enable(context.getUser(), arg.getRuleApiName());
        return new ArchiveRuleOperate.Result();
    }

    @ServiceMethod("disable")
    public ArchiveRuleOperate.Result disable(ServiceContext context, ArchiveRuleOperate.Arg arg) {
        if (StringUtils.isEmpty(arg.getRuleApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        objectArchiveService.disable(context.getUser(), arg.getRuleApiName());
        return new ArchiveRuleOperate.Result();
    }

    private List<IObjectArchiveRuleInfo> convertObjectArchiveRuleInfo(List<MappingRuleDocument> ruleList, ServiceContext context) {
        if (CollectionUtils.empty(ruleList)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        //1 创建归档规则
        List<IObjectArchiveRuleInfo> archiveRuleList = ruleList.stream()
                .map(ruleInfo -> {
                            ruleInfo.remove(IObjectMappingRuleInfo.ACTION);
                            return (IObjectArchiveRuleInfo) new ObjectArchiveRuleInfo(ruleInfo);
                        }
                ).collect(Collectors.toList());
        archiveRuleList.forEach(rule -> {
            if (StringUtils.equals("all", rule.getWhereType())) {
                rule.setWheres(Lists.newArrayList());
            }
            rule.setTenantId(context.getTenantId());
            if (StringUtils.isEmpty(rule.getId())) {
                rule.setCreatedBy(context.getUser().getUserId());
            }
            rule.setLastModifiedBy(context.getUser().getUserId());
        });
        return archiveRuleList;
    }
}
