package com.facishare.paas.appframework.core.predef.service.dto.publicobject;

import com.facishare.paas.appframework.metadata.publicobject.dto.PublicFieldDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicFieldTypeDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.DesignerResourceResult;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/19
 */
public interface FindDesignerResource {
    @Data
    class Arg {
        private String objectApiName;
    }

    @Data
    class Result {
        private List<PublicFieldDTO> fields;
        private List<PublicFieldTypeDTO> fieldTypes;

        public static Result fromDesignerResourceResult(DesignerResourceResult designerResourceResult) {
            Result result = new Result();
            result.setFields(designerResourceResult.getFields());
            result.setFieldTypes(designerResourceResult.getFieldTypes());
            return result;
        }

    }

}
