package com.facishare.paas.appframework.core.predef.service.dto.departmentDataPrivilege;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface UpsertDepartmentDataRights {

    @Data
    class Arg{
        List<String> entityIds;
        List<String> deptIds;
        Integer type;
        //0 部门内数据共享 1组织内数据共享
        int scene;
        boolean includeSub;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result{
        Object result;
    }


}
