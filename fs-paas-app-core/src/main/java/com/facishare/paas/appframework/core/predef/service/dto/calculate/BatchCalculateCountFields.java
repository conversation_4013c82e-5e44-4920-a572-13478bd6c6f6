package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2018/1/29
 */
public interface BatchCalculateCountFields {

    @Data
    class Arg {
        private String objectApiName;
        private String objectDataId;
        private List<CountField> countFieldList;

        public List<Count> getCountFieldDescribeList() {
            return countFieldList.stream().map(CountField::toCountFieldDescribe).collect(Collectors.toList());
        }
    }

    @Data
    @Builder
    class Result {
        private ObjectDataDocument countFieldValues;
    }

    @Data
    class CountField {
        private String apiName;
        private String relatedFieldName;
        private String countObjectApiName;
        private String countFieldApiName;
        private String countType;
        private String returnType;
        private int decimalScale;
        private List<LinkedHashMap> wheres;

        public CountFieldDescribe toCountFieldDescribe() {
            CountFieldDescribe countFieldDescribe = new CountFieldDescribe();
            countFieldDescribe.setApiName(apiName);
            countFieldDescribe.setFieldApiName(relatedFieldName);
            countFieldDescribe.setSubObjectDescribeApiName(countObjectApiName);
            countFieldDescribe.setCountFieldApiName(countFieldApiName);
            countFieldDescribe.setCountType(countType);
            countFieldDescribe.setReturnType(returnType);
            countFieldDescribe.setDecimalPlaces(decimalScale);
            countFieldDescribe.setWheres(wheres);

            return countFieldDescribe;
        }
    }


}
