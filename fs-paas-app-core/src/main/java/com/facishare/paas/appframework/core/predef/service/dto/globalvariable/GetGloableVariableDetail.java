package com.facishare.paas.appframework.core.predef.service.dto.globalvariable;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface GetGloableVariableDetail {

    @Data
    class Arg {
        @JSONField(name = "M1")
        private String apiName;

        @JSONField(name = "M2")
        private Boolean realTimeTrans;

    }

    @Data
    class Result {
        @JSONField(name = "M1")
        private Map global_Variable;

        @JSONField(name = "M2")
        private boolean success;
    }
}
