package com.facishare.paas.appframework.core.predef.service.dto.search;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface SearchByName {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        List<SearchByName.DataInfo> list;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class DataInfo {
        String apiName;
        String displayName;
        String id;
        String name;
    }
}
