package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;

/**
 * Created by zhouwr on 2018/12/21
 */
public interface CalculateCountOrFormulaField {
    @Data
    class Arg {
        private String objectApiName;
        private String fieldApiName;
        private String dataId;
    }

    @Data
    @Builder
    class Result {
        private ObjectDataDocument result;
    }
}
