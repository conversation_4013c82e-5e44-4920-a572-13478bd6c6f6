package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022-02-15-18:34
 */
public interface FindDescribeByGroupFieldType {

    @Data
    class Arg {
        private Set<String> fieldType;
        private String sourceInfo;
    }

    @Data
    @Builder
    class Result {
        private List<ObjectDescribeDocument> objectDescribeList;
        private ManageGroupDTO manageGroup;
    }
}
