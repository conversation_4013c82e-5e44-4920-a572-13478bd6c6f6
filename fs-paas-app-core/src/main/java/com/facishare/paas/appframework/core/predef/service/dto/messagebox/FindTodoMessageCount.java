package com.facishare.paas.appframework.core.predef.service.dto.messagebox;

import com.facishare.paas.appframework.common.service.dto.FindTodoCount;
import lombok.Builder;
import lombok.Data;

public interface FindTodoMessageCount {
    @Data
    class Arg {
    }

    @Data
    @Builder
    class Result {
        int total;
        int tobeAssignedSalesClue;
        int tobeProcessedSalesClue;
        int tobeReport;

        public static Result from(FindTodoCount.Result result) {
            return Result.builder()
                    .tobeAssignedSalesClue(result.getTobeAssignedSalesClue())
                    .tobeProcessedSalesClue(result.getTobeProcessedSalesClue())
                    .total(result.getTotal())
                    .tobeReport(result.getTobeReport())
                    .build();
        }
    }
}
