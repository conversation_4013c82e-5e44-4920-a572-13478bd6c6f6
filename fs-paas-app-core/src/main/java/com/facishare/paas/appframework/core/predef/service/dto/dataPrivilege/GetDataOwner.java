package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2018/1/3
 */
public interface GetDataOwner {

    @Data
    class Arg {
        private Map<String, List<String>> dataIdMap;
    }

    @Data
    @AllArgsConstructor
    class Result {
        private Map<String, String> ownerIdMap;
    }

}
