package com.facishare.paas.appframework.core.predef.service.dto.objectMapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

public interface CreateRule {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("rule_list")
        @SerializedName("rule_list")
        private List<MappingRuleDocument> ruleList;

        @JSONField(name = "M2")
        @JsonProperty("button")
        @SerializedName("button")
        private ButtonDocument button;

        @J<PERSON><PERSON>ield(name = "M3")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        private String describeApiName;

        @JSONField(name = "M4")
        @JsonProperty("roles")
        @SerializedName("roles")
        private List<String> roles;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        @JsonProperty("ruleList")
        @SerializedName("ruleList")
        private List<MappingRuleDocument> ruleList;
    }
}
