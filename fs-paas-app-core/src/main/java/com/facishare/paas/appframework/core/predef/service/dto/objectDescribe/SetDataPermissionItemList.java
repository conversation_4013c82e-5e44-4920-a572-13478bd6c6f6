package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface SetDataPermissionItemList {

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private List<DataPermissionItems> dataPermissionItemList;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class DataPermissionItems {
        private List<DataPermissionItem> dataPermissionItems;
        private String fieldApiName;
        private String objectApiName;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class DataPermissionItem {
        private String roleType;
        private Boolean isChecked;
    }

    @Builder
    @Data
    class Result {
        private Boolean success;
        private List<GetDataPermissionItems.DataPermissionGroupInfos> dataPermissionGroupInfos;
    }

}
