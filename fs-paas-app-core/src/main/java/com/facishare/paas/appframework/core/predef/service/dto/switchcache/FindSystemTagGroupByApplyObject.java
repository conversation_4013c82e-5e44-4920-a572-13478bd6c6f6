package com.facishare.paas.appframework.core.predef.service.dto.switchcache;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface FindSystemTagGroupByApplyObject {

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JsonProperty("describe_api_name")
        String describeApiName;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        List<ResultInfo> result;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ResultInfo {
        @JsonProperty("tag_group_api_name")
        String tagGroupApiName;
        @JsonProperty("tag_group_name")
        String tagGroupName;
        @JsonProperty("tag_group_id")
        String tagGroupId;
        @JsonProperty("is_enable")
        Boolean enable;
        @JsonProperty("tag_list")
        List<TagInfo> tagList;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class TagInfo {
        @JsonProperty("tag_id")
        String tagId;
        @JsonProperty("tag_name")
        String tagName;
        @JsonProperty("tag_api_name")
        String tagApiName;
        @JsonProperty("description")
        String description;
        @JsonProperty("is_enable")
        Boolean enable;
    }

}
