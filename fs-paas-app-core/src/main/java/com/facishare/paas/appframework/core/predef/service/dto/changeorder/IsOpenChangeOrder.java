package com.facishare.paas.appframework.core.predef.service.dto.changeorder;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface IsOpenChangeOrder {

    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    @Data
    class Arg{
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        @JSONField(name = "describe_api_name")
        String describeApiName;
    }
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    @Data
    class Result{
        Boolean result;
    }
}
