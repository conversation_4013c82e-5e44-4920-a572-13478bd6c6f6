package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface TeamRole {
    @Data
    class Arg {
        private String describeApiName;
    }
    @Data
    @Builder
    class Result {
        private List<TeamMemberRole> roleList;
    }

    @Data
    @Builder
    class TeamMemberRole {
        @JsonProperty("role_type")
        @JSONField(name = "role_type")
        private String roleType;
        @JsonProperty("role_name")
        @J<PERSON><PERSON>ield(name = "role_name")
        private String roleName;
        private int status;
    }
}
