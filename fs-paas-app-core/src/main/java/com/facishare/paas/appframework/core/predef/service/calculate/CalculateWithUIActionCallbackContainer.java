package com.facishare.paas.appframework.core.predef.service.calculate;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.uievent.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2020/06/18
 */
@Data
public class CalculateWithUIActionCallbackContainer implements ActionContainer {
    @NonNull
    private ServiceFacade serviceFacade;
    @NonNull
    private InfraServiceFacade infraServiceFacade;
    @NonNull
    private RequestContext requestContext;
    @NonNull
    private IObjectData objectData;
    private Map<String, List<IObjectData>> detailData = Maps.newHashMap();

    private IObjectDescribe objectDescribe;
    private Map<String, IObjectDescribe> detailDescribe = Maps.newHashMap();


    private UIEventProcess process;

    @Builder
    public CalculateWithUIActionCallbackContainer(@NonNull ServiceFacade serviceFacade,
                                                  @NonNull InfraServiceFacade infraServiceFacade,
                                                  @NonNull RequestContext requestContext,
                                                  @NonNull ObjectDataDocument objectData,
                                                  Map<String, List<ObjectDataDocument>> details,
                                                  IObjectDescribe objectDescribe) {
        this.serviceFacade = serviceFacade;
        this.infraServiceFacade = infraServiceFacade;
        this.requestContext = requestContext;
        init(objectData, details, objectDescribe);
    }

    public CalculateWithUIActionCallbackContainer initProcessor() {
        process = new SimpleUIEventProcess();
        // 构造Diff处理器
        DiffProcessor diffAfterFunction = new DiffProcessor.Builder(this)
                .setDiffMaster(true)
                .setDiffDetail(true)
                .setShieldFieldsAfterFunction(true).build();
        DiffProcessor diffAfterCompute = new DiffProcessor.Builder(this)
                .setDiffMaster(true)
                .setDiffDetail(false)
                .build();

        // 按照顺序添加处理器
        process.addOrderedProcessors(                  // 函数处理
                diffAfterFunction,       // 主从都diff,为了计算准备
                new ComputeProcessor(this),    // 计算
                diffAfterCompute, // 只diff主(计算只改变主z)
                new SupplementProcessor(this));
        return this;
    }

    public UIEventProcess.ProcessRequest invoke(ObjectDataDocument masterData, Map<String, List<ObjectDataDocument>> detailData) {
        IObjectData data = toObjectData(masterData, objectDescribe.getApiName());
        Map<String, List<IObjectData>> details = detailData.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream().map(it -> toObjectData(it, entry.getKey())))
                .collect(Collectors.groupingBy(IObjectData::getDescribeApiName, Collectors.mapping(it -> it, Collectors.toList())));
        UIEventProcess.ProcessRequest request = UIEventProcess.ProcessRequest.builder()
                .masterData(data)
                .detailDataMap(details)
                .build();
        process.invoke(request);
        return request;
    }

    private void init(@NonNull ObjectDataDocument objectData,
                      Map<String, List<ObjectDataDocument>> details,
                      @NonNull IObjectDescribe objectDescribe) {
        this.objectDescribe = objectDescribe;
        this.objectData = toObjectData(objectData, objectDescribe.getApiName());
        if (CollectionUtils.empty(details)) {
            return;
        }
        this.detailDescribe = serviceFacade.findObjects(requestContext.getTenantId(), details.keySet());
        this.detailData = details.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream().map(it -> toObjectData(it, entry.getKey())))
                .collect(Collectors.groupingBy(IObjectData::getDescribeApiName, Collectors.mapping(it -> it, Collectors.toList())));
    }

    private IObjectData toObjectData(@NonNull ObjectDataDocument objectData, String describeApiName) {
        IObjectData data = objectData.toObjectData();
        data.setTenantId(requestContext.getTenantId());
        data.setDescribeApiName(describeApiName);
        return data;
    }

    @Override
    public ServiceFacade getServiceFacade() {
        return serviceFacade;
    }

    @Override
    public InfraServiceFacade getInfraServiceFacade() {
        return infraServiceFacade;
    }

    @Override
    public RequestContext getContext() {
        return requestContext;
    }

    @Override
    public IObjectData getMasterData() {
        return objectData;
    }

    @Override
    public Map<String, List<IObjectData>> getDetailData() {
        return detailData;
    }

    @Override
    public IObjectDescribe getObjectDescribe() {
        return objectDescribe;
    }

    @Override
    public Map<String, IObjectDescribe> getDetailDescribe() {
        return detailDescribe;
    }

    @Override
    public void customHandleDiffedMaster(IObjectData masterWithOnlyChangedFields) {

    }

    @Override
    public void customHandleDiffedDetail(Map<String, List<IObjectData>> detailWithOnlyChangedFields) {

    }
}
