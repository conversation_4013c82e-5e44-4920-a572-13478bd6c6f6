package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface DuplicateSearchRuleList {
    @Data
    class Arg {
        // TODO: 2023/1/10 合并list接口和rule_priority_list
        @JsonProperty("describe_api_name")
        private String describeApiName;
        @JsonProperty("priortiy_by_order")
        private boolean priorityByOrder = false;
        @JsonProperty("type")
        private IDuplicatedSearch.Type type;
        @JsonProperty("source_info")
        private String sourceInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        List<DuplicateSearchSimpleInfo> list;
        Boolean useMultiRule;
        ManageGroupDTO manageGroup;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class DuplicateSearchSimpleInfo {
        private String duplicateRuleName;
        private String duplicateRuleApiName;    // 规则api
        private String describeApiName; // 关联对象api
        private IDuplicatedSearch.Type type;    // 新建规则还是查重工具
        boolean enable;
        private String mark;    // 描述
        private String lastModifyBy;
        private Long lastModifyTime;
        private String createdBy;
        private Long createTime;
        Integer order;
    }
}
