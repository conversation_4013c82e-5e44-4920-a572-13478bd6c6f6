package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.metadata.TeamMemberInfoPoJo;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.protostuff.Tag;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface GetTeamMember {
    @Data
    class Arg {
        @JSONField(name = "M1")
        String dataID;

        @JSONField(name = "M2")
        String objectDescribeApiName;

        @JSONField(name = "M3")
        @JsonProperty("includeOutMember")
        boolean includeOutMember;
    }

    @Data
    @Builder
    class Result {
        @Tag(1)
        @JSONField(name = "M1")
        List<TeamMemberInfoPoJo> teamMemberInfos;

        @JSONField(name = "M2")
        Boolean hasEditPermission;

        @JSONField(name = "M3")
        List<ButtonDocument> buttons;

        @JSONField(name = "M4")
        Boolean isShowingOutTeamMember;

        Boolean isShowingErDepartmentTeamMember;

        // 相关团队选人组件，屏蔽「全选」按钮
        Boolean disableSelectAll;
    }

}
