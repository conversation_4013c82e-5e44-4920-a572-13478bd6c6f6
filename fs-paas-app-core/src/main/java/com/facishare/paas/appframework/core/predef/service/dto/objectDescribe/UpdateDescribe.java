package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface UpdateDescribe {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("json_data")
        @SerializedName(value = "json_data")
        private String jsonData;

        @JSONField(name = "M2")
        @JsonProperty("json_layout")
        @SerializedName(value = "json_layout")
        private String jsonLayout;

        @JSONField(name = "M3")
        @JsonProperty("include_layout")
        @SerializedName(value = "include_layout")
        private boolean includeLayout;

        @JSONField(name = "M4")
        @JsonProperty("active")
        @SerializedName(value = "active")
        private boolean active;

        @JsonProperty("describe_extra")
        @SerializedName(value = "describe_extra")
        private ObjectDescribeDocument describeExtra;

//        @JSONField(name = "M5")
//        @JsonProperty("dbType")
//        @SerializedName(value = "dbType")
//        private String dbType;
//
//        @JSONField(name = "M6")
//        @JsonProperty("slot_created")
//        @SerializedName(value = "slot_created")
//        private boolean slot_created;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        private Object layout;

        @JSONField(name = "M2")
        private Map objectDescribe;

        private ObjectDescribeDocument describeExtra;
    }
}
