package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface GetRelatedResults {
    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
        private IDuplicatedSearch.Type type;
        private ObjectDataDocument objectData;
        private Boolean isNeedDuplicate;
        //用于第二次查重时,标识上次查到重复的规则是哪个,方便从redis中取数据
        private String ruleApiNameIntercepted;
        //查重工具查重时,标识要查重的规则
        private String duplicateRuleApiName;
        private Integer pageSize;
        private Integer pageNumber;
        //当规则apiName为空时，是否使用优先级最高的规则查重
        private Boolean useFirstRule;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private List<GetResult.Result> results;
    }
}
