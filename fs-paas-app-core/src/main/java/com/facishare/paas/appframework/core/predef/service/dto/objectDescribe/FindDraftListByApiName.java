package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 18/1/17.
 */
public interface FindDraftListByApiName {
  @Data
  class Arg {
    @JSONField(name = "M1")
    @JsonProperty("draft_apiname_list")
    @SerializedName("draft_apiname_list")
    List<String> draftApiNameList;

  }

  @Data
  class Result {
    public List<ObjectDescribeDocument> objectDescribeDraftList;
  }
}
