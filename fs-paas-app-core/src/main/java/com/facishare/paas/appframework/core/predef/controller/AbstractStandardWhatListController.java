package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.plugin.APLControllerPlugin;
import com.facishare.paas.appframework.core.model.plugin.Plugin;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardWhatListController.Arg;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.layout.WhatComponentRender;
import com.facishare.paas.appframework.metadata.layout.component.FlowTaskListMobileComponentExt;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.What;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2018/11/14
 */
public abstract class AbstractStandardWhatListController<A extends Arg> extends BaseListController<A> {
    protected Map<String, IObjectDescribe> objects;
    protected ObjectDescribeExt whatDescribe;
    protected LayoutExt whatLayoutExt;
//    QueryResult<IObjectData> queryResult;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.WhatListHeader.getFuncPrivilegeCodes();
    }

    @Override
    protected void before(A arg) {
        // 不需要走功能权限
        super.before(arg);
        stopWatch.lap("findObject");
    }

    @Override
    protected Result doService(A arg) {
        whatLayoutExt = findWhatObjectLayout();
        List<ILayout> layouts = findWhatListLayouts();
        stopWatch.lap("findWhatListLayouts");

        SearchTemplateQuery query = buildSearchTemplateQuery(layouts);
        stopWatch.lap("buildSearchTemplateQuery");

        queryResult = getQueryResult(query);

        if (CollectionUtils.empty(layouts)) {
            // 查所有时，不需要查当前流程对象的 layout
            List<IObjectDescribe> whatDescribes = objects.values().stream()
                    .filter(iObjectDescribe -> !Objects.equals(iObjectDescribe.getApiName(), objectDescribe.getApiName()))
                    .collect(Collectors.toList());
            layouts.addAll(findWhatListLayouts(whatDescribes));
            stopWatch.lap("findWhatListLayouts#Collection");
        }

        return buildResult(layouts, query, queryResult);
    }

    @Override
    protected LayoutAgentType getLayoutAgentType() {
        if (LayoutAgentType.MOBILE.getCode().equals(arg.getLayoutAgentType())) {
            return LayoutAgentType.MOBILE;
        }
        return super.getLayoutAgentType();
    }

    @Override
    protected ObjectDescribeExt findObject() {
        objects = serviceFacade.findObjects(controllerContext.getTenantId(),
                Lists.newArrayList(arg.getObjectDescribeApiName(), arg.getWhatApiName()));
        Lists.newArrayList(objects.keySet()).forEach(k -> objects.put(k, objects.get(k).copy()));


        //先在这个地方处理，可以到跟底层处理掉
        objects.forEach((k, v) -> v.removeFieldDescribe("extend_obj_data_id"));
        if (!Strings.isNullOrEmpty(arg.getWhatApiName())) {
            whatDescribe = ObjectDescribeExt.of(objects.get(arg.getWhatApiName()));
        }
        return ObjectDescribeExt.of(objects.get(arg.getObjectDescribeApiName()));
    }

    protected SearchTemplateQuery buildSearchTemplateQuery(List<ILayout> whatLayouts) {
        SearchTemplateQuery searchTemplateQuery = serviceFacade.getSearchTemplateQuery(controllerContext.getUser(), objectDescribe,
                getSearchTemplateId(), getSearchQueryInfo());
        searchTemplateQuery.setFindExplicitTotalNum(getFindExplicitTotalNum());
        if (Strings.isNullOrEmpty(arg.getWhatApiName())) {
            return searchTemplateQuery;
        }

        // 查what对象的所有字段
        List<String> whatFieldApiNames = getWhatFieldApiNames();

        searchTemplateQuery.setWhatDescribeApiName(arg.getWhatApiName());
        searchTemplateQuery.setWhatFieldApiNames(Lists.newArrayList(whatFieldApiNames));

        String apiNameField = getWhatApiNameField();
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, apiNameField, arg.getWhatApiName());
        return searchTemplateQuery;
    }

    private String getWhatApiNameField() {
        if (AppFrameworkConfig.isWhatListWhatFieldGray(controllerContext.getTenantId())) {
            return getWhatField().getApiNameFieldApiName();
        }
        if (Utils.BPM_TASK_API_NAME.equals(arg.getObjectDescribeApiName())) {
            return ObjectDataExt.OBJECT_API_NAME_HUMP;
        }
        return ObjectDataExt.OBJECT_API_NAME;
    }

    private What getWhatField() {
        return objectDescribe.getWhatField()
                .orElseThrow(IllegalArgumentException::new);
    }

    private String getWhatDescribeApiName(What whatField, ObjectDataExt objectDataExt) {
        if (AppFrameworkConfig.isWhatListWhatFieldGray(controllerContext.getTenantId())) {
            return objectDataExt.getWhatObjectApiName(whatField);
        }
        return objectDataExt.getObjectApiName();
    }

    private String getWhatObjectDataId(What whatField, ObjectDataExt objectDataExt) {
        if (AppFrameworkConfig.isWhatListWhatFieldGray(controllerContext.getTenantId())) {
            return objectDataExt.getWhatObjectDataId(whatField);
        }
        return objectDataExt.getObjectDataId();
    }

    private IObjectData getWhatObjectData(IObjectData data, What whatField) {
        if (AppFrameworkConfig.isWhatListWhatFieldGray(controllerContext.getTenantId())) {
            return ObjectDataExt.of(data).getWhatObjectData(whatField);
        }
        return ObjectDataExt.of(data).getWhatObjectData();
    }

    private List<String> getWhatFieldApiNames() {
        if (Objects.isNull(whatLayoutExt)) {
            return whatDescribe.getWhatObjectFieldDescribes()
                    .stream()
                    .map(field -> WhatComponentExt.getWhatFieldName(arg.getWhatApiName(), field.getApiName()))
                    .collect(Collectors.toList());
        }
        return whatDescribe.getFieldByApiNames(whatLayoutExt.getFieldList())
                .stream()
                .map(field -> WhatComponentExt.getWhatFieldName(arg.getWhatApiName(), field.getApiName()))
                .collect(Collectors.toList());
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = findData(query);
        stopWatch.lap("findData");

        return getQueryResult(queryResult);
    }

    private QueryResult<IObjectData> getQueryResult(QueryResult<IObjectData> queryResult) {
        What whatField = getWhatField();
        if (!Strings.isNullOrEmpty(arg.getWhatApiName())) {
            List<IObjectData> dataList = queryResult.getData();
            // 分离what对象的data，并且异步处理引用字段和 lookup 字段
            Set<IObjectData> whatObjectDataSet = dataList.stream()
                    .map(data -> getWhatObjectData(data, whatField))
                    .collect(Collectors.toSet());
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask()
                    .submit(() -> asyncFillFieldInfo(objectDescribe, dataList))
                    .submit(() -> asyncFillFieldInfo(whatDescribe, Lists.newArrayList(whatObjectDataSet)));

            if (Utils.CUSTOMER_PAYMENT_API_NAME.equals(arg.getWhatApiName())) {
                parallelTask.submit(() -> serviceFacade.parsePaymentObjOrderNames(whatDescribe,
                        Lists.newArrayList(whatObjectDataSet), controllerContext.getUser(), true));
            }
            try {
                parallelTask.await(5, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.error("Error in async fill field info whatListController#getQueryResult, ei:{}, object:{}, whatObject:{}",
                        controllerContext.getTenantId(), objectDescribe.getApiName(), whatDescribe.getApiName(), e);
            }
            stopWatch.lap("asyncFillFieldInfo");

            // 合并data
            Map<String, IObjectData> objectDataIdMap = whatObjectDataSet.stream()
                    .collect(Collectors.toMap(IObjectData::getId, data -> data));
            dataList.forEach(data -> {
                ObjectDataExt objectDataExt = ObjectDataExt.of(data);
                String objectDataId = getWhatObjectDataId(whatField, objectDataExt);
                if (!Strings.isNullOrEmpty(objectDataId)) {
                    objectDataExt.mergeWhatData(objectDataIdMap.get(objectDataId));
                }
            });
            return queryResult;
        }

        List<IObjectData> dataList = queryResult.getData();
        Map<String, List<String>> objectIdNameMap = Maps.newHashMap();
        dataList.forEach(data -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            String describeApiName = getWhatDescribeApiName(whatField, objectDataExt);
            String objectDataId = getWhatObjectDataId(whatField, objectDataExt);
            if (Strings.isNullOrEmpty(objectDataId)) {
                return;
            }
            if (objectIdNameMap.containsKey(describeApiName)) {
                objectIdNameMap.get(describeApiName).add(objectDataId);
            } else {
                objectIdNameMap.put(describeApiName, Lists.newArrayList(objectDataId));
            }
        });

        // 异步查询数据
        Map<String, IObjectData> objectDataIdMap = getObjectDataIdMap(objectIdNameMap);
        stopWatch.lap("getObjectDataIdMap");
        // 查whatList的对象
        objects.putAll(serviceFacade.findObjects(controllerContext.getTenantId(), objectIdNameMap.keySet()));

        Map<String, List<IObjectData>> apiNameDataMap = Maps.newConcurrentMap();
        objectDataIdMap.values().forEach(data -> {
            String describeApiName = ObjectDataExt.of(data).getDescribeApiName();
            if (apiNameDataMap.containsKey(describeApiName)) {
                apiNameDataMap.get(describeApiName).add(data);
            } else {
                apiNameDataMap.put(describeApiName, Lists.newArrayList(data));
            }
        });

        // 异步处理引用字段和 lookup 字段
        processQuoteAndLookupField(dataList, apiNameDataMap);
        stopWatch.lap("processQuoteAndLookupField");

        dataList.forEach(data -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            // 已办场景对应的数据被删除时，不作处理
            String objectDataId = getWhatObjectDataId(whatField, objectDataExt);
            Optional.ofNullable(objectDataId)
                    .map(objectDataIdMap::get)
                    .ifPresent(objectDataExt::mergeWhatData);
        });
        //先在这个地方处理，可以到跟底层处理掉
        objects.forEach((k, v) -> v.removeFieldDescribe("extend_obj_data_id"));
        stopWatch.lap("findObjects");
        return queryResult;
    }

    private void processQuoteAndLookupField(List<IObjectData> dataList, Map<String, List<IObjectData>> apiNameDataMap) {
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask()
                .submit(() -> asyncFillFieldInfo(objectDescribe, dataList));
        apiNameDataMap.forEach((whatApiName, whatDataList) ->
                parallelTask.submit(() -> asyncFillFieldInfo(objects.get(whatApiName), whatDataList)));
        if (CollectionUtils.notEmpty(apiNameDataMap.get(Utils.CUSTOMER_PAYMENT_API_NAME))) {
            parallelTask.submit(() -> serviceFacade.parsePaymentObjOrderNames(objects.get(Utils.CUSTOMER_PAYMENT_API_NAME),
                    apiNameDataMap.get(Utils.CUSTOMER_PAYMENT_API_NAME), controllerContext.getUser(), true));
        }
        try {
            parallelTask.await(6, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("Error in async fill field info whatListController#processQuoteAndLookupField, ei:{}, object:{}",
                    controllerContext.getTenantId(), objectDescribe.getApiName(), e);
        }
    }

    private Map<String, IObjectData> getObjectDataIdMap(Map<String, List<String>> objectIdNameMap) {
        Map<String, IObjectData> objectDataIdMap = Maps.newConcurrentMap();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        objectIdNameMap.forEach((objectApiName, ids) -> parallelTask.submit(() -> objectDataIdMap.putAll(findByIds(ids, objectApiName)
                .stream().collect(Collectors.toMap(IObjectData::getId, data -> data)))));
        try {
            parallelTask.await(7, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("Error in async fill field info whatListController#getObjectDataIdMap, ei:{}, object:{}",
                    controllerContext.getTenantId(), objectDescribe.getApiName(), e);
        }
        return objectDataIdMap;
    }

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult;
        if (AppFrameworkConfig.isGrayWhatListIsAllowDeleteAllMembers(controllerContext.getTenantId(), objectDescribe.getApiName())) {
            queryResult = serviceFacade.findBySearchQuery(controllerContext.getUser(), objectDescribe,
                    objectDescribe.getApiName(), query, false, false, true);
        } else {
            queryResult = serviceFacade.findBySearchQuery(controllerContext.getUser(), objectDescribe,
                    objectDescribe.getApiName(), query);
        }
        List<IObjectData> objectDataList = ObjectDataExt.synchronize(queryResult.getData());
        if (CollectionUtils.notEmpty(objectDataList)) {
            queryResult.setData(objectDataList);
        }
        return queryResult;
    }

    private List<IObjectData> findByIds(List<String> ids, String objectApiName) {
        return serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), ids, objectApiName);
    }

    private List<ILayout> findWhatListLayouts() {
        if (Strings.isNullOrEmpty(arg.getWhatApiName())) {
            return Lists.newArrayList();
        }
        List<ILayout> whatListLayout = serviceFacade.getLayoutLogicService().findWhatListLayout(buildLayoutContext(), objectDescribe, whatDescribe);

        //移除禁用和删除的字段
        removeWhatLayoutDisableFiled(whatListLayout, whatDescribe);
        return whatListLayout;
    }

    private List<ILayout> findWhatListLayouts(Collection<IObjectDescribe> whatDescribes) {
        List<ILayout> whatListLayouts = serviceFacade.getLayoutLogicService().findWhatListLayouts(buildLayoutContext(), objectDescribe, Lists.newArrayList(whatDescribes));
        stopWatch.lap("findWhatListLayouts");

        //移除禁用和删除的字段
        removeWhatListLayoutDisableFiled(whatListLayouts, whatDescribes);
        return whatListLayouts;
    }

    private void removeWhatListLayoutDisableFiled(List<ILayout> whatListLayouts, Collection<IObjectDescribe> whatDescribes) {
        whatDescribes.forEach(whatDescribe -> {
            List<ILayout> layoutList = whatListLayouts.stream()
                    .filter(whatLayout -> Objects.equals(whatLayout.getWhatApiName(), whatDescribe.getApiName()))
                    .collect(Collectors.toList());
            removeWhatLayoutDisableFiled(layoutList, whatDescribe);
            stopWatch.lap("removeWhatLayoutDisableFiled#" + whatDescribe.getApiName());
        });
    }

    private void removeWhatLayoutDisableFiled(List<ILayout> whatListLayouts, IObjectDescribe whatDescribe) {
        whatListLayouts.forEach(a -> WhatComponentRender.builder()
                .describeExt(ObjectDescribeExt.of(objectDescribe))
                .whatDescribeExt(ObjectDescribeExt.of(whatDescribe))
                .whatComponentExt(WhatComponentExt.of(LayoutExt.of(a).getWhatComponent().get()))
                .user(controllerContext.getUser())
                .functionPrivilegeService(serviceFacade)
                .build()
                .render());
    }

    @Override
    protected void fillInfo(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        batchConvertRichTextFieldFromMetadata(objectDescribe, dataList);
        //拼装组织机构信息
        super.fillInfo(objectDescribe, dataList);
        // 转化long类型的时间
        ObjectDataExt.formatTimeFieldToLong(objectDescribe, dataList);
    }

    private void batchConvertRichTextFieldFromMetadata(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        try {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
            Set<IFieldDescribe> richTextFields = Sets.newHashSet(describeExt.getRichTextFields());
            CollectionUtils.nullToEmpty(dataList)
                    .stream()
                    .map(ObjectDataExt::of)
                    .forEach(a -> a.convertRichTextFromMetadataWithField(richTextFields));

            Set<IFieldDescribe> cooperativeRichTextFields = Sets.newHashSet(describeExt.getCooperativeRichTextFields());
            if (AppFrameworkConfig.cooperativeRichTextGray(objectDescribe.getTenantId(), objectDescribe.getApiName())) {
                CollectionUtils.nullToEmpty(dataList)
                        .stream()
                        .map(ObjectDataExt::of)
                        .forEach(a -> a.convertCooperativeRichTextFromMetadataWithField(cooperativeRichTextFields));
            }
        } catch (AppBusinessException e) {
            log.warn("batchConvertRichTextFieldFromMetadata failed:{},tenantId:{},objectApiName:{},dataList:{}", e.getMessage(),
                    objectDescribe.getTenantId(), objectDescribe.getApiName(), ObjectDataExt.getDataIdName(dataList));
        } catch (Exception e) {
            log.error("batchConvertRichTextFieldFromMetadata error:{},tenantId:{},objectApiName:{},dataList:{}", e.getMessage(),
                    objectDescribe.getTenantId(), objectDescribe.getApiName(), ObjectDataExt.getDataIdName(dataList), e);
        }
    }

    @Override
    protected String getSearchQueryInfo() {
        return arg.getSearchQueryInfo();
    }

    @Override
    protected String getSearchTemplateId() {
        return arg.getSearchTemplateId();
    }

    @Override
    protected String getSearchTemplateApiName() {
        return arg.getSearchTemplateApiName();
    }

    @Override
    protected String getSearchTemplateType() {
        return null;
    }

    @Override
    protected boolean serializeEmpty() {
        return arg.serializeEmpty();
    }

    @Override
    protected boolean extractExtendInfo() {
        return arg.extractExtendInfo();
    }

    @Override
    protected Map<String, Integer> getDescribeVersionMap() {
        return null;
    }

    @Override
    protected String getUsePageType() {
        return null;
    }

    @Override
    protected Boolean getFindExplicitTotalNum() {
        if (RequestUtil.isCepRequest()) {
            return Boolean.TRUE.equals(arg.getFindExplicitTotalNum());
        }
        return arg.getFindExplicitTotalNum();
    }

    private Result buildResult(List<ILayout> layouts, SearchTemplateQuery query, QueryResult<IObjectData> queryResult) {
        List<String> cachedDescribe = arg.getCachedDescribe();

        Map<String, LayoutDocument> layoutMap = layouts.stream()
                .filter(it -> BooleanUtils.isTrue(it.isDefault()))
                .collect(Collectors.toMap(ILayout::getWhatApiName, LayoutDocument::of, (x, y) -> x));
        Result ret = Result.builder()
                .dataList(ObjectDataDocument.ofList(queryResult.getData()))
                .total(queryResult.getTotalNumber())
                .limit(query.getLimit())
                .offset(query.getOffset())
                .listLayoutMap(layoutMap)
                .layoutInfos(buildFlowTaskLayoutInfo(layouts))
                .extendInfo(buildExtendInfo())
                .build();
        if (arg.includeDescribe()) {
            objectDescribe.mergeWhatObjectFields(whatDescribe);
            Map<String, IObjectDescribe> describeMapExcuseCached = getDescribeMapExcuseCached(layouts, cachedDescribe);

            if (AppFrameworkConfig.isGrayWhatListDescribeCache(controllerContext.getTenantId(), objectDescribe.getApiName())) {

                Map<String, Integer> describeVersionMap = arg.getDescribeVersionMap();
                ObjectDescribeDocument objectDescribeDocument = ObjectDescribeDocument.handleDescribeCache(describeVersionMap, objectDescribe);
                if (Objects.nonNull(objectDescribeDocument)) {
                    ret.setObjectDescribe(objectDescribeDocument);
                }

                Map<String, ObjectDescribeDocument> documentMap = Maps.newHashMap();
                describeMapExcuseCached.forEach((describeApiName, describe) -> {
                    ObjectDescribeDocument describeDocument = ObjectDescribeDocument.handleDescribeCache(describeVersionMap, describe);
                    if (Objects.nonNull(describeDocument)) {
                        documentMap.put(describeApiName, describeDocument);
                    }
                });
                ret.setDescribeMap(documentMap);

            } else {
                ret.setObjectDescribe(ObjectDescribeDocument.of(objectDescribe));
                ret.setDescribeMap(ObjectDescribeDocument.ofMap(describeMapExcuseCached));
            }
        }

        return ret;
    }

    private List<RecordTypeLayoutStructure> buildFlowTaskLayoutInfo(List<ILayout> whatLayouts) {
        User user = controllerContext.getUser();
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FLOW_TASK_LAYOUT_GRAY, user.getTenantId()) || user.isOutUser()) {
            return null;
        }
        if (!isMobile()) {
            return Lists.newArrayList();
        }
        if (!Strings.isNullOrEmpty(arg.getWhatApiName())) {
            RecordTypeLayoutStructure flowTaskLayoutInfo = buildRecordTypeLayoutStructure(whatLayouts, whatDescribe);
            if (Objects.isNull(flowTaskLayoutInfo)) {
                return null;
            }
            return Lists.newArrayList(flowTaskLayoutInfo);
        }
        Map<String, List<ILayout>> layoutMap = whatLayouts.stream()
                .collect(Collectors.groupingBy(ILayout::getWhatApiName, Collectors.mapping(Function.identity(), Collectors.toList())));
        return objects.values().stream()
                .filter(iObjectDescribe -> !Objects.equals(iObjectDescribe.getApiName(), objectDescribe.getApiName()))
                .map(describe -> buildRecordTypeLayoutStructure(layoutMap.getOrDefault(describe.getApiName(), Collections.emptyList()), describe))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private boolean isMobile() {
        return getLayoutAgentType() == LayoutAgentType.MOBILE;
    }

    private RecordTypeLayoutStructure buildRecordTypeLayoutStructure(List<ILayout> whatLayouts, IObjectDescribe whatObjectDescribe) {
        stopWatch.lap("start buildRecordTypeLayoutStructure#" + whatObjectDescribe.getApiName());
        FlowTaskLayoutExt flowTaskLayoutExt = findFlowTaskLayoutExt(whatObjectDescribe);
        if (Objects.isNull(flowTaskLayoutExt)) {
            return null;
        }

        RecordTypeLayoutStructure layoutStructure = RecordTypeLayoutStructure.builder()
                .describeApiName(whatObjectDescribe.getApiName())
                .build();

        if (!isUserDefineMobileField(flowTaskLayoutExt)) {
            layoutStructure.setAbstractLayoutList(LayoutDocument.ofList(whatLayouts));
            Map<String, String> recordTypes = getMainRoleRecordLayoutMappingByRecordTypes(controllerContext.getUser(), whatObjectDescribe, whatLayouts);
            layoutStructure.setRecordLayoutMapping(recordTypes);
        } else {
            DocumentBaseEntity flowTaskLayoutComponent = flowTaskLayoutExt.getFlowTaskListMobileComponent()
                    .map(it -> new DocumentBaseEntity(it.toMap()))
                    .orElse(null);
            layoutStructure.setFlowTaskLayoutComponent(flowTaskLayoutComponent);
        }
        stopWatch.lap("end buildRecordTypeLayoutStructure#" + whatObjectDescribe.getApiName());
        return layoutStructure;
    }

    private boolean isUserDefineMobileField(FlowTaskLayoutExt flowTaskLayoutExt) {
        return flowTaskLayoutExt.getFlowTaskListMobileComponent()
                .map(FlowTaskListMobileComponentExt::mobileFieldTypeIsUserDefine)
                .orElse(false);
    }

    private String getDefaultRoleCode(User user) {
        return serviceFacade.getDefaultRoleCode(user).orElseGet(() -> {
            log.warn("no default role code, tenantId:{}, userId:{}", user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser());
            return "";
        });
    }

    private FlowTaskLayoutExt findFlowTaskLayoutExt(IObjectDescribe whatObjectDescribe) {
        ILayout layout = serviceFacade.getLayoutLogicService().getFlowTaskListLayoutWitchComponents(buildLayoutContext(), objectDescribe,
                whatObjectDescribe, PageType.List, getLayoutAgentType(), null);
        if (Objects.isNull(layout)) {
            return null;
        }
        return FlowTaskLayoutExt.of(layout);
    }

    private Map<String, String> getMainRoleRecordLayoutMappingByRecordTypes(User user, IObjectDescribe whatDescribe,
                                                                            List<ILayout> whatLayouts) {
        String defaultLayout = null;
        Set<String> layoutNames = Sets.newHashSet();
        for (ILayout layout : whatLayouts) {
            if (BooleanUtils.isTrue(layout.isDefault())) {
                defaultLayout = layout.getName();
            }
            layoutNames.add(layout.getName());
        }
        String defaultLayoutName = defaultLayout;

        String defaultRoleCode = getDefaultRoleCode(user);
        Set<String> recordTypes = Sets.newHashSet(ObjectDescribeExt.of(whatDescribe).getRecordTypeNames());

        String describeApiName = whatDescribe.getApiName();
        List<RoleViewPojo> defaultRoleViews = serviceFacade.batchFindRoleViewList(user, describeApiName, recordTypes,
                Sets.newHashSet(defaultRoleCode), Sets.newHashSet(FlowTaskLayoutExt.getRoleViewType(objectDescribe.getApiName(), LayoutTypes.WHAT_LIST)), controllerContext.getAppId());
        Map<String, String> resultMap = Optional.ofNullable(defaultRoleViews)
                .orElse(Lists.newArrayList()).stream()
                .filter(it -> layoutNames.contains(it.getViewId()))
                .collect(Collectors.toMap(RoleViewPojo::getRecordTypeId, RoleViewPojo::getViewId, (a, b) -> a));
        recordTypes.forEach(recordType -> resultMap.putIfAbsent(recordType, defaultLayoutName));
        return resultMap;
    }

    private Map<String, IObjectDescribe> getDescribeMapExcuseCached(List<ILayout> layouts, List<String> cachedDescribe) {
        if (CollectionUtils.empty(cachedDescribe)) {
            return objects;
        }
        layouts.removeIf(layout -> cachedDescribe.contains(layout.getWhatApiName()));
        return objects.entrySet().stream()
                .filter(entry -> !cachedDescribe.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private LayoutExt findWhatObjectLayout() {
        if (Strings.isNullOrEmpty(arg.getWhatApiName())) {
            return null;
        }
        if (Objects.isNull(whatDescribe)) {
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getWhatApiName());
            whatDescribe = ObjectDescribeExt.of(describe);
        }
        ILayout whatLayout = serviceFacade.getLayoutLogicService().findObjectLayoutWithType(buildLayoutContext(), "", whatDescribe, ILayout.LIST_LAYOUT_TYPE, null);
        return LayoutExt.of(whatLayout);
    }

    @Override
    protected void finallyDo() {
        // 是否选择替换 option 信息
        if (arg.isReplaceOptionValue() && Objects.nonNull(queryResult)) {
            // 格式化时间类型 和选项option
            convertFieldValue();
            stopWatch.lap("convertFieldForView");
        }
        super.finallyDo();
    }


    @Override
    protected Plugin.Arg buildAPLPluginArg(String method) {
        APLControllerPlugin.TriggerInfo triggerInfo = buildTriggerInfo();
        Arg controllerArg = Objects.isNull(arg) ? null : arg.copy2Plugin();
        BaseListController.Result controllerResult = Objects.isNull(result) ? null : result.copy2Plugin();
        return new APLControllerPlugin.Arg(controllerContext.getObjectApiName(), controllerArg, controllerResult, triggerInfo);
    }

    @Override
    protected String getSearchQueryInfoFromAplPlugin(APLControllerPlugin.Result aplPluginResult) {
        Arg controllerArg = aplPluginResult.getControllerArg(Arg.class);
        if (Objects.isNull(controllerArg)) {
            return null;
        }
        return controllerArg.getSearchQueryInfo();
    }

    @Override
    protected void setSearchTemplateQuery2Arg(SearchTemplateQuery query) {
        arg.setSearchQueryInfo(query.toJsonString());
    }

    @Override
    protected boolean isExtractFormatValue() {
        return BooleanUtils.isTrue(arg.getExtractFormatValue());
    }

    private void convertFieldValue() {
        if (CollectionUtils.empty(queryResult.getData())) {
            return;
        }
        // 入参有whatApiName的时候，此时描述不需要要加工
        if (!Strings.isNullOrEmpty(arg.getWhatApiName())) {
            convertFieldForView(controllerContext.getUser(), objectDescribe.getFieldDescribes(), queryResult.getData());
            return;
        }
        // 按数据中的what字段绑定的对象ApiName将数据进行分组
        Map<String, List<IObjectData>> dataMap = objectDescribe.getWhatField()
                .map(What::getApiNameFieldApiName)
                .map(fieldApiName -> queryResult.getData().stream().collect(Collectors.groupingBy(data -> data.get(fieldApiName, String.class))))
                .orElse(Collections.emptyMap());
        if (CollectionUtils.empty(dataMap) || CollectionUtils.empty(objects)) {
            return;
        }
        dataMap.forEach((apiName, dataList) -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe.copy());
            describeExt.mergeWhatObjectFields(objects.get(apiName));
            convertFieldForView(controllerContext.getUser(), describeExt.getFieldDescribes(), queryResult.getData());
        });
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        @JSONField(name = "M3")
        @JsonProperty("search_query_info")
        private String searchQueryInfo;

        @JSONField(name = "M4")
        @JsonProperty("object_describe_api_name")
        private String objectDescribeApiName;

        @JSONField(name = "M5")
        @JsonProperty("search_template_id")
        private String searchTemplateId;

        @JSONField(name = "M6")
        @JsonProperty("search_template_api_name")
        String searchTemplateApiName;

        @JSONField(name = "M10")
        @JsonProperty("what_api_name")
        private String whatApiName;

        @JSONField(name = "M11")
        @JsonProperty("cached_describe")
        private List<String> cachedDescribe;

        @JSONField(name = "M12")
        @JsonProperty("replace_option_value")
        @SerializedName("replace_option_value")
        boolean replaceOptionValue;

        @JSONField(name = "extract_format_value")
        @JsonProperty("extract_format_value")
        @SerializedName("extract_format_value")
        private Boolean extractFormatValue;

        @JSONField(name = "M13")
        @JsonProperty("find_explicit_total_num")
        @SerializedName("find_explicit_total_num")
        Boolean findExplicitTotalNum;

        /**
         * 是否序列化返回对象中的空值
         */
        private Boolean serializeEmpty;
        /**
         * 是否将人员部门字段的扩展信息提取到外层
         */
        private Boolean extractExtendInfo;

        @JSONField(name = "ignore_describe")
        @JsonProperty("ignore_describe")
        private Boolean ignoreDescribe;

        //端上缓存的describe的版本号
        private Map<String, Integer> describeVersionMap;

        //布局适用端(web或mobile)
        private String layoutAgentType;

        /**
         * 是否需要返回描述
         * <p>
         * null, true 需要返回描述
         * false 不需要返回描述
         *
         * @return true 需要返回描述, false不需要要返回描述
         */
        public boolean includeDescribe() {
            return BooleanUtils.isNotTrue(ignoreDescribe);
        }

        public boolean serializeEmpty() {
            return !Boolean.FALSE.equals(serializeEmpty);
        }

        public boolean extractExtendInfo() {
            return Boolean.TRUE.equals(extractExtendInfo);
        }

        public Arg copy2Plugin() {
            Arg arg = new Arg();
            arg.setSearchQueryInfo(searchQueryInfo);
            arg.setObjectDescribeApiName(objectDescribeApiName);
            arg.setWhatApiName(whatApiName);
            return arg;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result extends BaseListController.Result {

        @JSONField(name = "M7", serialize = false)
        private ObjectDescribeDocument objectDescribe;

        @JSONField(name = "M10")
        private Map<String, LayoutDocument> listLayoutMap;

        @JSONField(name = "M11")
        private Map<String, ObjectDescribeDocument> describeMap;

        private List<RecordTypeLayoutStructure> layoutInfos;

    }

    @Data
    @Builder
    public static class RecordTypeLayoutStructure {
        private String describeApiName;
        private List<LayoutDocument> abstractLayoutList;
        private Map<String, String> recordLayoutMapping;
        private DocumentBaseEntity flowTaskLayoutComponent;
    }
}
