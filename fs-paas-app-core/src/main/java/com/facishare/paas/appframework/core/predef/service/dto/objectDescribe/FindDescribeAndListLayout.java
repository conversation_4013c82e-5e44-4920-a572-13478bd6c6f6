package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * create by z<PERSON><PERSON> on 2020/11/16
 */
public interface FindDescribeAndListLayout {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        private String describeApiName;
        private String layoutApiName;
        private boolean noNeedReplaceI18n = false;
        private boolean removeI18n = false;
    }

    @Data
    @Builder
    class Result {
        private LayoutDocument layout;
        private ObjectDescribeDocument describeExtra;
        private ObjectDescribeDocument objectDescribe;
    }
}
