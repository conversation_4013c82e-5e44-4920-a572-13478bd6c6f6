package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> create by liy on 2024/6/13
 */
@Getter
@AllArgsConstructor
public enum PackagePluginAppType {

    ONLINE_DOC("OnlineDoc", "在线文档", "personal_auth.plugin.OnlineDoc"),
    ONLINE_MEETING("OnlineMeeting", "在线会议", "personal_auth.plugin.OnlineMeeting"),
    SCHEDULE_SYNC("ScheduleSync", "日程同步", "personal_auth.plugin.ScheduleSync"),
    ;

    private final String type;
    private final String name;
    private final String nameKey;
    private final static Map<String, PackagePluginAppType> TYPE_MAP = Maps.newHashMap();

    static {
        for (PackagePluginAppType item : PackagePluginAppType.values()) {
            TYPE_MAP.put(item.getType(), item);
        }
    }

    /**
     * 获取枚举
     */
    public static PackagePluginAppType of(String type) {
        return TYPE_MAP.get(type);
    }

    /**
     * 检查是否在枚举范围内
     */
    public static boolean in(String type) {
        return Objects.nonNull(TYPE_MAP.get(type));
    }
}
