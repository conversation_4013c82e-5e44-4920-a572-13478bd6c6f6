package com.facishare.paas.appframework.core.predef.service.dto.enterpriserelation;

import com.facishare.paas.appframework.privilege.dto.SupportEnterpriseRelationResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/08/19
 */
public interface SupportEnterpriseRelation {

    class Arg {
    }

    @Data
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    class Result {
        private Boolean support;
        @JsonProperty("support_team_member")
        private Boolean supportTeamMember;

        public static Result of(SupportEnterpriseRelationResult result) {
            return new Result(result.isSupportInterconnectBaseAppLicense(), result.isSupportTeamMember());
        }
    }
}
