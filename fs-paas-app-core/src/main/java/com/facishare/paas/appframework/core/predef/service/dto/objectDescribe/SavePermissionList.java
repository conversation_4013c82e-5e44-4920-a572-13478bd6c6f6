package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

public interface SavePermissionList {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private Boolean outPermission;
        private String roleType;
        private String describeApiName;
        private List<RolePermission> rolePermissionList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class RolePermission {
        private String objectDisplayName;
        private String objectApiName;
        private String targetRelatedListLabel;
        @Deprecated
        private boolean enable;
        private String fieldAPiName;
        private String type;

        public boolean isEnableRead(){
            //兼容历史参数
            if (enable){
                return true;
            }

            return Objects.equals(type,"read");
        }

        public boolean isEnableWrite(){
            return Objects.equals(type,"write");
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private boolean success;
    }

}
