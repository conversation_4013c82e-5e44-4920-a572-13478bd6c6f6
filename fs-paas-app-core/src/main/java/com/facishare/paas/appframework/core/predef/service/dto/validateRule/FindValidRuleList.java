package com.facishare.paas.appframework.core.predef.service.dto.validateRule;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface FindValidRuleList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        String describeApiName;

        @JSONField(name = "M1")
        String ruleName;
    }
    @Data
    class Result {
        @JSONField(name = "M2")
        private boolean success;

        @JSONField(name = "M8")
        private List ruleList;
    }
}
