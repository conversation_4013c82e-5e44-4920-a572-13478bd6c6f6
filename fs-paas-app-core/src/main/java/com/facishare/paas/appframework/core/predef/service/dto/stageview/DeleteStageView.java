package com.facishare.paas.appframework.core.predef.service.dto.stageview;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/02/05
 */
public interface DeleteStageView {
    @Data
    class Arg {
        @JsonProperty("describe_api_name")
        @JSONField(name = "describe_api_name")
        private String describeApiName;
    }

    @Data
    @AllArgsConstructor
    class Result {
        private boolean isSuccess;

        public static Result buildSuccess() {
            return new Result(true);
        }
    }
}
