package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.QueryTemplateDocument;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface GetOutTemplate {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        private String describeApiName;

        @JSONField(name = "M2")
        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        private String extendAttribute;
    }

    @Builder
    @Data
    class Result {
        @J<PERSON><PERSON>ield(name = "M1")
        private List<QueryTemplateDocument> templates;
    }
}
