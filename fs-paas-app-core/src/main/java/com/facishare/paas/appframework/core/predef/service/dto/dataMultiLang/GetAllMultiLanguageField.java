package com.facishare.paas.appframework.core.predef.service.dto.dataMultiLang;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface GetAllMultiLanguageField {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {
        @Builder.Default
        Boolean includeEnable = Boolean.TRUE;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        List<MultiLangInfo> multiLangInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class MultiLangInfo {
        String describeApiName;
        String displayName;
        List<FieldInfo> fieldInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class FieldInfo {
        String fieldApiName;
        String label;
    }
}
