package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface GetDataPermissionItems {

    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private List<DataPermissionGroupInfos> dataPermissionGroupInfos;
    }

    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class DataPermissionGroupInfos {
        private List<DataPermissionItemInfo> dataPermissionItemInfos;
        private String dataRoleName;
    }

    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class DataPermissionItemInfo {
        private String dataDisplayName;
        private String targetApiName;
        private String dataKey;
        private String fieldApiName;
        private String objectApiName;
        private String roleType;
        private Boolean isChecked;
        private Boolean isEditable;
    }

}
