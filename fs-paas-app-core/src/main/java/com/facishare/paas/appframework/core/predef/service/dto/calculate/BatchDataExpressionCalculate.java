package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface BatchDataExpressionCalculate {

    @Data
    class Arg {
        @JSONField(name = "M4")
        private String calculate_data_list;
    }
    @Data
    class Result {
        @JSONField(name = "M1")
        Map value_list;
    }
}
