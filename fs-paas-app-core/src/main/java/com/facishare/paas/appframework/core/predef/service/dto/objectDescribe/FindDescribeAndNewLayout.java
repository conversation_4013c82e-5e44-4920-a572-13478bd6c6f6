package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

public interface FindDescribeAndNewLayout {
    @Data
    class Arg {
        String describeApiName;
        String layoutApiName;
        boolean includeLayout = true;
    }

    @Builder
    @Data
    class Result {
        Object layout;
        Map objectDescribe;
    }
}
