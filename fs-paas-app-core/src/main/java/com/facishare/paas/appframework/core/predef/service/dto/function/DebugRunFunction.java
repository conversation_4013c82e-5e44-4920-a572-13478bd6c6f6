package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public interface DebugRunFunction {
    @Data
    class Arg {
        @JSONField(name = "api_name")
        @SerializedName(value = "api_name")
        @JsonProperty(value = "api_name")
        String apiName;

        @JSONField(name = "binding_object_api_name")
        @SerializedName(value = "binding_object_api_name")
        @JsonProperty(value = "binding_object_api_name")
        String bindingObjectAPIName;

        @JSONField(name = "input_data")
        @SerializedName(value = "input_data")
        @JsonProperty(value = "input_data")
        List<FunctionInfo.Parameter> inputData;

        @JSONField(name = "data_source")
        @SerializedName(value = "data_source")
        @JsonProperty(value = "data_source")
        String dataSource;

        FunctionInfo function;

        @JSONField(name = "token")
        @SerializedName(value = "token")
        @JsonProperty(value = "token")
        String token;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
        private Object runResult;
        private String logInfo;
        private String error;
        private String token;
    }
}
