package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/9/25
 */
public interface ValidateDefaultValue {
    @Data
    class Arg {
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
        private Set<String> masterCalculateFields;
        private Map<String, Map<String, Set<String>>> detailCalculateFields;
    }

    @Data
    @Builder
    class Result {
        private Map<String, Object> masterDataDiff;
        private Map<String, List<Map<String, Object>>> detailsDataDiff;
    }
}
