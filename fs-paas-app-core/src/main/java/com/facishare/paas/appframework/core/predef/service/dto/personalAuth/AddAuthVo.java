package com.facishare.paas.appframework.core.predef.service.dto.personalAuth;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/13
 */
public interface AddAuthVo {

    @Data
    class Arg {
        /**
         * 授权应用类型
         * @see PersonalAuthAppType
         */
        private String appType;
        /**
         * 插件apiName
         */
        private String pluginApiName;
        /**
         * 运行时数据
         */
        private Map<String, Object> runtimeData;
        /**
         * 过期时间戳，毫秒
         */
        private long expiredTime;
    }

    @Data
    class Result {
        private boolean success;
    }
}
