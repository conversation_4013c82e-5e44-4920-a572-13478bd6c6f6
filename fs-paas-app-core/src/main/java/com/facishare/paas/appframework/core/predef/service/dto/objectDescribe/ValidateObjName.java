package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface ValidateObjName {

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    class Arg {
        String name;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        String value;
    }

}
