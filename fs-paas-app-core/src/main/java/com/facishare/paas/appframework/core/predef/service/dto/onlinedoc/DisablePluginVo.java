package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR> create by liy on 2024/6/5
 */
public interface DisablePluginVo {
    @Data
    class Arg {
        /**
         * 插件apiName
         */
        private String pluginApiName;

        public void validate() {
            if (StringUtils.isBlank(pluginApiName)) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_PLUGIN_API_NAME));
            }
        }
    }

    @Data
    class Result {
        /**
         * 是否成功
         */
        private boolean success;
    }
}
