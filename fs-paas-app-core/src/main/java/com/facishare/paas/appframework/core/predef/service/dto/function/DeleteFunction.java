package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NoArgsConstructor;

public interface DeleteFunction {
    @Data
    class Arg {
        @JSONField(name = "api_name")
        @SerializedName(value = "api_name")
        @JsonProperty(value = "api_name")
        String apiName;

        @JSONField(name = "binding_object_api_name")
        @SerializedName(value = "binding_object_api_name")
        @JsonProperty(value = "binding_object_api_name")
        String bindingObjectAPIName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result{
        Boolean success;
    }

}
