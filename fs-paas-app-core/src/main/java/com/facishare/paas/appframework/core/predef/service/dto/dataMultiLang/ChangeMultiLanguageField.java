package com.facishare.paas.appframework.core.predef.service.dto.dataMultiLang;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface ChangeMultiLanguageField {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        List<MultiLanguageField> enableMultiLanguageInfo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class MultiLanguageField {
        String describeApiName;
        List<String> enableField;
    }

    @NoArgsConstructor
    @Builder
    class Result {

    }
}
