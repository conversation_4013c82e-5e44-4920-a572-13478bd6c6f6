package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface FindBizDescribeList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private boolean isDraft = false;
        @JSONField(name = "M2")
        private boolean isIncludeSystemObj = true;
        @JSONField(name = "M3")
        private boolean isIncludeFieldDescribe = false;
        @JSONField(name = "M4")
        private String packageName;
        @JSONField(name = "M5")
        private boolean isAsc = false;
        @JSONField(name = "M6")
        private boolean isIncludeUnActived = false;
        private boolean checkDetailObjectButton = false;
        private String sourceInfo;
        @JsonProperty("isConvertRule")
        @SerializedName("isConvertRule")
        private boolean isConvertRule;
        @JsonProperty("isArchiveRule")
        @SerializedName("isArchiveRule")
        private boolean isArchiveRule;
        private boolean onlyBigObject;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private List<ObjectDescribeDocument> objectDescribeList;

        private ManageGroupDTO manageGroup;

//        @JSONField(name = "M2")
//        private List<String> iconPathList;
    }
}
