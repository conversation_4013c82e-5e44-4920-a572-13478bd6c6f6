package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import com.facishare.paas.appframework.core.predef.service.dto.button.UpdateButtonUrl;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

public interface BatchUpdateButtonUrl {
    @Data
    class Arg {
        private UpdateButtonUrl.Arg buttonInfo;
        private Set<String> tenants;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<String> failTenants;
    }
}
