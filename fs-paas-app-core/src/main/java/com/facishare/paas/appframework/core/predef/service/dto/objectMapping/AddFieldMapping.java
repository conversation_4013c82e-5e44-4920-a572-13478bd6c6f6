package com.facishare.paas.appframework.core.predef.service.dto.objectMapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.MappingRuleDetailDocument;
import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.facishare.paas.metadata.api.IObjectMappingRuleDetailInfo;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface AddFieldMapping {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private Map<String,List<MappingRuleDetailDocument>> map;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        private MappingRuleDocument rule;
    }
}
