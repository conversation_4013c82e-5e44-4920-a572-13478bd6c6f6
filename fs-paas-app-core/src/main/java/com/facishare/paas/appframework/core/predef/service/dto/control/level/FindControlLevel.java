package com.facishare.paas.appframework.core.predef.service.dto.control.level;

import com.facishare.paas.appframework.metadata.config.ObjectControlLevelLogicService;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhaooju on 2024/5/22
 */
public interface FindControlLevel {

    @Data
    class Arg {
        private String resourceType;
        private String primaryKey;
        private String parentFieldValue;
    }

    @Data
    @Builder
    class Result {
        private List<ObjectControlLevelLogicService.ObjectControlLevelInfo> controlLevelInfos;
    }
}
