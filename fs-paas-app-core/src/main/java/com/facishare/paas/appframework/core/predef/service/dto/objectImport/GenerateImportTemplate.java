package com.facishare.paas.appframework.core.predef.service.dto.objectImport;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface GenerateImportTemplate {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String describeApiName;

        @JSONField(name = "M2")
        @JsonProperty(value = "ImportType")
        @SerializedName(value = "ImportType")
        private Integer importType = 0;
    }
    @Data
    class Result {

    }
}
