package com.facishare.paas.appframework.core.predef.service.dto.publicobject;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.publicobject.module.EnterpriseRelationQueryParam;
import com.facishare.paas.appframework.metadata.publicobject.module.EnterpriseRelationResult;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhaooju on 2023/12/25
 */
public interface FindEnterpriseRelation {

    @Data
    class Arg {
        private String objectApiName;
        private EnterpriseRelationQueryParam queryParam;
        private String type;
    }

    @Data
    @Builder
    class Result {
        private Integer totalNumber;
        private List<ObjectDataDocument> dataList;
        private ObjectDescribeDocument describe;
        private List<EnterpriseRelationResult.OperateInfo> operateInfoList;

        public static Result from(EnterpriseRelationResult result) {
            return Result.builder()
                    .totalNumber(result.getTotalNumber())
                    .dataList(ObjectDataDocument.ofList(result.getDataList()))
                    .describe(ObjectDescribeDocument.of(result.getDescribe()))
                    .operateInfoList(result.getOperateInfoList())
                    .build();
        }
    }
}
