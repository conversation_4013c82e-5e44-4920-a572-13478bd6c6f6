package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface FindRecordInfo {

    @Data
    class Arg{
        @JSONField(name = "M1")
        String describeApiName;
        @JSONField(name = "M2")
        String recordApiName;
        boolean includeRemark;
        boolean noNeedReplaceI18n = false;

    }

    @Data
    class Result{
        @JSONField(name = "M1")
        private Map recordTypeOption;
        private String remark;
        @JsonProperty("i18nInfoList")
        private List<I18nInfo> i18nInfoList;
    }
}
