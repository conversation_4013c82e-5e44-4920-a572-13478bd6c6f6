package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface DataDuplicatedByRedis {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        String describeApiName;
        List<ObjectDataDocument> objectDataList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        List<DuplicateSearchDataInfo> duplicatedInfos;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class DuplicatedInfo {
        IObjectData objectData;
        List<String> duplicatedIds;
    }
}
