package com.facishare.paas.appframework.core.predef.service.dto.uniqueRule;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

public interface SaveUniqueRule {

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private IUniqueRule uniqueRule;
    }

    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private IUniqueRule uniqueRule;
    }
}
