package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

public interface SaveLayoutAssign {

    @Data
    class Arg {
        @JSONField(name = "M1")
        String describeApiName;

        @J<PERSON>NField(name = "M2")
        String role_list;

        String layoutType;
        private String whatDescribeApiName;

        private String sourceInfo;

        /**
         * 应用Id
         */
        private String appId;
    }

    @Data
    class Result {
        @JSONField(name = "M2")
        private boolean success;
    }
}
