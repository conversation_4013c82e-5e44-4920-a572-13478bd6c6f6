package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import lombok.Data;

import java.util.List;

public interface UpdateOptionalFeaturesSwitch {

    @Data
    class Arg {
        List<String> describeApiNames;
        Boolean isModifyRecordEnabled;
        Boolean isModifyRelatedTeamEnabled;
        Boolean crossObjectFilterButton;
    }

    @Data
    class Result {

    }

}
