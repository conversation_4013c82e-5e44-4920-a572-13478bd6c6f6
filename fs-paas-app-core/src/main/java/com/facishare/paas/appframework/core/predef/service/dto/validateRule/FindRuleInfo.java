package com.facishare.paas.appframework.core.predef.service.dto.validateRule;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface FindRuleInfo {
    @Data
    class Arg {
        @JSONField(name = "M1")
        String describeApiName;

        @JSONField(name = "M2")
        String ruleApiName;
    }
    @Data
    class Result {
        @JSONField(name = "M1")
        private Map rule;

        @JSONField(name = "M2")
        private boolean success;
    }
}
