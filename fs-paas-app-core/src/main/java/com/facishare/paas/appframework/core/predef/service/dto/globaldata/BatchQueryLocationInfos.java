package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.MetaDataGlobalService;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public interface BatchQueryLocationInfos {

    @Data
    class Arg {
        //地区类型：支持省，市，区,镇
        private String type;
        //当前地区代码
        private Set<String> codes;
    }

    @Data
    @Builder
    class Result {
        private List<Map<String, String>> locationInfos;

        public static Result of(List<MetaDataGlobalService.CountryInfo> countryInfos) {
            if (CollectionUtils.empty(countryInfos)) {
                return Result.builder().build();
            }
            List<Map<String, String>> collect = countryInfos.stream()
                    .map(MetaDataGlobalService.CountryInfo::toMap)
                    .collect(Collectors.toList());
            return Result.builder().locationInfos(collect).build();
        }
    }
}
