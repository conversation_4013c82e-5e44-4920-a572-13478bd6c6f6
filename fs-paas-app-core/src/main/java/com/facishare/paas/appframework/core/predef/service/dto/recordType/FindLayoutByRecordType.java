package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

public interface FindLayoutByRecordType {

    @Data
    class Arg{
        @JSONField(name = "M1")
        String describeApiName;
        @JSONField(name = "M2")
        String recordApiName;
        String layoutType;
    }

    @Data
    class Result{
        @JSONField(name = "M5")
        private Map layout;

        @JSONField(name = "M9")
        private Map objectDescribe;
    }
}
