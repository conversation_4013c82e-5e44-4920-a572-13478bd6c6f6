package com.facishare.paas.appframework.core.predef.service.dto.option.dependence;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.options.bo.OptionDependence;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/8/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OptionDependenceDTO {
    private String value;
    private List<String> childOptions;

    public static OptionDependenceDTO from(OptionDependence optionDependence) {
        return OptionDependenceDTO.builder()
                .value(optionDependence.getValue())
                .childOptions(optionDependence.getChildOptions())
                .build();
    }

    public static List<OptionDependenceDTO> fromList(List<OptionDependence> optionDependenceList) {
        if (CollectionUtils.empty(optionDependenceList)) {
            return Collections.emptyList();
        }
        return optionDependenceList.stream()
                .map(OptionDependenceDTO::from)
                .collect(Collectors.toList());
    }

    public OptionDependence convert() {
        return new OptionDependence(value, childOptions);
    }
}
