package com.facishare.paas.appframework.core.predef.service.dto.publicobject;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.EnterpriseRelationSimpleInfo;
import com.facishare.paas.appframework.metadata.publicobject.module.InternationalVerifyMessage;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobParamVerifyInfo;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobVerifyResult;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhaooju on 2023/12/22
 */
public interface VerifyJob {
    @Data
    class Arg {
        private String objectApiName;
        private String jobType;
        private String upstreamTenantId;
        private String jobParam;

        private PublicObjectJobParamDTO getJobParamDTO() {
            return PublicObjectJobParamDTO.fromJsonString(jobParam);
        }

        public PublicObjectJobParamVerifyInfo toPublicObjectJobParamVerifyInfo() {
            PublicObjectJobParamDTO jobParamDTO = getJobParamDTO();
            if (!checkParams(jobParamDTO)) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            return PublicObjectJobParamVerifyInfo.fromPublicObjectJobParamDTO(jobParamDTO, upstreamTenantId);
        }

        private boolean checkParams(PublicObjectJobParamDTO jobParamDTO) {
            if (Objects.equals(jobParamDTO.getObjectApiName(), objectApiName) && Objects.equals(jobParamDTO.getJobType(), jobType)) {
                return true;
            }
            return false;
        }
    }

    @Data
    class Result {
        private List<String> messages;
        private List<InternationalVerifyMessage> internationalVerifyMessages;

        private ObjectDescribeDocument describe;

        private List<EnterpriseRelationSimpleInfo> enterpriseRelations;

        public boolean isSuccess() {
            return CollectionUtils.empty(messages);
        }

        public static Result fromVerifyResult(PublicObjectJobVerifyResult verifyResult) {
            Result result = new Result();
            result.setMessages(verifyResult.getMessageList());
            result.setInternationalVerifyMessages(verifyResult.getVerifyMessages());
            result.setDescribe(ObjectDescribeDocument.of(verifyResult.getDescribe()));
            result.setEnterpriseRelations(verifyResult.getEnterpriseRelationSimpleInfos());
            return result;
        }
    }

}
