package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface SetDuplicateRulePriority {
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class Arg {
        @JsonProperty("describe_api_name")
        String describeApiName;
        @JsonProperty("duplicate_rule_api_names")
        List<String> duplicateRuleApiNames;
        IDuplicatedSearch.Type type;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        Boolean result;
    }


}
