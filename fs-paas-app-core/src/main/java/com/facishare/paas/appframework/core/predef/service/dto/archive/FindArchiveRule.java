package com.facishare.paas.appframework.core.predef.service.dto.archive;

import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface FindArchiveRule {
    @Data
    class Arg {
        @SerializedName("rule_name")
        @JsonProperty("rule_name")
        private String ruleName;
        @SerializedName("rule_api_name")
        @JsonProperty("rule_api_name")
        private String ruleApiName;
    }

    @Data
    @Builder
    class Result {
        @SerializedName("rule_list")
        @JsonProperty("rule_list")
        private List<MappingRuleDocument> ruleList;
    }
}
