package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege.TemporaryPrivilege.ConfigInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2018/10/31
 */
public interface QueryTemporaryPrivilege {
    @Data
    class Arg {
        private String apiName;
    }

    @Data
    @Builder
    class Result {
        @Getter(onMethod = @__({@JsonProperty("configs")}))
        private List<ConfigInfo> ConfigInfoList;
        private String message;
        private boolean success;
    }
}
