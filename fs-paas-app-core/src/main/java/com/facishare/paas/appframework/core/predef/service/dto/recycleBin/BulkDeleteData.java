package com.facishare.paas.appframework.core.predef.service.dto.recycleBin;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface BulkDeleteData {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        String objectDescribeAPIName;

        @J<PERSON><PERSON>ield(name = "M2")
        List<String> idList;


        public List<IObjectData> buildDeleteObjectDataParameter(ServiceContext context){
            return idList.stream().map(x->buildObjectData(context.getTenantId(),objectDescribeAPIName,x))
                    .collect(Collectors.toList());
        }

        private IObjectData buildObjectData(String tenantId,String objectDescribeAPIName,String id){
            IObjectData data = new ObjectData();
            data.setId(id);
            data.setTenantId(tenantId);
            data.setDescribeApiName(objectDescribeAPIName);
            return data;
        }
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private List<ObjectDataDocument> dataList;
        @JSONField(name = "M2")
        private Boolean success;
        @JSONField(name = "M3")
        private Integer pageCount;
        @JSONField(name = "M4")
        private Integer pageNumber;
        @JSONField(name = "M5")
        private Integer pageSize;
        @JSONField(name = "M6")
        private Integer totalCount;
    }
}
