package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import lombok.Builder;
import lombok.Data;

public interface ConsumeAndReturnMapQuota {
    @Data
    class Arg {
        private String bizSource;
        private String bizKey;
        private String moduleKey;
        private String paraKey;
        private int consumeCount;
        private String apiName;
        private String dataId;
    }

    @Data
    @Builder
    class Result {
        //private Long quotaLeft;
        private boolean consumeSuccess;
    }
}
