package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.Builder;
import lombok.Data;
import java.util.Map;

public interface CreateOrUpdateDescribe {
    @Data
    class Arg {
        private ObjectDescribeDocument describeJson;
        private String describeApiName;
        private ObjectDescribeDocument describeAttribute;
        /**
         * 对象附加属性
         */
        private ObjectDescribeDocument describeExtraAttribute;
        private Map<String, Map<String, Object>> fields;
        /**
         * 字段扩展属性
         */
        private Map<String, Map<String, Object>> fieldExtraAttribute;
        private String tenantId;
    }

    @Builder
    @Data
    class Result {

    }
}
