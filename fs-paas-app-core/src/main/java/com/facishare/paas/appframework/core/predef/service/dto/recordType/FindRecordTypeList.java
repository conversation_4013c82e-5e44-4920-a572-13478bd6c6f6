package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface FindRecordTypeList {

    @Data
    class Arg {
        @JSONField(name = "M1")
        String describeApiName;
        @JSONField(name = "M2")
        Boolean is_only_active = false;
        boolean includeRemark;
    }

    @Data
    class Result {
        @JSONField(name = "M8")
        private List record_list;
        @JsonProperty("option_remark")
        @JSONField(name = "option_remark")
        private List<Map<String, Object>> optionRemark;
    }

}
