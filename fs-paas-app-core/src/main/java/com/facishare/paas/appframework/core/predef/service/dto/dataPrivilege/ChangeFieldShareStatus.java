package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface ChangeFieldShareStatus {
    @Data
    class Arg {
        @JsonProperty("ruleCodes")
        @SerializedName("ruleCodes")
        @JSONField(name = "M1")
        List<String> ruleCodes;

        @JsonProperty("status")
        @SerializedName("status")
        @JSONField(name = "M3")
        int status;
    }
    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        boolean success;
    }
}
