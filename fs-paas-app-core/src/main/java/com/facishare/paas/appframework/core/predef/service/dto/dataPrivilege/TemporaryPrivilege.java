package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.privilege.dto.TemporaryRights.RuleConfig;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.util.List;
import java.util.Objects;

import static com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege.TemporaryPrivilegeSwitchConfig.MAX_VALIDITY_TERM;

/**
 * create by z<PERSON><PERSON> on 2018/10/31
 */
public interface TemporaryPrivilege {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        /**
         * 对象ApiName
         */
        private String apiName;
        /**
         * 权限收回方式
         */
        private String recyclingWay;
        /**
         * 自定义授权有效期
         */
        private Integer validityTerm;
        /**
         * 权限类型
         */
        private Integer level;

        public TemporaryPrivilegeSwitchConfig convertTo() {
            // 当validityTerm大于最大值时，使用最大值
            if (Objects.nonNull(validityTerm) && validityTerm > MAX_VALIDITY_TERM) {
                validityTerm = MAX_VALIDITY_TERM;
            }
            return TemporaryPrivilegeSwitchConfig.builder()
                    .confSwitch(true)
                    .level(this.level)
                    .validityTerm(this.validityTerm)
                    .recyclingWay(this.recyclingWay)
                    .build();
        }

        public RuleConfig convertToRuleConfig() {
            return RuleConfig.builder()
                    .level(getLevel())
                    .withdrawalWay(getRecyclingWay())
                    .validityTerm(getValidityTerm())
                    .build();
        }
    }

    @Data
    @Builder
    class Result {
        @Getter(onMethod = @__({@JsonProperty("config")}))
        private ConfigInfo configResult;
        private String message;
        private boolean success;
    }

    @Data
    class AvailableObjectArg {
        @SerializedName("sourceInfo")
        @JsonProperty("sourceInfo")
        private String sourceInfo;

    }

    @Data
    @Builder
    class AvailableObjectResult {
        @Getter(onMethod = @__({@JsonProperty("objects")}))
        private List<ObjectDescribeDocument> objectDescribeList;
        private String message;
        private boolean success;
        private int total;
    }

    @Data
    @Builder
    class BatchCreateResult {
        private boolean success;
        private String message;
        private List<String> errorList;
    }

    @Data
    @Builder
    class ConfigInfo {
        /**
         * 对象名称
         */
        private String displayName;
        /**
         * 对象ApiName
         */
        private String apiName;
        /**
         * 状态
         */
        private Boolean confSwitch;
        /**
         * 权限类型
         */
        private Integer level;
        /**
         * 权限收回方式
         */
        private String recyclingWay;
        /**
         * 自定义授权有效期
         */
        private Integer validityTerm;

        public static ConfigInfo convert(TemporaryPrivilegeSwitchConfig configResult) {
            return ConfigInfo.builder()
                    .confSwitch(configResult.getConfSwitch())
                    .level(configResult.getLevel())
                    .recyclingWay(configResult.getRecyclingWay())
                    .validityTerm(configResult.getValidityTerm())
                    .build();
        }

        public RuleConfig convertTo() {
            return RuleConfig.builder()
                    .level(getLevel())
                    .withdrawalWay(getRecyclingWay())
                    .validityTerm(getValidityTerm())
                    .build();
        }
    }
}
