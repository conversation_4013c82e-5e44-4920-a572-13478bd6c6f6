package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

public interface CheckStoredFormulaFields {
    @Data
    class Arg {
        private String objectApiName;
        private String excludeObjectApiName;
        private List<String> includeType;
    }

    @Data
    @AllArgsConstructor
    class Result {
        private boolean success;
    }
}
