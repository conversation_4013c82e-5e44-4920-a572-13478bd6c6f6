package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.RelationMemberDataPermission.ObjectRelated.*;

public class RelationMemberDataPermission {

    public static List<ObjectRelated> objectRelatedList = Lists.newArrayList(CONTACT_LOOKUP_ACCOUNT, OPPORTUNITY_LOOKUP_ACCOUNT,
            NEW_OPPORTUNITY_LOOKUP_ACCOUNT, SALES_ORDER_LOOKUP_ACCOUNT, CONTRACT_LOOKUP_ACCOUNT, PAYMENT_LOOKUP_ACCOUNT,
            REFUND_LOOKUP_ACCOUNT, INVOICE_APPLICATION_LOOKUP_ACCOUNT, CONTACT_LOOKUP_OPPORTUNITY, SALES_ORDER_LOOKUP_OPPORTUNITY);

    public static List<String> apiNames;

    public enum ObjectRelated {

        CONTACT_LOOKUP_ACCOUNT(Utils.CONTACT_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("1", "2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        OPPORTUNITY_LOOKUP_ACCOUNT(Utils.OPPORTUNITY_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("1", "2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        NEW_OPPORTUNITY_LOOKUP_ACCOUNT(Utils.NEW_OPPORTUNITY_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("1", "2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        SALES_ORDER_LOOKUP_ACCOUNT(Utils.SALES_ORDER_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("1", "2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        CONTRACT_LOOKUP_ACCOUNT(Utils.CONTRACT_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("1", "2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        //        VISITING_LOOKUP_ACCOUNT(Utils.VISITING_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("1", "2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        PAYMENT_LOOKUP_ACCOUNT(Utils.CUSTOMER_PAYMENT_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("1", "2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        REFUND_LOOKUP_ACCOUNT(Utils.REFUND_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("1", "2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        INVOICE_APPLICATION_LOOKUP_ACCOUNT(Utils.INVOICE_APPLICATION_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("1", "2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        // CONTACT_LOOKUP_ACCOUNT(Utils.CONTACT_API_NAME, Utils.ACCOUNT_API_NAME, "account_id", Lists.newArrayList("2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        CONTACT_LOOKUP_OPPORTUNITY(Utils.CONTACT_API_NAME, Utils.OPPORTUNITY_API_NAME, "opportunity_id", Lists.newArrayList("2", "3"), I18NKey.VIEW_RELATION_OBJECT),
        SALES_ORDER_LOOKUP_OPPORTUNITY(Utils.SALES_ORDER_API_NAME, Utils.OPPORTUNITY_API_NAME, "opportunity_id", Lists.newArrayList("2", "3"), I18NKey.VIEW_RELATION_OBJECT);
        // CONTACT_LOOKUP_OPPORTUNITY(Utils.CONTACT_API_NAME, Utils.OPPORTUNITY_API_NAME, "account_id", Lists.newArrayList("2", "3"), I18NKey.VIEW_RELATION_OBJECT);

        private String objectApiName;
        private String targetObjectApiName;
        private String filedAPiName;
        private List<String> roleType;
        private String displayName;

        ObjectRelated(String objectApiName, String targetObjectApiName, String filedAPiName, List<String> roleType, String displayName) {
            this.objectApiName = objectApiName;
            this.targetObjectApiName = targetObjectApiName;
            this.filedAPiName = filedAPiName;
            this.roleType = roleType;
            this.displayName = displayName;
        }

        public String getObjectApiName() {
            return objectApiName;
        }

        public String getTargetObjectApiName() {
            return targetObjectApiName;
        }

        public String getFiledAPiName() {
            return filedAPiName;
        }

        public String getDisplayName() {
            return displayName;
        }

        public List<String> getRoleType() {
            return roleType;
        }

        static {
            apiNames = Stream.of(values()).map(ObjectRelated::getObjectApiName).collect(Collectors.toList());
        }
    }
}
