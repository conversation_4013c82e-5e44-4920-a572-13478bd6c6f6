package com.facishare.paas.appframework.core.predef.service.dto.multiCurrency;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface FindExchangeRateByCurrency {

    @Data
    class Arg {
        private String fromCurrencyCode;
        private String toCurrencyCode;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        private String exchangeRate;
    }

}
