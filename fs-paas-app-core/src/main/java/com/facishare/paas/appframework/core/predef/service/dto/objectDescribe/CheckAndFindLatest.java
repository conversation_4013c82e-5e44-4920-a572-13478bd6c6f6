package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.Builder;
import lombok.Data;

/**
 * Created by zhouwr on 2017/11/8
 */
public interface CheckAndFindLatest {
    @Data
    class Arg {
        @J<PERSON>NField(name = "M1")
        private String describeAPIName;

        @J<PERSON>NField(name = "M2")
        private int revision;
    }

    @Data
    @Builder
    class Result {
        private int errorCode;
        private String errorMessage;
        private ObjectDescribeDocument result;
    }

    interface Status {
        int CODE_OK = 0;
        int CODE_NOT_FOUND = 1;
        int CODE_ERROR = 2;
        int CODE_DATA_NOT_CHANGE = 3;
        int CODE_DATA_EXPIRED = 4;

        String MESSAGE_OK = "OK";
        String MESSAGE_ERROR = "ERROR";
        String MESSAGE_NOT_FOUND = "NOT_FOUND::objectDescribe is not found";
        String MESSAGE_PARAMETER_IS_NULL = "PARAMETER_IS_NULL";
        String MESSAGE_DUPLICATE_TRIGGER_API_NAME = "DUPLICATE_TRIGGER_API_NAME";
    }

}
