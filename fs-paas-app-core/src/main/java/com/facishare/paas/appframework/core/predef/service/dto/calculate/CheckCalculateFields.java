package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2018/11/27
 */
public interface CheckCalculateFields {

    @Data
    class Arg {
        private String objectApiName;
        private List<Map<String, Object>> fieldList;
    }

    @Data
    @AllArgsConstructor
    class Result {
        private boolean success;
    }
}
