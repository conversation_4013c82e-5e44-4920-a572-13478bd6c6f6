package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

public interface GetZoneByFsUniqueCodes {
    @Data
    class Arg {
        private List<String> fsUniqueCodes;
    }
    @Data
    @Builder
    class Result {
        List<ZoneInfo> optionList;
    }

    @Data
    @Builder
    class ZoneInfo {
        String value;
        String label;
        String type;
        String parentValue;
        String standardCode;

        public static List<GetZoneByFsUniqueCodes.ZoneInfo> fromCountryArea(List<CountryAreaService.CountryAreaDTO> list) {
            if(CollectionUtils.empty(list)) {
                return Lists.newArrayList();
            }

            return list.stream().map(a-> GetZoneByFsUniqueCodes.ZoneInfo.builder()
                    .value(a.getFsUniqueCode())
                    .label(a.getName())
                    .type(a.getType())
                    .parentValue(a.getParentFsUniqueCode())
                    .standardCode(a.getStandardCode())
                    .build()).collect(Collectors.toList());
        }
    }
}
