package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public interface QueryAreaInfo {
    @Data
    class Arg {
        //搜索关键字
        private String keyword;
        private String parentPath;
        private Integer level;

    }

    @Data
    @Builder
    class Result {
        List<QueryAreaInfo.AreaInfo> optionList;
    }

    @Data
    @Builder
    class AreaInfo {
        String value;
        String label;
        String type;
        List<String> parentCodes;
        List<String> parentLabels;

        public static List<QueryAreaInfo.AreaInfo> fromCountryArea(List<CountryAreaService.CountryAreaDTO> list) {
            if(CollectionUtils.empty(list)) {
                return Lists.newArrayList();
            }

            return list.stream().map(a-> {
                List<String> codeList = Lists.newArrayList();
                List<String> labelList = Lists.newArrayList();
                boolean existRootNode = false;
                if(!StringUtils.isBlank(a.getBreadcrumbCode())) {
                    codeList = Lists.newArrayList(StringUtils.split(a.getBreadcrumbCode(), '/'));
                    existRootNode = codeList.remove("ROOT");
                }
                if(!StringUtils.isBlank(a.getBreadcrumbName())) {
                    labelList = Lists.newArrayList(StringUtils.split(a.getBreadcrumbName(), '/'));
                    if(existRootNode) {
                        labelList.remove(0);
                    }
                }

                return AreaInfo.builder()
                        .value(a.getFsUniqueCode())
                        .label(a.getName()) // 需要修改
                        .type(a.getType())
                        .parentCodes(codeList)
                        .parentLabels(labelList)
                        .build();
            }).collect(Collectors.toList());
        }
    }
}
