package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface BulkFindRecordTypeList {

    @Data
    class Arg{
        @J<PERSON>NField(name = "M1")
        List<String> describeApiNameList;
    }

    @Data
    class Result{
        @JSONField(name = "M1")
        private List<Item> recordTypeMap;
    }

    @Data
    @Builder
    class Item {
        @JSONField(name = "apiName")
        private String apiName;
        @JSONField(name = "displayName")
        private String displayName;
        @JSONField(name = "recordType")
        private List<Map> recordType;
    }
}
