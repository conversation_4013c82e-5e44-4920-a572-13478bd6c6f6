package com.facishare.paas.appframework.core.predef.service.dto.enterpriserelation;

import com.facishare.paas.appframework.core.model.User;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/08/17
 */
public interface GetRelationDownstreamInfo {
    @Data
    class Arg {
        private String describeApiName;
        private String dataId;
    }

    @Data
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    class Result {
        private String userId;
        private String userName;
        private String outerTenantId;

        public static Result of(User user) {
            if (Objects.isNull(user)) {
                return new Result();
            }
            return new Result(user.isOutUser() ? user.getOutUserId() : user.getUserId(),
                    user.getUserName(), user.getOutTenantId());
        }
    }
}
