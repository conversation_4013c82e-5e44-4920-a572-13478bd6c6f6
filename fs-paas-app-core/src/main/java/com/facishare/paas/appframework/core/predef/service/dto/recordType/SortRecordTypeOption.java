package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/14
 */
public interface SortRecordTypeOption {

    @Data
    class Arg {
        @JsonProperty("describe_api_name")
        private String describeApiName;
        @JsonProperty("sorted_record_type_option")
        private List<String> sortedRecordTypeOption;
    }

    class Result {

    }
}
