package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.facishare.paas.appframework.metadata.expression.CalculateDataContext;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;


public interface BulkDataCalculateWithExpression {

    @Data
    class Arg {
        private String objectApiName;
        private List<CalculateDataContext> dataCtx;
        private List<SimpleExpression> expressionList;
    }

    @Data
    @AllArgsConstructor
    class Result {
        private Map<String, Map<String, Object>> calcResult;
    }
}
