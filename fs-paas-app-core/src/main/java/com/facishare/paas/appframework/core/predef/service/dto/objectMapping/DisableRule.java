package com.facishare.paas.appframework.core.predef.service.dto.objectMapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;


public interface DisableRule {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("rule_api_name")
        @SerializedName("rule_api_name")
        private String ruleApiName;

        @JSONField(name = "M2")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        private String describeApiName;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        @JsonProperty("isSuccess")
        @SerializedName("isSuccess")
        private Boolean isSuccess;
    }
}
