package com.facishare.paas.appframework.core.predef.service.dto.multiCurrency;

import com.facishare.paas.appframework.metadata.MtExchangeRate;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface QueryRate {

    @Data
    class Arg {
        private String currencyCode;
        private Long startTime;
        private Long endTime;
        private int pageNumber;
        private int pageSize;
    }

    @Data
    @Builder
    class Result {
        private List<MtExchangeRate> exchangeRateList;
        private int pageCount;
        private int pageNumber;
        private int pageSize;
        private int totalCount;
    }
}
