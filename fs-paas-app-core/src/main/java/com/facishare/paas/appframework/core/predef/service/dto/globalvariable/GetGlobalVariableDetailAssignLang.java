package com.facishare.paas.appframework.core.predef.service.dto.globalvariable;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

public interface GetGlobalVariableDetailAssignLang {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JSONField(name = "M1")
        private String apiName;

        @JSONField(name = "M2")
        private Boolean realTimeTrans;

        @JSONField(name = "M3")
        private String lang;

    }
}
