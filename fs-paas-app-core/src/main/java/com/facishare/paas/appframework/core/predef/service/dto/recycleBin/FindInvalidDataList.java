package com.facishare.paas.appframework.core.predef.service.dto.recycleBin;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Order;
import com.facishare.paas.metadata.api.condition.IConditions;
import com.facishare.paas.metadata.api.condition.MatchConditions;
import com.facishare.paas.metadata.api.condition.TermConditions;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.arg.FindInvalidDataListArg;
import com.facishare.paas.metadata.impl.search.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface FindInvalidDataList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        //兼容参数格式
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        String objectDescribeAPIName;
        @JSONField(name = "M2")
        String name;
        @JSONField(name = "M3")
        String invalidOperator;
        @JSONField(name = "M4")
        Long startDate;
        @JSONField(name = "M5")
        Long endDate;
        @JSONField(name = "M6")
        int pageSize = 20; //default
        @JSONField(name = "M7")
        int pageNumber = 1; //default
        @JSONField(name = "M8")
        @JsonProperty("SortField")
        String SortField;
        @JSONField(name = "M9")
        @JsonProperty("SortType")
        int SortType;

        @JsonProperty("find_explicit_total_num")
        Boolean findExplicitTotalNum;

        public SearchQuery buildSearchQuery() {
            SearchQuery searchQuery = new SearchQuery();
            String argName = this.getName();
            String argInvalidOperator = this.getInvalidOperator();
            Integer pageNumber = this.getPageNumber();
            Integer pageSize = this.getPageSize();
            Integer offset = (pageNumber - 1) * pageSize;
            Integer limit = pageSize;
            searchQuery.setOffset(offset);
            searchQuery.setLimit(limit);
            boolean ascending = false;
            if (this.getSortType() == 1) {
                ascending = true;
            }
            Order order = new Order(this.getSortField(), ascending);

            List<Order> orders = Lists.newArrayList();
            orders.add(order);
            searchQuery.setOrders(orders);
            MatchConditions conditions = new MatchConditions();
            if (StringUtils.isNotBlank(argName)) {
                conditions.addCondition(IObjectData.NAME, argName);
            }
            String statusField = ObjectAPINameMapping.getStatusFieldAPIName(objectDescribeAPIName);
            conditions.addCondition(statusField, ObjectDataExt.SFA_STATUS_INVALID);

            List<IConditions> iConditions = new ArrayList<>();
            iConditions.add(conditions);

            //操作人只能使用等于操作符
            if (StringUtils.isNotBlank(argInvalidOperator)) {
                TermConditions termConditions = new TermConditions();
                termConditions.addCondition(IObjectData.LAST_MODIFIED_BY, argInvalidOperator);
                searchQuery.addCondition(termConditions);
            }

            searchQuery.addConditions(iConditions);
            searchQuery.addRangeConditions(IObjectData.LAST_MODIFIED_TIME, this.getStartDate(), this.getEndDate());

            return searchQuery;
        }

        public SearchTemplateQuery buildSearchTemplate() {
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(this.pageSize);
            query.setOffset((pageNumber - 1) * pageSize);

            List<IFilter> filters = buildFilter();
            query.setFilters(filters);
            query.setOrders(ORDER_BY_LIST);

            return query;
        }

        public FindInvalidDataListArg toFindInvalidDataListArg(String tenantId) {
            FindInvalidDataListArg arg = new FindInvalidDataListArg();
            arg.setTenantId(tenantId);
            arg.setDescribeApiName(objectDescribeAPIName);
            arg.setName(name);
            arg.setStartDate(startDate);
            arg.setEndDate(endDate);
            arg.setInvalidOperator(invalidOperator);
            arg.setLimit(pageSize);
            arg.setOffset((pageNumber - 1) * pageSize);
            arg.setFindExplicitTotalNum(Boolean.TRUE.equals(findExplicitTotalNum));

            if (StringUtils.isNotEmpty(SortField)) {
                boolean asc = SortType == 1;
                if ("last_modified_by".equals(SortField)) {
                    SortField = "invalidBy";
                } else if ("last_modified_time".equals(SortField)) {
                    SortField = "invalidTime";
                }
                FindInvalidDataListArg.Order order = new FindInvalidDataListArg.Order(SortField, asc);
                arg.setOrders(Lists.newArrayList(order));
            }

            return arg;
        }

        private static final List<OrderBy> ORDER_BY_LIST = Lists.newArrayList();

        static {
            OrderBy orderBy = new OrderBy();
            orderBy.setFieldName(IObjectData.LAST_MODIFIED_TIME);
            orderBy.setFieldName(IObjectData.LAST_MODIFIED_TIME);
            orderBy.setIsAsc(false);
            ORDER_BY_LIST.add(orderBy);
        }

        private List<IFilter> buildFilter() {
            List<IFilter> filters = Lists.newArrayList();
            IFilter filter = new Filter();
            filter.setFieldName(IObjectData.IS_DELETED);
            filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
            filter.setFieldValueType("string");
            filter.setOperator(Operator.EQ);
            filter.setIndexName(IObjectData.IS_DELETED);
            filter.setFieldValues(Arrays.asList("1"));
            filters.add(filter);

            filter = new Filter();
            filter.setFieldName(IObjectData.DESCRIBE_API_NAME);
            filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
            filter.setFieldValueType("string");
            filter.setOperator(Operator.EQ);
            filter.setIndexName(IObjectData.DESCRIBE_API_NAME);
            filter.setFieldValues(Arrays.asList(this.getObjectDescribeAPIName()));
            filters.add(filter);

            if (!Strings.isNullOrEmpty(this.getName())) {
                filter = new Filter();
                filter.setFieldName(IObjectData.NAME);
                filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
                filter.setFieldValueType("string");
                filter.setOperator(Operator.LIKE);
                filter.setIndexName(IObjectData.NAME);
                filter.setFieldValues(Arrays.asList(this.getName()));
                filters.add(filter);
            }

            if (!Strings.isNullOrEmpty(this.getInvalidOperator())) {
                filter = new Filter();
                filter.setFieldName(IObjectData.LAST_MODIFIED_BY);
                filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
                filter.setFieldValueType("string");
                filter.setOperator(Operator.EQ);
                filter.setIndexName(IObjectData.LAST_MODIFIED_BY);
                filter.setFieldValues(Arrays.asList(this.getInvalidOperator()));
                filters.add(filter);
            }

            if (null != this.getStartDate() && this.getStartDate() != 0) {
                filter = new Filter();
                filter.setFieldName(IObjectData.LAST_MODIFIED_TIME);
                filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
                filter.setFieldValueType("date");
                filter.setOperator(Operator.GTE);
                filter.setIndexName(IObjectData.LAST_MODIFIED_TIME);
                filter.setFieldValues(Arrays.asList(this.getStartDate().toString()));
                filters.add(filter);
            }

            if (null != this.getEndDate() && this.getEndDate() != 0) {
                filter = new Filter();
                filter.setFieldName(IObjectData.LAST_MODIFIED_TIME);
                filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
                filter.setFieldValueType("date");
                filter.setOperator(Operator.LTE);
                filter.setIndexName(IObjectData.LAST_MODIFIED_TIME);
                filter.setFieldValues(Arrays.asList(this.getEndDate().toString()));
                filters.add(filter);
            }
            return filters;
        }
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private List<ObjectDataDocument> dataList;
        @JSONField(name = "M2")
        private Boolean success;
        @JSONField(name = "M3")
        private Integer pageCount;
        @JSONField(name = "M4")
        private Integer pageNumber;
        @JSONField(name = "M5")
        private Integer pageSize;
        @JSONField(name = "M6")
        private Integer totalCount;
        @JSONField(name = "M7")
        private List<ButtonDocument> bulkButtons;
    }

    @Data
    @Builder
    class QueryResult<T> {
        private List<T> data;
        private Integer totalNumber;
    }
}
