package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.QueryTemplateDocument;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by liyiguang on 2017/10/23.
 */
public interface GetTemplate {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        private String describeApiName;

        @JSONField(name = "M2")
        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        private String extendAttribute;

        @JSONField(name = "only_default_template")
        @JsonProperty("only_default_template")
        @SerializedName("only_default_template")
        private Boolean onlyDefaultTemplate;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        private List<QueryTemplateDocument> templates;

        @JSONField(name = "M2")
        @JsonProperty("filter_fields")
        @SerializedName("filter_fields")
        private List<CommonFilterField.FilterField> filterFields;

        @JSONField(name = "M3")
        @JsonProperty("field_infos")
        @SerializedName("field_infos")
        private Map<String, Map<String, Object>> fieldInfos;

        @JSONField(name = "M4")
        @JsonProperty("support_tag")
        @SerializedName("support_tag")
        private boolean supportTag;

        @JSONField(name = "M5")
        private List<ButtonDocument> buttons;

        @JSONField(name = "M6")
        @JsonProperty("support_geo_query")
        @SerializedName("support_geo_query")
        private boolean supportGeoQuery;
    }
}
