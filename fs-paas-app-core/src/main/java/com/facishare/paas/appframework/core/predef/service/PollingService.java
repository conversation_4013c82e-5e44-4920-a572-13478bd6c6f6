package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.common.service.model.PollingMsgEndType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.polling.PollingNotify;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ServiceModule("polling")
public class PollingService {
    @Autowired
    private MessagePollingService messagePollingService;

    @ServiceMethod("notify")
    public PollingNotify.Result pollingNotify(PollingNotify.Arg arg, ServiceContext context) {
        try {
            messagePollingService.sendPollingMessage(context.getUser(), arg.getPolling<PERSON>ey(), PollingMsgEndType.valueOf(arg.getMsgEndType()), arg.getIsRealTime());
        } catch(Exception e) {
            log.warn("Error in send notify by polling, arg:{}", arg, e);
        }
        return PollingNotify.Result.builder().success(true).build();
    }
}
