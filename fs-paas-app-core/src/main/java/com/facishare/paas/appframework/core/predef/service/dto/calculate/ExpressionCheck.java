package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface ExpressionCheck {
    @Data
    class Arg {
        private String json_data;
        private List<ExpressionDTO.FormVariableDTO> extFields;
        private Boolean onlySupportGrounded;
        private Boolean errorReminder;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private String value;
        private int code;
    }
}
