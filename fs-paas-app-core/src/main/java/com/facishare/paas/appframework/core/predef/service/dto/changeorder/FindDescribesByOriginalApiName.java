package com.facishare.paas.appframework.core.predef.service.dto.changeorder;

import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.changeorder.OriginalAndChangeDescribes;
import lombok.Builder;
import lombok.Data;

import java.util.Map;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON>ju on 2023/3/20
 */
public interface FindDescribesByOriginalApiName {

    @Data
    class Arg {
        private String describeApiName;
    }

    @Data
    @Builder
    class Result {
        private ChangeOrderAllDescribes changeOrderDescribes;

    }

    @Data
    @Builder
    class ChangeOrderAllDescribes {
        private ObjectDescribeDocument originalDescribe;
        private Map<String, ObjectDescribeDocument> originalDetailDescribes;
        private ObjectDescribeDocument changeOrderDescribe;
        private Map<String, ObjectDescribeDocument> changeOrderDetailDescribes;

        public static ChangeOrderAllDescribes of(OriginalAndChangeDescribes changeOrderDescribes) {
            if (Objects.isNull(changeOrderDescribes)) {
                return builder().build();
            }
            return builder()
                    .originalDescribe(ObjectDescribeDocument.of(changeOrderDescribes.getOriginalDescribes().getObjectDescribe()))
                    .originalDetailDescribes(ObjectDescribeDocument.ofMap(changeOrderDescribes.getOriginalDescribes().getDetailDescribes()))
                    .changeOrderDescribe(ObjectDescribeDocument.of(changeOrderDescribes.getChangeOrderDescribes().getObjectDescribe()))
                    .changeOrderDetailDescribes(ObjectDescribeDocument.ofMap(changeOrderDescribes.getChangeOrderDescribes().getDetailDescribes()))
                    .build();

        }
    }
}
