package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

public interface SetDataPermissionItems {

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String fieldApiName;
        private String objectApiName;
        private String roleType;
        private Boolean isChecked;
    }

    @Builder
    @Data
    class Result {
        Boolean success;
    }

}
