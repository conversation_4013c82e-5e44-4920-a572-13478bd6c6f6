package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by liyiguang on 2017/10/23.
 */
public interface FindCustomeSceneById {
    @Data
    class Arg {
        @JSONField(name = "M1")
        String scene_id;

        @JSONField(name = "M2")
        String describe_api_name;

        @JSONField(name = "M3")
        String scene_api_name;

        @JSONField(name = "M4")
        String scene_type;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        boolean success;

        @JSONField(name = "M2")
        Map searchTemplate;
    }
}
