package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * create by <PERSON><PERSON><PERSON> on 2018/11/14
 */
public interface TemporaryRights {

    @Data
    class Arg {
        private String sourceId;
        private String dataId;
        private String owner;
        private String apiName;
        private Set<String> temporaryRightsIds;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
        private String message;
    }

}
