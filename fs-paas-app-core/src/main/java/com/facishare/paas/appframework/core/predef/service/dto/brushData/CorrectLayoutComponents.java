package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/1/29
 */
public interface CorrectLayoutComponents {

    @Data
    class Arg {
        private String describeApiName;
        private String layoutApiName;
        private boolean updateLayout;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private LayoutDocument layout;
    }
}
