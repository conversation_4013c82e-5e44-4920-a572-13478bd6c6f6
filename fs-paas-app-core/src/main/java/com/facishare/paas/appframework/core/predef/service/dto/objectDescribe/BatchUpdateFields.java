package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface BatchUpdateFields {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        List<BatchUpdateFieldsInfo> batchUpdateFieldsInfoList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class BatchUpdateFieldsInfo {
        String describeApiName;
        List<FieldInfo> fieldInfos;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class FieldInfo {
        String fieldApiName;
        String attributeKey;
        Object attributeValue;

        public Map<String, Object> toMap() {
            Map<String, Object> fieldInfoMap = Maps.newHashMap();
            fieldInfoMap.put("fieldApiName", getFieldApiName());
            fieldInfoMap.put("attributeValue", getAttributeValue());
            fieldInfoMap.put("attributeKey", getAttributeKey());
            return fieldInfoMap;
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    class Result {
        String message;
        Map<String, Set<String>> failApiNameList;
        boolean success;
    }
}
