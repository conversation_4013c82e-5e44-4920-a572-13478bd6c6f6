package com.facishare.paas.appframework.core.predef.service.dto.function;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

public interface DeleteFunctionRelation {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String type;
        String value;
        String funcApiName;
    }

    @Data
    @Builder
    @Jacksonized
    class Result {
        int code;
        String message;
    }
}
