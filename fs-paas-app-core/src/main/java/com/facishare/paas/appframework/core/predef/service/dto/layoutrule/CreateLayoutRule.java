package com.facishare.paas.appframework.core.predef.service.dto.layoutrule;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutRuleDocument;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

public interface CreateLayoutRule {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("json_data")
        @SerializedName("json_data")
        private LayoutRuleDocument layoutRule;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        @JSONField(name = "M1")
        boolean success;
    }
}
