package com.facishare.paas.appframework.core.predef.service.dto.handler;

import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.appframework.metadata.repository.model.HandlerRuntimeConfig;
import lombok.Data;

import java.util.List;

/**
 * Created by zhouwr on 2023/2/23.
 */
public interface UpsertHandlerDefinitionAndRuntimeConfig {
    @Data
    class Arg {
        private List<HandlerDefinition> definitions;
        private List<HandlerRuntimeConfig> runtimeConfigs;
    }

    class Result {

    }
}
