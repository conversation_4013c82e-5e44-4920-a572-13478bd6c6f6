package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

public interface GetZoneByParent {
    @Data
    class Arg {
        //地区类型：只支持省，市，区
        private String parentPath;
        private String valueType;
        private Boolean cascadeChildren;
        private Integer cascadeLevel;
    }

    @Data
    @Builder
    class Result {
        List<ZoneInfo> optionList;
    }

    @Data
    @Builder
    class ZoneInfo {
        String value;
        String label;
        String type;
        String parentValue;
        String briefCode;

        public static List<ZoneInfo> fromCountryArea(List<CountryAreaService.CountryAreaDTO> list) {
            if(CollectionUtils.empty(list)) {
                return Lists.newArrayList();
            }

            return list.stream().map(a-> ZoneInfo.builder()
                    .value(a.getFsUniqueCode())
                    .label(a.getName()) // 需要修改
                    .type(a.getType())
                    .parentValue(a.getParentFsUniqueCode())
                    .briefCode(a.getBriefCode())
                    .build()).collect(Collectors.toList());
        }
    }

}
