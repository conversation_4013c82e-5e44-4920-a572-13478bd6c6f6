package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.facishare.paas.appframework.metadata.dto.RecordTypeMatchInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface FindRecordTypeMatchList {
    @Data
    class Arg{
        String describeApiName;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result{
        @JsonProperty("master_label")
        private String masterLabel;
        @JsonProperty("detail_label")
        private String detailLabel;
        @JsonProperty("master_record_list")
        private List<RecordTypeMatchInfo> masterRecordList;
        @JsonProperty("detail_record_list")
        private List<RecordTypeMatchInfo> detailRecordList;
    }
}
