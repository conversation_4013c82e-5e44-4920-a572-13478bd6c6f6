package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.privilege.dto.TemporaryRightsInfo;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public interface QueryTemporaryRights {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        @JSONField(name = "describe_api_name")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        String objectDescribeApiName;

        @JSONField(name = "object_data_id")
        @JsonProperty("object_data_id")
        @SerializedName("object_data_id")
        String objectDataId;

        Integer pageSize;

        Integer pageNumber;

        @JSONField(name = "user_id")
        @JsonProperty("user_id")
        @SerializedName("user_id")
        String userId;

        String scene;
    }

    @Data
    @Builder
    class Result {
        List<TemporaryPrivilegeInfo> resultList;
        List<TemporaryRightsInfo> rightsInfoList;
        Integer totalNumber;
        Integer pageSize;
        Integer pageNumber;
        Integer pageCount;
    }
}
