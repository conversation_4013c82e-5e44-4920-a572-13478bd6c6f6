package com.facishare.paas.appframework.core.predef.service.dto.objectImport;

import lombok.Data;

import java.util.List;

public interface FindImportInfosByNames {
    @Data
    class Result {
        private int rowNo;
        private List<ImportCellInfo> cellInfoList;
    }

    @Data
    class Arg {
        private List<CellStruct> cellStructList;
    }

    @Data
    class ImportCellInfo {
        private int rowNo;
        private String describeApiName;
        private Object primaryFieldValue;

        /**
         * 第一条数据的id
         */
        private String firstDataId;
        private int amount;
    }

    @Data
    class CellStruct {
        private int rowNo;
        private String describeApiName;
        private String primaryFieldValue;
    }

}
