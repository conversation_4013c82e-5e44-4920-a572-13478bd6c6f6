package com.facishare.paas.appframework.core.predef.service.dto.publicobject;

import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectStatusResult;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectStatusType;
import lombok.Builder;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/11
 */
public interface QueryPublicObjectStatus {

    @Data
    class Arg {
        private String objectApiName;
    }

    @Data
    class Result {
        /**
         * disable: 不支持开启公共对象
         * enable：支持开启公共对象
         * opening：开启公共对象中，不能编辑描述
         * opened：已经开启了公共对象
         */
        private String status;
        private String jobId;
        private String jobType;

        @Builder
        public Result(PublicObjectStatusType status, String jobId, String jobType) {
            this.status = status.getType();
            this.jobId = jobId;
            this.jobType = jobType;
        }

        public static Result of(PublicObjectStatusResult publicObjectStatusResult) {
            return Result.builder()
                    .status(publicObjectStatusResult.getPublicObjectStatus())
                    .jobId(publicObjectStatusResult.getJobId())
                    .jobType(publicObjectStatusResult.getJobType())
                    .build();
        }
    }
}
