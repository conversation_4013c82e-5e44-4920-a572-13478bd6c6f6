package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface ChangePeopleAllocateRule {


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JsonProperty("object_api_name")
        @JSONField(name = "object_api_name")
        private String objectApiName;

        @JsonProperty("field_api_name")
        @JSONField(name = "field_api_name")
        private String fieldApiName;
        @JsonProperty("allocate_rule")
        @JSONField(name = "allocate_rule")
        private String allocateRule;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class Result {
        Boolean success;
        String message;
    }

    enum RequestCode {
        FAIL("500"),

        SUCCESS("200");

        String code;

        RequestCode(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

}
