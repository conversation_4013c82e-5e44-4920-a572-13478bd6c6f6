package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface CheckNeedShowRelatedObjs {
    @Data
    class Arg {
        @JSONField(name = "M2")
        String objectDescribeApiName;
        @JSONField(name = "M1")
        String objectDataId;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        Boolean result;
    }
}
