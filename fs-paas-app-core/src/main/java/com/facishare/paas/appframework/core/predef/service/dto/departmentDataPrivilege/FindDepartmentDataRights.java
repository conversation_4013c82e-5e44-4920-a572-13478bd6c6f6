package com.facishare.paas.appframework.core.predef.service.dto.departmentDataPrivilege;

import com.facishare.paas.appframework.privilege.dto.DepartmentDataRights;
import com.facishare.paas.appframework.privilege.dto.DepartmentRightsResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface FindDepartmentDataRights {

    @Data
    class Arg{
        List<String> ids;
        int scene;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result{
        List<DepartmentRightsResult> result;
    }

}
