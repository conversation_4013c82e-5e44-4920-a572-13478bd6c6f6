package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.IconDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface FindIconList {
    @Data
    class Arg {

    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        @JSONField(name = "M1")
        List<IconDocument> iconList;
    }
}
