package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by luozhq on 2018/3/27.
 */
public interface QueryFunction {

    @Data
    class Arg {
        @JSONField(name = "function_name")
        @SerializedName(value = "function_name")
        @JsonProperty(value = "function_name")
        String functionName;

        @JSONField(name = "binding_object_api_name")
        @SerializedName(value = "binding_object_api_name")
        @JsonProperty(value = "binding_object_api_name")
        String bindingObjectAPIName;

        @JSONField(name = "name_space")
        @SerializedName(value = "name_space")
        @JsonProperty(value = "name_space")
        List<String> nameSpace;

        @JSONField(name = "pageNumber")
        @SerializedName(value = "pageNumber")
        @JsonProperty(value = "pageNumber")
        String pageNumber;

        @JSONField(name = "pageSize")
        @SerializedName(value = "pageSize")
        @JsonProperty(value = "pageSize")
        String pageSize;

        @JSONField(name = "is_include_used")
        @SerializedName(value = "is_include_used")
        @JsonProperty(value = "is_include_used")
        Boolean isIncludeUsed;

        @JSONField(name = "is_active")
        @SerializedName(value = "is_active")
        @JsonProperty(value = "is_active")
        Boolean isActive;

        @JSONField(name = "return_type")
        @SerializedName(value = "return_type")
        @JsonProperty(value = "return_type")
        String returnType;

        @JSONField(name = "return_type_list")
        @SerializedName(value = "return_type_list")
        @JsonProperty(value = "return_type_list")
        List<String> returnTypeList;  //函数返回值类型批量查询，保留上面属性兼容旧版代码


    }

    @Data
    @Builder
    class Result {
        List<FunctionInfo> function;
        Integer totalNumber;

    }
}
