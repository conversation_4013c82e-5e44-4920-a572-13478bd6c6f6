package com.facishare.paas.appframework.core.predef.service.dto.objectImport;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.*;

@Data
public class MDImportObj {
    String apiName;
    Set<String> objectSet;
    IObjectDescribe describe;

    public final static List<String> NO_BATCH_IMPORT_LIST = Lists.newArrayList(
           Utils.GOODS_RECEIVED_NOTE_API_NAME, Utils.DELIVERY_NOTE_API_NAME,
            Utils.OUTBOUND_DELIVER_NOTE_API_NAME
    );

    private static final List<String> GOOD_MD_OBJ = Lists.newArrayList(
            Utils.GOODS_RECEIVED_NOTE_API_NAME,
            Utils.GOODS_RECEIVED_NOTE_PRODUCT_API_NAME
    );
    private static final List<String> DELIVERY_MD_OBJ = Lists.newArrayList(
            Utils.DELIVERY_NOTE_API_NAME,
            Utils.DELIVERY_NOTE_PRODUCT_API_NAME
    );

    //出库单
    private static final List<String> OUT_MD_OBJ = Lists.newArrayList(
            Utils.OUTBOUND_DELIVER_NOTE_API_NAME,
            Utils.OUTBOUND_DELIVER_NOTE_PRODUCT_API_NAME
    );

    private static IObjectDescribe generateObjAllObj(String apiName, String displayName, boolean isUnique) {
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName(apiName);
        objectDescribe.setDisplayName(displayName);
        //构造Field实例， 主属性描述
        Map<String, Object> nameMap = Maps.newHashMap();
        nameMap.put(IFieldDescribe.API_NAME, IObjectData.NAME);
        nameMap.put(IFieldDescribe.TYPE, IFieldType.TEXT);
        nameMap.put(IFieldDescribe.IS_UNIQUE, isUnique);
        nameMap.put(IFieldDescribe.IS_REQUIRED, true);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(nameMap);
        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe));
        return objectDescribe;
    }


    public static void handle(List<IObjectDescribe> validList) {
        Optional<IObjectDescribe> first = validList.stream().filter(b -> GOOD_MD_OBJ.contains(b.getApiName())).findFirst();
        first.ifPresent(c->{
            validList.removeIf(d->GOOD_MD_OBJ.contains(d.getApiName()));
            validList.add(MDImportObj.generateObjAllObj(Utils.GOODS_RECEIVED_NOTE_API_NAME,
                    I18N.text(I18NKey.GOODS_RECEIVED_NOTE_GOODS_RECEIVED_NOTE_PRODUCT), false));
        });

        Optional<IObjectDescribe> second = validList.stream().filter(b -> DELIVERY_MD_OBJ.contains(b.getApiName())).findFirst();
        second.ifPresent(c->{
            IFieldDescribe fieldDescribe = c.getFieldDescribe(IObjectData.NAME);
            boolean isUnique = false;
            if (Objects.nonNull(fieldDescribe)) {
                isUnique = fieldDescribe.isUnique();
            }
            validList.removeIf(d->DELIVERY_MD_OBJ.contains(d.getApiName()));
            validList.add(MDImportObj.generateObjAllObj(Utils.DELIVERY_NOTE_API_NAME,
                    I18N.text(I18NKey.DELIVERY_NOTE_DELIVERY_NOTE_PRODUCT), isUnique));
        });

        Optional<IObjectDescribe> third = validList.stream().filter(b -> OUT_MD_OBJ.contains(b.getApiName())).findFirst();
        third.ifPresent(c->{
            validList.removeIf(d->OUT_MD_OBJ.contains(d.getApiName()));
            validList.add(MDImportObj.generateObjAllObj(Utils.OUTBOUND_DELIVER_NOTE_API_NAME,
                    I18N.text(I18NKey.OUT_NOTE_OUT_NOTE_PRODUCT), false));
        });
    }


}
