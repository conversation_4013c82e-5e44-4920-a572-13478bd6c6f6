package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

public interface CheckPrivilege {
    @Data
    class Arg {
        @JsonProperty("id")
        @SerializedName("id")
        @JSONField(name = "M1")
        String id;

        @JsonProperty("object_describe_api_name")
        @SerializedName("object_describe_api_name")
        @JSONField(name = "M2")
        String objectDescribeApiName;

        @JsonProperty("action_code")
        @SerializedName("action_code")
        @JSONField(name = "M3")
        String actionCode;
    }
    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        String value;
    }
}
