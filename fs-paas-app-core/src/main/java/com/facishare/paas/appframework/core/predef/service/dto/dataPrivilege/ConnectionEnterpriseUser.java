package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/16 2:34 下午
 */
public interface ConnectionEnterpriseUser {

    @Data
    class Arg {
        @JSONField(name = "M1")
        Integer offset;
        @JSONField(name = "M2")
        Integer limit;
        @J<PERSON>NField(name = "M3")
        String shortName;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        List<ConnectionEnterpriseInfo> infos;
    }

    @Data
    @Builder
    class ConnectionEnterpriseInfo {
        String outTenantId;
        String outTenantName;
        List<OutUser> outUsers;
    }

    @Data
    @Builder
    class OutUser {
        String outUserId;
        String outUserName;
        String gender;
        String profile;
    }


}