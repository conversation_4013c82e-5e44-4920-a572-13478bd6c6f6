package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/13
 */
public interface GetAllPluginVo {

    @Data
    class Arg {
        /**
         * 页号，从0开始
         */
        private int pageNumber;
        /**
         * 页大小
         */
        private int pageSize;
        /**
         * 应用类型 PersonalAuthAppType
         */
        private String appType;

        public void validate() {
            if (pageSize <= 0) {
                //todo:待完善
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_DEFINE_TYPE));
            }
        }
    }

    @Data
    class Result {
        /**
         * 插件列表
         */
        private List<PluginItem> pluginList;
        /**
         * 是否有下一页
         */
        private boolean hasMore;
    }

    @Data
    class PluginItem {
        /**
         * 预制类型
         */
        private String defineType;
        /**
         * 应用类型 PackagePluginAppType
         */
        private String appType;
        /**
         * 应用名称
         */
        private String appName;
        /**
         * 插件名称
         */
        private String pluginName;
        /**
         * 插件apiName
         */
        private String pluginApiName;
        /**
         * icon cPath
         */
        private String icon;
        /**
         * icon 全路径
         */
        private String iconUrl;
        /**
         * 是否启用
         */
        private boolean active;
        /**
         * 是否绑定开发账号
         */
        private boolean binding;
        /**
         * 扩展信息
         */
        private Map<String, String> extraInfo = Maps.newHashMap();
    }
}
