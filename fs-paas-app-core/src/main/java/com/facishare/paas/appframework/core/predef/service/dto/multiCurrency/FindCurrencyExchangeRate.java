package com.facishare.paas.appframework.core.predef.service.dto.multiCurrency;

import com.facishare.paas.appframework.metadata.repository.model.MtCurrencyExchange;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface FindCurrencyExchangeRate {

    @Data
    class Arg {
        private String toCurrencyCode;
    }

    @Data
    @Builder
    class Result {
        List<MtCurrencyExchange> currencyExchanges;
    }
}
