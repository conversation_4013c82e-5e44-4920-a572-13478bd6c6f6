package com.facishare.paas.appframework.core.predef.service.dto.layoutrule;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.LayoutRuleDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;

public interface FindRuleRelatedInfo {

    @Data
    class Arg {

        @JSONField(name = "M1")
        private String describeApiName;
        private Boolean includeDescribe;
        private Boolean includeLayout;
        private String sourceInfo;
        /**
         * 是否需要对布局进行多语处理 true: 需要多语处理 false或null: 不需要多语处理
         */
        private Boolean needLayoutLang;

        public boolean includeDescribe() {
            return BooleanUtils.isNotFalse(includeDescribe);
        }

        public boolean includeLayout() {
            return BooleanUtils.isNotFalse(includeLayout);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        @JSONField(name = "M1")
        private List<LayoutDocument> layoutList;
        @JSONField(name = "M2")
        private List<LayoutRuleDocument> layoutRuleList;
        @JSONField(name = "M3")
        private ObjectDescribeDocument describe;
        private ManageGroupDTO layoutManageGroup;
    }
}
