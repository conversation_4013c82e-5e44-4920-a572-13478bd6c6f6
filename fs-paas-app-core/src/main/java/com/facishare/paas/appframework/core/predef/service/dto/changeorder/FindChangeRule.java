package com.facishare.paas.appframework.core.predef.service.dto.changeorder;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2023/3/30
 */
public interface FindChangeRule {

    @Data
    class Arg {
        private String ruleApiName;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class FindAllResult {
        private List<MtChangeOrderRuleDTO> changeRules;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class FindOneResult {
        private MtChangeOrderRule changeRule;
    }

    class Result {
        public static Result buildSuccess() {
            return new Result();
        }
    }

    @Data
    @Builder
    class MtChangeOrderRuleDTO {
        private String label;
        @JsonProperty("api_name")
        private String apiName;
        @JsonProperty("original_describe_api_name")
        private String originalDescribeApiName;
        @JsonProperty("original_describe_display_name")
        private String originalDescribeDisplayName;
        @JsonProperty("change_describe_api_name")
        private String changeDescribeApiName;
        @JsonProperty("change_describe_display_name")
        private String changeDescribeDisplayName;
        @JsonProperty("effective_timing")
        private String effectiveTiming;
        @JsonProperty("calibration_timing")
        private List<String> calibrationTiming;
        @JsonProperty("is_active")
        private Boolean active;
        @JsonProperty("last_modified_time")
        protected Long lastModifiedTime;
        @JsonProperty("last_modified_by")
        protected String lastModifiedBy;

        public static MtChangeOrderRuleDTO from(MtChangeOrderRule changeOrderRule) {
            return MtChangeOrderRuleDTO.builder()
                    .label(changeOrderRule.getLabel())
                    .apiName(changeOrderRule.getApiName())
                    .originalDescribeApiName(changeOrderRule.getOriginalDescribeApiName())
                    .changeDescribeApiName(changeOrderRule.getChangeDescribeApiName())
                    .effectiveTiming(changeOrderRule.getEffectiveTiming())
                    .calibrationTiming(CollectionUtils.nullToEmpty(changeOrderRule.getCalibrationTiming()))
                    .active(changeOrderRule.getActive())
                    .lastModifiedTime(changeOrderRule.getLastModifiedTime())
                    .lastModifiedBy(changeOrderRule.getLastModifiedBy())
                    .build();
        }
    }
}
