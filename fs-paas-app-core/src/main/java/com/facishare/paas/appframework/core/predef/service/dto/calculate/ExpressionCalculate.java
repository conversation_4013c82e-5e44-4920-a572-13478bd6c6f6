package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface ExpressionCalculate {
    @Data
    class Arg {
        @JSONField(name = "M1")
        String api_name;

        @JSONField(name = "M2")
        String objectDescribeApiName;

        @JSONField(name = "M3")
        private String object_data;

        @JSONField(name = "M4")
        boolean calculateFormulaOnly = false;
    }
    @Data
    class Result {
        @JSONField(name = "M1")
        Map value_list;
    }
}
