package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/04/26
 */
public interface BulkInvalidData {

    @Data
    class Arg {
        @JsonProperty("data_id")
        private String masterDataId;
        @JsonProperty("object_describe_api_name")
        private String masterObjectApiName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private boolean success;
        private String message;

        public static Result buildSuccess() {
            Result result = new Result();
            result.setSuccess(true);
            return result;
        }

        public static Result buildError(String message) {
            Result result = new Result();
            result.setSuccess(false);
            result.setMessage(message);
            return result;
        }
    }
}
