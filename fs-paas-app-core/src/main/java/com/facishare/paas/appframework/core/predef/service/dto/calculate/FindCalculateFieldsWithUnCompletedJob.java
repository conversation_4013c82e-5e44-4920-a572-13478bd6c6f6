package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.dto.QueryJob;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.repository.model.MtAsyncTaskMonitor;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2024/10/11.
 */
public interface FindCalculateFieldsWithUnCompletedJob {

    @Data
    class Arg {
        private String objectApiName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        @Builder.Default
        private List<CalculateFieldInfo> calculateFields = Lists.newArrayList();
        private boolean supportCalculationProgress;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class CalculateFieldInfo {
        private String apiName;
        private String type;
        private String jobId;

        public static List<CalculateFieldInfo> of(QueryJob.JobInfo jobInfo, IObjectDescribe describe) {
            List<String> fieldApiNames = JSON.parseArray(jobInfo.getJobParam(), String.class);
            return getCalculateFieldInfoList(fieldApiNames, describe, jobInfo.getId());
        }

        private static List<CalculateFieldInfo> getCalculateFieldInfoList(List<String> fieldApiNames, IObjectDescribe describe, String jobInfo) {
            if (CollectionUtils.empty(fieldApiNames)) {
                return Collections.emptyList();
            }
            return fieldApiNames.stream()
                    .filter(describe::containsField)
                    .map(fieldApiName -> CalculateFieldInfo.builder()
                            .apiName(fieldApiName)
                            .type(describe.getFieldDescribe(fieldApiName).getType())
                            .jobId(jobInfo)
                            .build())
                    .collect(Collectors.toList());
        }

        public static List<CalculateFieldInfo> of(MtAsyncTaskMonitor jobInfo, IObjectDescribe describe) {
            List<String> fieldApiNames = Lists.newArrayList(StringUtils.split(jobInfo.getBizApiName(), ","));
            return getCalculateFieldInfoList(fieldApiNames, describe, jobInfo.getTaskId());
        }
    }

}
