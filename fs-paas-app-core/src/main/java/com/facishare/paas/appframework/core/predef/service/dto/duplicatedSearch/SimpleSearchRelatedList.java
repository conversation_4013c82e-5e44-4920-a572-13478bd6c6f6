package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Objects;

public interface SimpleSearchRelatedList {
    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
        private String keyword;
        private Boolean isNeedDuplicate;
        private Integer pageSize;
        private Integer pageNumber;
    }
}
