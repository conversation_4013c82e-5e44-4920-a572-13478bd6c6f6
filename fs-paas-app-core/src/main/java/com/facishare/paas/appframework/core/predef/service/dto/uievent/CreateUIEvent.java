package com.facishare.paas.appframework.core.predef.service.dto.uievent;

import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.appframework.core.model.UIEventDocument;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface CreateUIEvent {
    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String describeApiName;
        private String layoutApiName;
        private List<UIEventDocument> events;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<UIEventDocument> events;
    }
}
