package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface DelShareRules {
    @Data
    class Arg {
        @JsonProperty("SharedRuleIds")
        @SerializedName("SharedRuleIds")
        @JSONField(name = "M1")
        List<String> sharedRuleIds;
    }
    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        boolean result;
    }
}
