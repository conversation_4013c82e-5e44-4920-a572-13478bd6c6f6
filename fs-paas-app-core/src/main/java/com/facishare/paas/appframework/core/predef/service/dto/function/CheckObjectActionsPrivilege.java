package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2019-03-12.
 */
public interface CheckObjectActionsPrivilege {

    @Data
    class Arg {
        String apiName;
        List<String> actionCodes;
    }

    @Data
    class Result {

        @JsonProperty("Result")
        @SerializedName("Result")
        @JSONField(name = "Result")
        Map<String, Object> result;

        @JsonProperty("Value")
        @SerializedName("Value")
        @JSONField(name = "Value")
        Object value;

        public static Result ofSuccess(Map<String, Boolean> result) {
            Result result1 = new Result();

            Map<String, Object> tmpResult = Maps.newHashMap();
            tmpResult.put("FailureCode", 0);

            result1.setResult(tmpResult);
            result1.setValue(result);

            return result1;
        }

        public static Result ofFailed(int errorCode, String errorMsg) {
            Result result1 = new Result();

            Map<String, Object> tmpResult = Maps.newHashMap();
            tmpResult.put("FailureCode", errorCode);
            tmpResult.put("FailureMsg", errorMsg);
            result1.setResult(tmpResult);
            return result1;
        }
    }

}
