package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import lombok.Data;

/**
 * 个人级列宽设置：<br>
 * 个人级列宽设置会导致场景列宽设置失效<br>
 */
public interface CustomFieldList {

    /**
     * 删除个人列宽设置
     */
    interface Delete {

        @Data
        class Arg {
            private String objApiName;
            private String whatApiName;
            private String sessionKey;
            private String extendAttribute;
        }

        @Data
        class Result {
            private boolean success;

            public static Result succeed() {
                Result result = new Result();
                result.setSuccess(true);
                return result;
            }
        }
    }
}
