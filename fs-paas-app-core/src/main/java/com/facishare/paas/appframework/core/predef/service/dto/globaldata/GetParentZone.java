package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

public interface GetParentZone {
    @Data
    class Arg {
        private String value;
    }

    @Data
    @Builder
    class Result {
        List<GetZoneByParent.ZoneInfo> parentList;
    }

    @Data
    @Builder
    class ZoneInfo {
        String value;
        String label;
        String type;

        public static List<GetZoneByParent.ZoneInfo> fromCountryArea(List<CountryAreaService.CountryAreaDTO> list) {
            if(CollectionUtils.empty(list)) {
                return Lists.newArrayList();
            }

            return list.stream().map(a-> GetZoneByParent.ZoneInfo.builder()
                    .value(a.getFsUniqueCode())
                    .label(a.getName()) // 需要修改
                    .type(a.getType())
                    .build()).collect(Collectors.toList());
        }
    }

}
