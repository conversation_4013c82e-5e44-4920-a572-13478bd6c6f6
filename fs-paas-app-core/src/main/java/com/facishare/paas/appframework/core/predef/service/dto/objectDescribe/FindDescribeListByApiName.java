package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by zhouwr on 2017/11/2
 */
public interface FindDescribeListByApiName {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("describe_apiname_list")
        @SerializedName("describe_apiname_list")
        List<String> describeApiNameList;
        private Boolean includeBigObject;
        private Boolean includeSocialObject;
    }

    @Data
    class Result {
        public List<ObjectDescribeDocument> objectDescribeList;
    }
}
