package com.facishare.paas.appframework.core.predef.service.dto.option;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON>ju on 2022/2/17
 */
public interface TriggerCalculate {

    @Data
    class Arg {
        @JsonProperty("api_names")
        private Set<String> optionApiNames;
        @JsonProperty("only_touch")
        private Boolean onlyTouch;
    }

    class Result {

    }
}