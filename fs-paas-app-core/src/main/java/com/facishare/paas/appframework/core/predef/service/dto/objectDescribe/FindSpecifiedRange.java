package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.common.util.Tuple;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/03/06
 */
public interface FindSpecifiedRange {

    @Data
    class Arg {
        private String objectApiName;
        private String associatedObjectApiName;
    }

    @Data
    @Builder
    class Result {
        private List<SpecifiedRange> specifiedRange;

        static public Result of(Map<String, List<Tuple<String, String>>> map) {
            return Result.builder()
                    .specifiedRange(map.entrySet().stream().map(entry -> SpecifiedRange.builder()
                            .displayName(entry.getKey())
                            .filters(entry.getValue().stream().map(x -> Filter.of(x.getKey(), x.getValue()))
                                    .collect(Collectors.toList()))
                            .build()).collect(Collectors.toList()))
                    .build();
        }
    }

    @Data
    @Builder
    class SpecifiedRange {
        private String displayName;
        private List<Filter> filters;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Filter {
        private String displayName;
        private String fieldValue;
    }
}
