package com.facishare.paas.appframework.core.predef.service.dto.option;

import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Set;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/12/15
 */
public interface FindOptionSet {
    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private MtOptionSet options;
    }

    @Data
    class Arg {
        @JsonProperty("api_name")
        private String optionApiName;
    }
}
