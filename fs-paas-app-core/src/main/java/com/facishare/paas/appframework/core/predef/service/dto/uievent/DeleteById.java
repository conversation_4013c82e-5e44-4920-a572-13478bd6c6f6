package com.facishare.paas.appframework.core.predef.service.dto.uievent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

public interface DeleteById {
    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String describeApiName;
        private String layoutApiName;
        private Set<String> eventIdList;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private boolean success;
    }
}
