package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-11 16:40
 */
public interface FindBaseScene {
    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
        private String extendAttribute;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @Builder
    class Result {
        List<IScene> baseScenes;
    }
}
