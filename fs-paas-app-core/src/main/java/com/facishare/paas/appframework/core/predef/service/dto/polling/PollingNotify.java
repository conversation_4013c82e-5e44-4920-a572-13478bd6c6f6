package com.facishare.paas.appframework.core.predef.service.dto.polling;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutRuleDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface PollingNotify {
    @Data
    class Arg {
        @JsonProperty("pollingKey")
        @SerializedName("pollingKey")
        private String pollingKey;

        @JsonProperty("isRealTime")
        @SerializedName("isRealTime")
        private Boolean isRealTime;

        @JsonProperty("msgEndType")
        @SerializedName("msgEndType")
        private String msgEndType;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        boolean success;
    }
}
