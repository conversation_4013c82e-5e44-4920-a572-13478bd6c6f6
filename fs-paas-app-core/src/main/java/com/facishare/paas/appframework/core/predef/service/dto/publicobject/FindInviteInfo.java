package com.facishare.paas.appframework.core.predef.service.dto.publicobject;

import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobInvitationInfo;
import lombok.Data;

import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/24
 */
public interface FindInviteInfo {
    @Data
    class Arg {
        private String objectApiName;
        private String token;
    }

    @Data
    class Result {
        private final String status;
        private final String message;
        private final String jobId;

        private Result(String status, String message, String jobId) {
            this.status = status;
            this.message = message;
            this.jobId = jobId;
        }

        public static Result fromPublicObjectJobInvitationInfo(PublicObjectJobInvitationInfo info) {
            if (Objects.isNull(info)) {
                return new Result(null, null, null);
            }
            return new Result(info.getStatus(), info.getMessage(), info.getJobId());
        }
    }
}
