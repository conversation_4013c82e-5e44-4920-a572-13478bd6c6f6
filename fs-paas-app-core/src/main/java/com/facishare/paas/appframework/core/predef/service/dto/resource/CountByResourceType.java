package com.facishare.paas.appframework.core.predef.service.dto.resource;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/8/4
 */
public interface CountByResourceType {

    @Data
    class Arg {
        private String resourceParentValue;
        private String resourceType;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private Integer total;
    }
}
