package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/11/01
 */
public interface FindFieldWidthConfig {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    class Arg {
        private List<String> describeApiNames;
    }

    @Data
    @Builder
    class Result {
        private Map<String, List<Map>> visibleFieldsWidth;
    }
}
