package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.facishare.paas.appframework.function.FunctionVSCodeExt;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface DownloadFunction {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String apiName;
        String bindingObjectApiName;
        String type;
        int pageNumber;
        int pageSize = 20;
    }

    @Data
    @Builder
    class Result  {
        List<FunctionVSCodeExt> list;
    }
}
