package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataPrivilegeSwitchConfig {

    @J<PERSON>NField(name = "workflow.temporary.permission")
    @JsonProperty("workflow.temporary.permission")
    private SwitchConfig permission;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SwitchConfig {
        public static final String KEY = "workflow.temporary.permission";
        public static final String SWITCH = "workflow.temporary.permission.switch";
        public static final String LEVEL = "workflow.temporary.permission.level";
        public static final String VALIDITY_TERM =  "workflow.temporary.permission.validity.term";

        @J<PERSON>NField(name = "workflow.temporary.permission.switch")
        @JsonProperty("workflow.temporary.permission.switch")
        private Boolean confSwitch;
        @JSONField(name = "workflow.temporary.permission.level")
        @JsonProperty("workflow.temporary.permission.level")
        private Integer level;
        @JSONField(name = "workflow.temporary.permission.validity.term")
        @JsonProperty("workflow.temporary.permission.validity.term")
        private Integer validityTerm;
    }
}

