package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.alibaba.fastjson.annotation.JSONField;
import com.beust.jcommander.internal.Lists;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.lab4inf.math.util.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2020/11/17
 */
public interface FindAssignedListLayout {

    @Data
    class Arg {
        private String describeApiName;
        private String whatDescribeApiName;
        private String sourceInfo;
        private String layoutType;
        private String appId;

        public String getLayoutType() {
            if (Strings.isNullOrEmpty(layoutType)) {
                return LayoutTypes.LIST_LAYOUT;
            }
            return layoutType;
        }

        public boolean flowTaskLayout() {
            return LayoutTypes.FLOW_TASK_LIST.equals(layoutType);
        }
    }

    @Data
    @Builder
    class Result {
        @JsonProperty("role_list")
        private List roleList;

        @JsonProperty("layout_list")
        private List<LayoutListItem> layoutList;

        private ManageGroupDTO layoutManageGroup;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class LayoutListItem {
        @JsonProperty("api_name")
        @JSONField(name = "api_name")
        private final String apiName;
        private final String label;
        @JsonProperty("is_default")
        @JSONField(name = "is_default")
        private final Boolean isDefault;


        public static List<LayoutListItem> fromLayouts(List<ILayout> layoutList) {
            if (CollectionUtils.empty(layoutList)) {
                return Lists.newArrayList();
            }
            return layoutList.stream()
                    .map(layout -> LayoutListItem.of(layout.getName(), layout.getDisplayName(), layout.isDefault()))
                    .collect(Collectors.toList());
        }
    }
}
