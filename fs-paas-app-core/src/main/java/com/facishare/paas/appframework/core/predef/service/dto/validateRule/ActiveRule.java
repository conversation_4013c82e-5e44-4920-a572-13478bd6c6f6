package com.facishare.paas.appframework.core.predef.service.dto.validateRule;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface ActiveRule {
    @Data
    class Arg {
        @JSONField(name = "M1")
        String describeApiName;

        @JSONField(name = "M2")
        String ruleApiName;

        @JSONField(name = "M3")
        @JsonProperty("isActive")
        boolean isActive;
    }
    @Data
    class Result {
        @JSONField(name = "M2")
        private boolean success;
    }
}
