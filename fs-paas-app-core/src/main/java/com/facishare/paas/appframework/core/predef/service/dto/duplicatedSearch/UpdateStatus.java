package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

public interface UpdateStatus {
    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
        private String duplicateSearchRuleApiName;
        private Boolean enable;
        private List<Entity> relatedEnables;
        private List<Entity> relatedRepeatAllowCreateEnables;
        private IDuplicatedSearch.Type type;
        private Integer version;
        private List<IDuplicatedSearch.Transform> transforms;
        private Boolean supportImport;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Entity {
        private String relatedApiName;
        private Boolean value;
    }

    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private IDuplicatedSearch duplicateSearch;
    }
}
