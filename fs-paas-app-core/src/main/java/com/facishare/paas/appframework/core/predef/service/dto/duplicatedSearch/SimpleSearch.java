package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

public interface SimpleSearch {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
        private String relatedApiName;

        private String duplicateRuleApiName;
        private String ruleApiNameIntercepted;
        private Boolean includeObjectDescribes;
        private String keyword;
        private Boolean isNeedDuplicate;
        private Integer pageSize;
        private Integer pageNumber;

        public Boolean getIncludeObjectDescribes() {
            return !Objects.isNull(includeObjectDescribes) && includeObjectDescribes;
        }

        public String getRelatedApiName() {
            return Objects.isNull(relatedApiName) ? "" : relatedApiName;
        }
    }
}
