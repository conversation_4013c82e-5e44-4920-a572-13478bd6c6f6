package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Set;

public interface UpdateOwnerTemporaryRights {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        @JSONField(name = "describe_api_name")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        String objectDescribeApiName;

        @JSONField(name = "object_data_id")
        @JsonProperty("object_data_id")
        @SerializedName("object_data_id")
        String objectDataId;

        @JSONField(name = "owners")
        @SerializedName(value = "owners")
        @JsonProperty(value = "owners")
        Set<String> owners;

        @J<PERSON>NField(name = "scene")
        @SerializedName(value = "scene")
        @JsonProperty(value = "scene")
        String scene;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
        private String errorInfo;
    }

}
