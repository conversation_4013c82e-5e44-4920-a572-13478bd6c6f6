package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.metadata.TeamMember;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

public interface FindRolesPermissionList {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
        private String roleType;
        private boolean includeRoles;
        private Boolean outPermission;
        private String sourceInfo;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private List<RolePermission> rolePermissionList;
        private List<Role> roles;
        private boolean enable;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class RolePermission {
        private String roleType;
        private String objectDisplayName;
        private String objectApiName;
        private String targetRelatedListLabel;
        //770后这个参数被废弃，改为使用 enableRead
        @Deprecated
        private boolean enable;
        //770 相关团队数据权限增加读写
        private String permissionType;
        private String fieldApiName;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Role {
        private String type;
        private String label;

        public static Role fromTeamMemberRole(TeamMember.Role role) {
            if (Objects.isNull(role)) {
                return null;
            }
            return new Role(role.getValue(), role.getLabel());
        }

        public static Role fromOutTeamMemberRole(TeamMember.OuterRole outerRole) {
            if (Objects.isNull(outerRole)) {
                return null;
            }
            return new Role(outerRole.getValue(), outerRole.getLabel());
        }
    }

}
