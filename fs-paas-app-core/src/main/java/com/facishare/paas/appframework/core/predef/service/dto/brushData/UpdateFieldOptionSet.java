package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Created by <PERSON>hao<PERSON>ju on 2022/11/9
 */
public interface UpdateFieldOptionSet {
    @Data
    class Arg {
        private String describeApiName;
        private Set<FieldOptionSetPair> fieldOptionSetPairs;

    }

    @Data
    class FieldOptionSetPair {
        private String fieldName;
        private String optionApiName;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        Collection<ErrorInfo> errorInfos;
    }

    @Data
    class ErrorInfo {
        private final String fieldName;
        private final List<String> message;

        public ErrorInfo(String fieldName) {
            this.fieldName = fieldName;
            this.message = Lists.newArrayList();
        }

        public void addMessage(String msg) {
            message.add(msg);
        }
    }
}
