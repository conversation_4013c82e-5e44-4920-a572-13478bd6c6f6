package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface UpdateRecordType {

    @Data
    class Arg{
        @JSONField(name = "M1")
        String describeApiName;
        @JSONField(name = "M2")
        String record_type;
        String remark;
        @JsonProperty("i18nInfoList")
        List<I18nInfo> i18nInfoList;
    }

    @Data
    class Result{
        @JSONField(name = "M1")
        private Map recordTypeOption;

        @JSONField(name = "M2")
        private boolean success;

        @JSONField(name = "M3")
        private Map data;

        @JSONField(name = "M4")
        private String failMessage;

        @JSONField(name = "M5")
        private Map layout;

        @JSONField(name = "M6")
        private List role_list;

        @JSONField(name = "M7")
        private List layout_list;

        @JSONField(name = "M8")
        private List record_list;

        @JSONField(name = "M9")
        private Map objectDescribe;
    }
}
