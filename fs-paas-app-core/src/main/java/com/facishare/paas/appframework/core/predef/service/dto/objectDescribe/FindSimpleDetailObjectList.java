package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhouwr on 2020/1/7
 */
public interface FindSimpleDetailObjectList {

    @Data
    class Arg {
        private String describeApiName;
        private String actionCode;
        private String sourceInfo;
    }

    @Data
    @Builder
    class Result {
        private List<ObjectDescribeDocument> detailDescribeList;
        private ManageGroupDTO manageGroup;
    }

}
