package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface FindAndDeleteDuplicateRule {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {
        @JsonProperty("describe_api_name")
        private String describeApiName;

        @JsonProperty("duplicate_rule_api_name")
        private String duplicateRuleApiName;

        @JsonProperty("type")
        private IDuplicatedSearch.Type type;

    }

    @Builder
    @Data
    class Result {
        Boolean result;
    }
}
