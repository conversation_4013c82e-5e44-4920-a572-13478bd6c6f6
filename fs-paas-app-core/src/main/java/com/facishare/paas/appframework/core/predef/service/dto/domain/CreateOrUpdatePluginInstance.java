package com.facishare.paas.appframework.core.predef.service.dto.domain;

import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2022/9/20.
 */
public interface CreateOrUpdatePluginInstance {
    @Data
    class Arg {
        private DomainPluginInstance pluginInstance;
        private Map<String, List<Map<String, Object>>> addFields;
        private Map<String, List<String>> layoutApiNames;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        private String id;
    }
}
