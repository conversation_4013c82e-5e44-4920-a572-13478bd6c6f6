package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface FindFieldListConfig {
    @Data
    class Arg {
        @JSONField(name = "M1")
        String objectDescribeApiName;

        @JSONField(name = "M2")
        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        String extendAttribute;

    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        List<Map<String, Object>> list;
    }
}
