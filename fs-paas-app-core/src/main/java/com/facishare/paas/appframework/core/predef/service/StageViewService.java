package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.stageview.CreateStageView;
import com.facishare.paas.appframework.core.predef.service.dto.stageview.DeleteStageView;
import com.facishare.paas.appframework.core.predef.service.dto.stageview.QueryStageView;
import com.facishare.paas.appframework.metadata.StageViewLogicService;
import com.facishare.paas.appframework.metadata.dto.StageViewInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/02/05
 */
@Slf4j
@Component
@ServiceModule("stage_view")
public class StageViewService {
    @Autowired
    private StageViewLogicService stageViewLogicService;

    @ServiceMethod("create")
    public CreateStageView.Result createStageView(CreateStageView.Arg arg, ServiceContext context) {
        stageViewLogicService.saveStageView(arg.getDescribeApiName(), arg.getStageViewInfos(), context.getUser());
        return CreateStageView.Result.buildSuccess();
    }

    @ServiceMethod("query")
    public QueryStageView.Result queryStageView(QueryStageView.Arg arg, ServiceContext context) {
        List<StageViewInfo> stageView = stageViewLogicService.findStageView(arg.getDescribeApiName(), context.getUser());
        return QueryStageView.Result.of(stageView);
    }

    @ServiceMethod("delete")
    public DeleteStageView.Result deleteStageView(DeleteStageView.Arg arg, ServiceContext context) {
        stageViewLogicService.deleteStageView(arg.getDescribeApiName(), context.getUser());
        return DeleteStageView.Result.buildSuccess();
    }
}
