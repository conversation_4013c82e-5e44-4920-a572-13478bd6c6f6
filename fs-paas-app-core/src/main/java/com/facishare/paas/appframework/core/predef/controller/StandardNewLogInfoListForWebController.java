package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.core.predef.service.dto.log.GetNewLogInfoListForWeb;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogType;
import com.facishare.paas.appframework.core.predef.service.dto.log.PageInfo;
import com.facishare.paas.appframework.log.AuditLogConfig;
import com.facishare.paas.appframework.log.dto.LogCondition;
import com.facishare.paas.appframework.log.dto.WebSearchResult;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Desc
 * <AUTHOR>
 * @CreateTime 2019-09-24 16:39
 */
public class StandardNewLogInfoListForWebController extends AbstractStandardNewLogInfoListController<GetNewLogInfoListForWeb.Arg, GetNewLogInfoListForWeb.Result> {

    @Override
    protected IObjectDescribe findDescribe() {
        return serviceFacade.findObjectUseThreadLocalCache(controllerContext.getTenantId(),
                Strings.isNullOrEmpty(arg.getDetailApiName()) ? arg.getApiName() : arg.getDetailApiName());
    }

    @Override
    protected List<LogRecord> getModifyRecordList(GetNewLogInfoListForWeb.Result result) {
        return result.getModifyRecordList();
    }

    @Override
    protected GetNewLogInfoListForWeb.Result doService(GetNewLogInfoListForWeb.Arg arg) {
        WebSearchResult logResult;
        Set<String> unauthorizedFields;
        LogCondition logCondition = LogCondition.builder()
                .operationalType(Optional.ofNullable(arg.getOperationalType()).orElse(""))
                .bizIds(arg.getBizIds())
                .otherBizIds(arg.getOtherBizIds())
                .pageSize(arg.getPageSize())
                .pageNumber(arg.getPageNumber())
                .operationTimeFrom(arg.getOperationTimeFrom())
                .operationTimeTo(arg.getOperationTimeTo())
                .operationTime(arg.getOperationTime())
                .needReturnCount(arg.getNeedReturnCount())
                .hidden(arg.getHidden())
                .build();
        if (Strings.isNullOrEmpty(arg.getDetailApiName())) {
            unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), arg.getApiName());
            if (isGrayCHRead()) {
                logCondition.setModule(arg.getApiName());
                logCondition.setObjectId(arg.getObjectId());
                logResult = serviceFacade.webSearchModifyRecord(controllerContext.getUser(), logCondition);
            } else {
                logResult = serviceFacade.webSearchModifyRecord(arg.getApiName(), arg.getObjectId(),
                        Optional.ofNullable(arg.getOperationalType()).orElse(""), arg.getPageSize(),
                        arg.getPageNumber(), controllerContext.getUser(), arg.getBizIds(), arg.getOtherBizIds());
            }
        } else {
            unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), arg.getDetailApiName());
            if (isGrayCHRead()) {
                logCondition.setModule(arg.getDetailApiName());
                logCondition.setMasterId(arg.getObjectId());
                logCondition.setMasterLogId(arg.getMasterLogId());
                logResult = serviceFacade.webSearchModifyRecordForMaster(controllerContext.getUser(), logCondition);
            } else {
                logResult = serviceFacade.webSearchModifyRecordForMaster(arg.getObjectId(), arg.getDetailApiName(),
                        arg.getMasterLogId(), Optional.ofNullable(arg.getOperationalType()).orElse(""),
                        controllerContext.getUser(), arg.getBizIds(), arg.getOtherBizIds(), arg.getPageSize(), arg.getPageNumber());
            }
        }
        filterSourceDetailSnapshot(logResult.getModifyRecordList(), arg.getSourceId(), arg.getMasterLogId());
        // 过滤字段权限
        filterFieldPermission(logResult.getModifyRecordList(), unauthorizedFields);
        GetNewLogInfoListForWeb.Result result = new GetNewLogInfoListForWeb.Result();
        result.setPageInfo(toPageInfo(logResult.getPageInfo()));
        List<LogRecord> logRecordList = new ArrayList<>(logResult.getModifyRecordList().size());
        logResult.getModifyRecordList().forEach(f -> logRecordList.add(modifyRecordToLogRecord(f)));
        //下游屏蔽多维度修改记录
        removeOutDimensionRecords(logRecordList);
        //隐藏插件定义中配置需要隐藏的字段
        removeFieldsByDomainPlugin(logRecordList);
        //处理字段掩码
        fillMaskFieldValue(logRecordList, objectDescribe, arg.getObjectId(), arg.getApiName());
        fillDimensionFieldValue(logRecordList, objectDescribe, arg.getObjectId(), arg.getApiName());
        fillEmployeeAndDepartmentFieldValue(logRecordList, objectDescribe, arg.getObjectId(), arg.getApiName());
        fillDataVisibilityRangeFieldValue(logRecordList, objectDescribe, arg.getObjectId(), arg.getApiName());
        // 处理相关团队的展示问题
        handleTeamMember(controllerContext.getUser(), logRecordList);
        handleRichTextFieldValue(objectDescribe, logRecordList);
        fillObjectReferenceManyFieldValue(objectDescribe, logRecordList);
        // 处理编辑类型的修改记录
        dealModifyDatas(objectDescribe, logRecordList);
        dealMultiLangData(objectDescribe, logRecordList);
        // 对 log 中 NPath 进行签名 
        dealNPathSign(logRecordList, IFieldType.IMAGE, IFieldType.FILE_ATTACHMENT);
        handleEmployeeMultiLang(controllerContext.getUser(), logRecordList);
        result.setModifyRecordList(logRecordList);
        String tenantQueryInterval = AuditLogConfig.getTenantQueryInterval(controllerContext.getTenantId(), LogType.AUDIT_LOG.getLogType());
        if (StringUtils.isNotBlank(tenantQueryInterval)) {
            result.setQueryInterval(tenantQueryInterval);
        }
        result.setObjectDescribeExtra(getFieldExtra());
        result.setHasMore(BooleanUtils.isTrue(logResult.getHasMore()));
        return result;
    }

    protected PageInfo toPageInfo(com.facishare.paas.appframework.log.dto.PageInfo info) {
        if (Objects.isNull(info)) {
            return null;
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageCount(info.getPageCount());
        pageInfo.setPageNumber(info.getPageNumber());
        pageInfo.setPageSize(info.getPageSize());
        pageInfo.setTotalCount(info.getTotalCount());
        return pageInfo;
    }

}
