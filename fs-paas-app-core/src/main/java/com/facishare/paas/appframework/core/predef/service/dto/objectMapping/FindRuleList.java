package com.facishare.paas.appframework.core.predef.service.dto.objectMapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public interface FindRuleList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("status")
        @SerializedName("status")
        private Integer status;

        @JSONField(name = "M2")
        @JsonProperty("rule_name")
        @SerializedName("rule_name")
        private String ruleName;

        @JSONField(name = "M3")
        @JsonProperty("biz_type")
        @SerializedName("biz_type")
        private String bizType;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        @JsonProperty("ruleList")
        @SerializedName("ruleList")
        private List<MappingRuleDocument> ruleList;
    }
}
