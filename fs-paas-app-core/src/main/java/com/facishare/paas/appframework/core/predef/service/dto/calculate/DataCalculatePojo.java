package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by linqiuying on 17/8/11.
 */
@Data
public class DataCalculatePojo {
    private List<String> api_name_list;
    private String objectDescribeApiName;
    private Map<String, Map<String, Object>> objectDataList;
    boolean calculateFormulaOnly = false;
}
