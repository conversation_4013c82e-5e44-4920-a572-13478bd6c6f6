package com.facishare.paas.appframework.core.predef.service.dto.option;

import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang.BooleanUtils;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/12/15
 */
public interface SaveOptionSet {

    @Data
    class Arg {
        private MtOptionSet option;
        private Boolean onlyUpdateOptions;

        public boolean onlyUpdateOptions() {
            return BooleanUtils.isTrue(onlyUpdateOptions);
        }
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private MtOptionSet option;
    }
}
