package com.facishare.paas.appframework.core.predef.service.dto.dataMultiLang;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface GetSupportFieldByObject {

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    class Arg {
        private String describeApiName;
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    class Result {
        private MultiLangInfo multiLangInfo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class MultiLangInfo {
        String describeApiName;
        String displayName;
        List<GetAllMultiLanguageField.FieldInfo> fieldInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class FieldInfo {
        String fieldApiName;
        String label;
    }
}


