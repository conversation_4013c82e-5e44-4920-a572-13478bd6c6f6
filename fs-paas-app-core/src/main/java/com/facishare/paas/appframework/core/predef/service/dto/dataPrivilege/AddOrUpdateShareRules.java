package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.DataSharing;
import com.facishare.paas.appframework.privilege.dto.RangeOutUser;
import com.facishare.paas.appframework.privilege.dto.RangesExt;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface AddOrUpdateShareRules {
    @Data
    class Arg {
        @JsonProperty("ObjectDescribeApiNames")
        @SerializedName("ObjectDescribeApiNames")
        @JSONField(name = "M1")
        List<String> objectDescribeApiNames;

        @JsonProperty("PermissionType")
        @SerializedName("PermissionType")
        @JSONField(name = "M2")
        Integer permissionType;

        @JsonProperty("SourceCircleIDs")
        @SerializedName("SourceCircleIDs")
        @JSONField(name = "M3")
        List<Integer> sourceCircleIDs;

        @JsonProperty("SourceEmployeeIDs")
        @SerializedName("SourceEmployeeIDs")
        @JSONField(name = "M4")
        List<Integer> sourceEmployeeIDs;

        @JsonProperty("SourceOrganizationIDs")
        @SerializedName("SourceOrganizationIDs")
        List<Integer> sourceOrganizationIDs;

        @JsonProperty("SourceUserGroupIDs")
        @SerializedName("SourceUserGroupIDs")
        @JSONField(name = "M5")
        List<String> sourceUserGroupIDs;

        @JsonProperty("SourceRoleIDs")
        @SerializedName("SourceRoleIDs")
        @JSONField(name = "M9")
        List<String> sourceRoleIDs;

        @JsonProperty("TargetCircleIDs")
        @SerializedName("TargetCircleIDs")
        @JSONField(name = "M6")
        List<Integer> targetCircleIDs;

        @JsonProperty("TargetEmployeeIDs")
        @SerializedName("TargetEmployeeIDs")
        @JSONField(name = "M7")
        List<Integer> targetEmployeeIDs;

        @JsonProperty("TargetOrganizationIDs")
        @SerializedName("TargetOrganizationIDs")
        List<Integer> targetOrganizationIDs;

        @JsonProperty("TargetUserGroupIDs")
        @SerializedName("TargetUserGroupIDs")
        @JSONField(name = "M8")
        List<String> targetUserGroupIDs;

        @JsonProperty("TargetRoleIDs")
        @SerializedName("TargetRoleIDs")
        @JSONField(name = "M8")
        List<String> targetRoleIDs;

        @JsonProperty("TargetOutTenantIDs")
        @SerializedName("TargetOutTenantIDs")
        @JSONField(name = "M9")
        List<String> targetOutTenantIds;

        @JsonProperty("TargetOutTenantGroupIDs")
        @SerializedName("TargetOutTenantGroupIDs")
        @JSONField(name = "M10")
        List<String> targetOutTenantGroupIds;

        @JsonProperty("TargetOutUsers")
        @SerializedName("TargetOutUsers")
        @JSONField(name = "M11")
        List<RangeOutUser> targetOutUsers;

        @JsonProperty("TargetOutRoleIDs")
        @SerializedName("TargetOutRoleIDs")
        @JSONField(name = "M12")
        List<String> targetOutRoleIds;

        int basedType;

        //是否包含子部门 1 包含 0/null  不包含
        private Integer receiveDeptCascade;

        public DataSharing toDataSharing(User user) {
            return DataSharing.builder()
                    .user(user)
                    .permissionType(getPermissionType())
                    .sourceRanges(getSourceRanges())
                    .targetRanges(getTargetRanges())
                    .describeApiNameList(getObjectDescribeApiNames())
                    .receiveDeptCascade(getReceiveDeptCascade())
                    .basedType(basedType)
                    .build();
        }

        private RangesExt getTargetRanges() {
            RangesExt result = new RangesExt();
            result.setUserGroup(CollectionUtils.nullToEmpty(getTargetUserGroupIDs()));
            result.setUser(convertToString(getTargetEmployeeIDs()));
            result.setRole(CollectionUtils.nullToEmpty(getTargetRoleIDs()));
            result.setDepartment(convertToString(getTargetCircleIDs()));
            result.setShareRuleOrgIds(convertToString(getTargetOrganizationIDs()));
            result.setOutTenantGroups(CollectionUtils.nullToEmpty(getTargetOutTenantGroupIds()));
            result.setOutTenantIds(CollectionUtils.nullToEmpty(getTargetOutTenantIds()));
            result.setOutUsersList(CollectionUtils.nullToEmpty(getTargetOutUsers()));
            result.setOutRoles(CollectionUtils.nullToEmpty(getTargetOutRoleIds()));
            return result;
        }

        private RangesExt getSourceRanges() {
            RangesExt result = new RangesExt();
            result.setDepartment(convertToString(getSourceCircleIDs()));
            result.setUser(convertToString(getSourceEmployeeIDs()));
            result.setShareRuleOrgIds(convertToString(getSourceOrganizationIDs()));
            result.setRole(CollectionUtils.nullToEmpty(getSourceRoleIDs()));
            result.setUserGroup(CollectionUtils.nullToEmpty(getSourceUserGroupIDs()));
            return result;
        }

        private List<String> convertToString(List<Integer> sourceCircleIDs) {
            if (CollectionUtils.empty(sourceCircleIDs)) {
                return Collections.emptyList();
            }
            return sourceCircleIDs.stream().map(String::valueOf).collect(Collectors.toList());
        }

    }


    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        List<String> result;

    }
}
