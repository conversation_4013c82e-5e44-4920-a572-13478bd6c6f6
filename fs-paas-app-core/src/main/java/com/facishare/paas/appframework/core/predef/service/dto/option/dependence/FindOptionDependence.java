package com.facishare.paas.appframework.core.predef.service.dto.option.dependence;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/8/1
 */
public interface FindOptionDependence {

    @Data
    class Arg {
        private String describeApiName;
        private String fieldApiName;
        private String childFieldName;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private FieldDependenceDTO fieldDependence;
    }
}
