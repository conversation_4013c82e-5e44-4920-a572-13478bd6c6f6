package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

public interface FindRoleAndLayout {

    @Data
    class Arg{
        @JSONField(name = "M1")
        String describeApiName;

    }

    @Data
    class Result{
        @JSONField(name = "M6")
        private List role_list;

        @JSONField(name = "M7")
        private List layout_list;
    }
}
