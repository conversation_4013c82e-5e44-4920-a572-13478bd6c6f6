package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 迁移数据的MigrationResult类，用于记录迁移结果
 */
@Data
public class MigrationResult implements Serializable {
    private int totalObjects;
    private int successCount;
    private Map<String, String> failedObjects = new HashMap<>();
    private String errorMessage;
    
    public void incrementSuccessCount() {
        this.successCount++;
    }
    
    public void addFailedObject(String apiName, String reason) {
        this.failedObjects.put(apiName, reason);
    }
    
    public double getCompletionRate() {
        return totalObjects > 0 ? (double) successCount / totalObjects : 0;
    }
} 