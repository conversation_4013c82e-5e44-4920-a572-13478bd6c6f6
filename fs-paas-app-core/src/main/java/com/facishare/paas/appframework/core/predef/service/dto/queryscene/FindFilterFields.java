package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface FindFilterFields {
    @Data
    class Arg {
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        private String describeApiName;

        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        private String extendAttribute;

        @JsonProperty("what_api_name")
        @SerializedName("what_api_name")
        private String whatApiName;
    }

    @Builder
    @Data
    class Result {
        @JsonProperty("filter_fields")
        @SerializedName("filter_fields")
        private List<CommonFilterField.FilterField> filterFields;
    }
}
