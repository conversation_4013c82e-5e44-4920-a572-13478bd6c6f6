package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface GetResultHeader {
    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
        private IDuplicatedSearch.Type type;
    }

    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private ObjectDescribeDocument objectDescribe;
        private List<String> includeFields;
    }

}
