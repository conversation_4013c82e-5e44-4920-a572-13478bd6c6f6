package com.facishare.paas.appframework.core.predef.service.dto.aggregate;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface GetAggregateValues {

    @Data
    class Arg {
        private ObjectDataDocument data;
//        private Map<String, List<ObjectDataDocument>> apiNameDetailDataList;
        private List<String> aggregateRuleIds;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        private ObjectDataDocument aggregateValues;
    }

}
