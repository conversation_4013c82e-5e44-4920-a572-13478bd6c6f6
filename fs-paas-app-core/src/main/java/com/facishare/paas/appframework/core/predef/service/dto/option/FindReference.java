package com.facishare.paas.appframework.core.predef.service.dto.option;

import com.facishare.paas.appframework.metadata.options.OptionReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Singular;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/12/15
 */
public interface FindReference {
    @Data
    class Arg {
        @JsonProperty("api_name")
        private String optionApiName;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        @Singular
        private List<OptionReference.SimpleReference> optionReferences;
    }
}
