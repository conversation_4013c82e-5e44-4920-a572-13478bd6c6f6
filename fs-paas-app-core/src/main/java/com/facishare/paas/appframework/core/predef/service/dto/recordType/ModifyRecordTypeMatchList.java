package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.facishare.paas.appframework.metadata.dto.RecordTypeMatchInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface ModifyRecordTypeMatchList {
    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        String describeApiName;
        @JsonProperty("record_list")
        List<RecordTypeMatchInfo> recordList;
    }

    @Data
    class Result {
        String result;
    }
}
