package com.facishare.paas.appframework.core.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ObjectDataDocumentGroovyTest {

    @Test
    void testProjectField() {
        // Given
        List<String> fieldProjection = Arrays.asList("name", "a", "field_no_exist");
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("name", "123");
        dataMap.put("a", 1);
        dataMap.put("b", 23.5);
        ObjectDataDocument data = ObjectDataDocument.of(dataMap);
        List<ObjectDataDocument> dataList = Arrays.asList(data);

        // When
        ObjectDataDocument.projectField(dataList, fieldProjection);

        // Then
        assertTrue(dataList.get(0).containsKey("name"));
        assertTrue(dataList.get(0).containsKey("a"));
        assertFalse(dataList.get(0).containsKey("b"));
    }

    @Test
    void testProjectFieldWithEmptyFieldProjection() {
        // Given
        List<String> fieldProjection = Collections.emptyList();
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("name", "123");
        dataMap.put("a", 1);
        dataMap.put("b", 23.5);
        ObjectDataDocument data = ObjectDataDocument.of(dataMap);
        List<ObjectDataDocument> dataList = Arrays.asList(data);

        // When
        ObjectDataDocument.projectField(dataList, fieldProjection);

        // Then
        assertTrue(dataList.get(0).containsKey("name"));
        assertTrue(dataList.get(0).containsKey("a"));
        assertTrue(dataList.get(0).containsKey("b"));
    }
}
