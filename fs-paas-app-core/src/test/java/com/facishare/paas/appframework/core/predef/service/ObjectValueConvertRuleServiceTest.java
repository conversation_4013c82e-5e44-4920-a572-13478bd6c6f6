package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.convertRule.*;
import com.facishare.paas.appframework.metadata.ObjectConvertRuleService;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectValueConvertRuleService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectValueConvertRuleService单元测试")
class ObjectValueConvertRuleServiceTest {

    @Mock
    private ObjectConvertRuleService objectConvertRuleService;
    
    @InjectMocks
    private ObjectValueConvertRuleService objectValueConvertRuleService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String RULE_API_NAME = "test_rule_api";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "convert_rule", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建转换规则成功的场景
     */
    @Test
    @DisplayName("测试创建转换规则成功")
    void testCreateConvertRuleSuccess() {
        // Arrange
        CreateConvertRule.Arg arg = new CreateConvertRule.Arg();
        
        MappingRuleDocument ruleDoc = new MappingRuleDocument();
        ruleDoc.put("rule_api_name", RULE_API_NAME);
        ruleDoc.put("rule_name", "Test Rule");
        
        List<MappingRuleDocument> ruleList = Arrays.asList(ruleDoc);
        arg.setRuleList(ruleList);

        doNothing().when(objectConvertRuleService).create(eq(user), any(List.class));

        // Act
        CreateConvertRule.Result result = objectValueConvertRuleService.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(objectConvertRuleService).create(eq(user), any(List.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建转换规则时规则列表为空的场景
     */
    @Test
    @DisplayName("测试创建转换规则 - 规则列表为空")
    void testCreateConvertRuleWithEmptyRuleList() {
        // Arrange
        CreateConvertRule.Arg arg = new CreateConvertRule.Arg();
        arg.setRuleList(Collections.emptyList());

        doNothing().when(objectConvertRuleService).create(eq(user), any(List.class));

        // Act
        CreateConvertRule.Result result = objectValueConvertRuleService.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(objectConvertRuleService).create(eq(user), any(List.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新转换规则成功的场景
     */
    @Test
    @DisplayName("测试更新转换规则成功")
    void testUpdateConvertRuleSuccess() {
        // Arrange
        UpdateConvertRule.Arg arg = new UpdateConvertRule.Arg();
        
        MappingRuleDocument ruleDoc = new MappingRuleDocument();
        ruleDoc.put("rule_api_name", RULE_API_NAME);
        ruleDoc.put("rule_name", "Updated Rule");
        
        List<MappingRuleDocument> ruleList = Arrays.asList(ruleDoc);
        arg.setRuleList(ruleList);

        doNothing().when(objectConvertRuleService).update(eq(user), any(List.class));

        // Act
        UpdateConvertRule.Result result = objectValueConvertRuleService.update(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(objectConvertRuleService).update(eq(user), any(List.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用或禁用转换规则成功的场景
     */
    @Test
    @DisplayName("测试启用或禁用转换规则成功")
    void testEnableOrDisableConvertRuleSuccess() {
        // Arrange
        EnableOrDisableConvertRule.Arg arg = new EnableOrDisableConvertRule.Arg();
        arg.setRuleApiName(RULE_API_NAME);
        arg.setStatus(1); // 启用

        doNothing().when(objectConvertRuleService).updateStatus(user, RULE_API_NAME, 1);

        // Act
        EnableOrDisableConvertRule.Result result = objectValueConvertRuleService.isActive(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(objectConvertRuleService).updateStatus(user, RULE_API_NAME, 1);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除转换规则成功的场景
     */
    @Test
    @DisplayName("测试删除转换规则成功")
    void testDeleteConvertRuleSuccess() {
        // Arrange
        DeleteConvertRule.Arg arg = new DeleteConvertRule.Arg();
        arg.setRuleApiName(RULE_API_NAME);

        doNothing().when(objectConvertRuleService).delete(user, RULE_API_NAME);

        // Act
        DeleteConvertRule.Result result = objectValueConvertRuleService.delete(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(objectConvertRuleService).delete(user, RULE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找规则列表成功的场景
     */
    @Test
    @DisplayName("测试查找规则列表成功")
    void testFindRuleListSuccess() {
        // Arrange
        FindConvertRuleList.Arg arg = new FindConvertRuleList.Arg();
        arg.setRuleName("Test Rule");
        arg.setSearchQueryInfo("search info");

        ObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo();
        ruleInfo.setRuleApiName(RULE_API_NAME);
        List<IObjectMappingRuleInfo> rules = Arrays.asList((IObjectMappingRuleInfo)ruleInfo);

        when(objectConvertRuleService.findRuleList(user, "Test Rule", "search info"))
                .thenReturn(rules);

        // Act
        FindConvertRuleList.Result result = objectValueConvertRuleService.findRuleList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRuleList());
        assertEquals(1, result.getRuleList().size());
        verify(objectConvertRuleService).findRuleList(user, "Test Rule", "search info");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称查找规则成功的场景
     */
    @Test
    @DisplayName("测试根据API名称查找规则成功")
    void testFindByApiNameSuccess() {
        // Arrange
        FindByApiName.Arg arg = new FindByApiName.Arg();
        arg.setRuleApiName(RULE_API_NAME);

        ObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo();
        ruleInfo.setRuleApiName(RULE_API_NAME);
        List<IObjectMappingRuleInfo> rules = Arrays.asList((IObjectMappingRuleInfo)ruleInfo);

        when(objectConvertRuleService.findConvertRuleByApiName(user, RULE_API_NAME))
                .thenReturn(rules);

        // Act
        FindByApiName.Result result = objectValueConvertRuleService.findByApiName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRuleList());
        assertEquals(1, result.getRuleList().size());
        verify(objectConvertRuleService).findConvertRuleByApiName(user, RULE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否支持转换规则成功的场景
     */
    @Test
    @DisplayName("测试检查是否支持转换规则成功")
    void testIsSupportConvertRuleSuccess() {
        // Arrange
        when(objectConvertRuleService.supportConvertRule(TENANT_ID)).thenReturn(true);

        // Act
        SupportConvertRule.Result result = objectValueConvertRuleService.isSupportConvertRule(serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(objectConvertRuleService).supportConvertRule(TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否支持转换规则返回false的场景
     */
    @Test
    @DisplayName("测试检查是否支持转换规则 - 不支持")
    void testIsSupportConvertRuleNotSupported() {
        // Arrange
        when(objectConvertRuleService.supportConvertRule(TENANT_ID)).thenReturn(false);

        // Act
        SupportConvertRule.Result result = objectValueConvertRuleService.isSupportConvertRule(serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(objectConvertRuleService).supportConvertRule(TENANT_ID);
    }
}
