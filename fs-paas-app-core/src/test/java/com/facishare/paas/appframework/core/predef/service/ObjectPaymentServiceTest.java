package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.PaymentException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.payment.GetPaymentUrl;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.payment.PaymentService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Optional;

import static com.facishare.paas.metadata.api.describe.Payment.PAY_STATUS_COMPLETE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectPaymentService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectPaymentService单元测试")
class ObjectPaymentServiceTest {

    @Mock
    private ServiceFacade serviceFacade;
    
    @InjectMocks
    private ObjectPaymentService objectPaymentService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OBJECT_API_NAME = "test_object__c";
    private final String DATA_ID = "test_data_id";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "payment", "find_payment_list");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支付URL成功的场景
     */
    @Test
    @DisplayName("测试获取支付URL成功")
    void testGetPaymentUrlSuccess() {
        // Arrange
        GetPaymentUrl.Arg arg = new GetPaymentUrl.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setObjectDataId(DATA_ID);

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);
        
        IObjectData mockData = new ObjectData();
        mockData.set("name", "Test Order");
        mockData.set("pay_status", "pending");
        mockData.set("pay_amount", new BigDecimal("100.50"));

        PaymentFieldDescribe paymentFieldDescribe = new PaymentFieldDescribe();
        paymentFieldDescribe.setPayStatusFieldApiName("pay_status");
        paymentFieldDescribe.setPayAmountFieldApiName("pay_amount");

        String expectedPaymentUrl = "https://payment.example.com/pay?order=123";

        when(serviceFacade.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectData(eq(TENANT_ID), eq(DATA_ID), eq(mockDescribe))).thenReturn(mockData);
        when(serviceFacade.getPaymentUrl(any(PaymentService.GetPaymentUrlArg.class))).thenReturn(expectedPaymentUrl);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedStatic.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPaymentFieldDescribe()).thenReturn(Optional.of(paymentFieldDescribe));

            // Act
            GetPaymentUrl.Result result = objectPaymentService.getPaymentUrl(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertEquals(expectedPaymentUrl, result.getPaymentUrl());
            verify(serviceFacade).findObject(TENANT_ID, OBJECT_API_NAME);
            verify(serviceFacade).findObjectData(eq(TENANT_ID), eq(DATA_ID), eq(mockDescribe));
            verify(serviceFacade).getPaymentUrl(any(PaymentService.GetPaymentUrlArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支付URL时没有支付字段描述的场景
     */
    @Test
    @DisplayName("测试获取支付URL - 没有支付字段描述")
    void testGetPaymentUrlWithNoPaymentFieldDescribe() {
        // Arrange
        GetPaymentUrl.Arg arg = new GetPaymentUrl.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setObjectDataId(DATA_ID);

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);

        when(serviceFacade.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedStatic.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPaymentFieldDescribe()).thenReturn(Optional.empty());

            // Act
            GetPaymentUrl.Result result = objectPaymentService.getPaymentUrl(arg, serviceContext);

            // Assert
            assertNull(result);
            verify(serviceFacade).findObject(TENANT_ID, OBJECT_API_NAME);
            verify(serviceFacade).findObjectData(TENANT_ID, DATA_ID, mockDescribe);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支付URL时订单已支付的异常场景
     */
    @Test
    @DisplayName("测试获取支付URL - 订单已支付异常")
    void testGetPaymentUrlWithCompletedPayment() {
        // Arrange
        GetPaymentUrl.Arg arg = new GetPaymentUrl.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setObjectDataId(DATA_ID);

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);
        
        IObjectData mockData = new ObjectData();
        mockData.set("pay_status", PAY_STATUS_COMPLETE);
        mockData.set("pay_amount", new BigDecimal("100.50"));

        PaymentFieldDescribe paymentFieldDescribe = new PaymentFieldDescribe();
        paymentFieldDescribe.setPayStatusFieldApiName("pay_status");
        paymentFieldDescribe.setPayAmountFieldApiName("pay_amount");

        when(serviceFacade.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectData(eq(TENANT_ID), eq(DATA_ID), eq(mockDescribe))).thenReturn(mockData);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedStatic.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPaymentFieldDescribe()).thenReturn(Optional.of(paymentFieldDescribe));

            // Act & Assert
            assertThrows(PaymentException.class, () -> {
                objectPaymentService.getPaymentUrl(arg, serviceContext);
            });
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支付URL时收款金额为空的异常场景
     */
    @Test
    @DisplayName("测试获取支付URL - 收款金额为空异常")
    void testGetPaymentUrlWithNullAmount() {
        // Arrange
        GetPaymentUrl.Arg arg = new GetPaymentUrl.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setObjectDataId(DATA_ID);

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);
        
        IObjectData mockData = new ObjectData();
        mockData.set("pay_status", "pending");
        mockData.set("pay_amount", null);

        PaymentFieldDescribe paymentFieldDescribe = new PaymentFieldDescribe();
        paymentFieldDescribe.setPayStatusFieldApiName("pay_status");
        paymentFieldDescribe.setPayAmountFieldApiName("pay_amount");

        when(serviceFacade.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectData(eq(TENANT_ID), eq(DATA_ID), eq(mockDescribe))).thenReturn(mockData);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedStatic.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPaymentFieldDescribe()).thenReturn(Optional.of(paymentFieldDescribe));

            // Act & Assert
            assertThrows(PaymentException.class, () -> {
                objectPaymentService.getPaymentUrl(arg, serviceContext);
            });
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支付URL时收款金额非法的异常场景
     */
    @Test
    @DisplayName("测试获取支付URL - 收款金额非法异常")
    void testGetPaymentUrlWithIllegalAmount() {
        // Arrange
        GetPaymentUrl.Arg arg = new GetPaymentUrl.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setObjectDataId(DATA_ID);

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);
        
        IObjectData mockData = new ObjectData();
        mockData.set("pay_status", "pending");
        mockData.set("pay_amount", new BigDecimal("-10.00")); // 负数金额

        PaymentFieldDescribe paymentFieldDescribe = new PaymentFieldDescribe();
        paymentFieldDescribe.setPayStatusFieldApiName("pay_status");
        paymentFieldDescribe.setPayAmountFieldApiName("pay_amount");

        when(serviceFacade.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectData(eq(TENANT_ID), eq(DATA_ID), eq(mockDescribe))).thenReturn(mockData);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedStatic.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPaymentFieldDescribe()).thenReturn(Optional.of(paymentFieldDescribe));

            // Act & Assert
            assertThrows(PaymentException.class, () -> {
                objectPaymentService.getPaymentUrl(arg, serviceContext);
            });
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支付URL时收款金额为零的异常场景
     */
    @Test
    @DisplayName("测试获取支付URL - 收款金额为零异常")
    void testGetPaymentUrlWithZeroAmount() {
        // Arrange
        GetPaymentUrl.Arg arg = new GetPaymentUrl.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setObjectDataId(DATA_ID);

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);
        
        IObjectData mockData = new ObjectData();
        mockData.set("pay_status", "pending");
        mockData.set("pay_amount", BigDecimal.ZERO);

        PaymentFieldDescribe paymentFieldDescribe = new PaymentFieldDescribe();
        paymentFieldDescribe.setPayStatusFieldApiName("pay_status");
        paymentFieldDescribe.setPayAmountFieldApiName("pay_amount");

        when(serviceFacade.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findObjectData(eq(TENANT_ID), eq(DATA_ID), eq(mockDescribe))).thenReturn(mockData);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedStatic.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPaymentFieldDescribe()).thenReturn(Optional.of(paymentFieldDescribe));

            // Act & Assert
            assertThrows(PaymentException.class, () -> {
                objectPaymentService.getPaymentUrl(arg, serviceContext);
            });
        }
    }
}
