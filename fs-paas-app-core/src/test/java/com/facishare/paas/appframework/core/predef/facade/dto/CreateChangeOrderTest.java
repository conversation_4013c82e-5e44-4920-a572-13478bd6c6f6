package com.facishare.paas.appframework.core.predef.facade.dto;

import com.facishare.paas.appframework.core.predef.facade.ObjectDataMapping;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class CreateChangeOrderTest {

    @Mock
    private IObjectDescribe originalDescribe;

    @Mock
    private IObjectDescribe changeDescribe;

    @Mock
    private IObjectData originalData;

    @Mock
    private IObjectData changeObjectData;

    @Mock
    private IObjectData masterData;

    @Mock
    private MtChangeOrderRule.ObjectFieldMapper objectFieldMapper;

    @Mock
    private ObjectDataMapping.MappingData mappingData;

    @BeforeEach
    void setUp() {
        // 移除不必要的全局stubbing，将它们移动到具体需要的测试方法中
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CreateChangeOrder.Arg.toOriginalDataTable方法正常转换为Table结构
     */
    @Test
    @DisplayName("正常场景 - 转换为原始数据Table")
    void testArgToOriginalDataTableSuccess() {
        // 配置需要的Mock行为
        when(originalDescribe.getApiName()).thenReturn("originalApiName");
        when(originalData.getId()).thenReturn("originalDataId");

        // 准备测试数据
        CreateChangeOrder.Arg arg = new CreateChangeOrder.Arg();
        arg.setDescribe(originalDescribe);
        arg.setOriginalData(originalData);

        Map<String, List<IObjectData>> originalDetails = Maps.newHashMap();
        IObjectData detailData = new ObjectData();
        detailData.setId("detailDataId");
        originalDetails.put("detailApiName", Lists.newArrayList(detailData));
        arg.setOriginalDetails(originalDetails);

        // 执行被测试方法
        Table<String, String, IObjectData> result = arg.toOriginalDataTable();

        // 验证结果
        assertNotNull(result);
        assertEquals(originalData, result.get("originalApiName", "originalDataId"));
        assertEquals(detailData, result.get("detailApiName", "detailDataId"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CreateChangeOrder.Result.buildSuccess方法正常构建成功结果
     */
    @Test
    @DisplayName("正常场景 - 构建成功结果")
    void testResultBuildSuccessSuccess() {
        // 执行被测试方法
        CreateChangeOrder.Result result = CreateChangeOrder.Result.buildSuccess("changeApiName", "changeDataId");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getMessage());
        assertEquals("changeApiName", result.getChangeOrderApiName());
        assertEquals("changeDataId", result.getChangeOrderDataId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CreateChangeOrder.Result.buildError方法正常构建错误结果
     */
    @Test
    @DisplayName("正常场景 - 构建错误结果")
    void testResultBuildErrorSuccess() {
        // 执行被测试方法
        CreateChangeOrder.Result result = CreateChangeOrder.Result.buildError("错误信息");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("错误信息", result.getMessage());
        assertNull(result.getChangeOrderApiName());
        assertNull(result.getChangeOrderDataId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CreateChangeOrder.ChangeOrderDataTuple.of静态方法正常创建元组
     */
    @Test
    @DisplayName("正常场景 - 创建变更单数据元组")
    void testChangeOrderDataTupleOfSuccess() {
        // 执行被测试方法
        CreateChangeOrder.ChangeOrderDataTuple tuple = CreateChangeOrder.ChangeOrderDataTuple.of(changeObjectData, originalData);

        // 验证结果
        assertNotNull(tuple);
        assertEquals(changeObjectData, tuple.getChangeObjectData());
        assertEquals(originalData, tuple.getOriginalObjectData());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CreateChangeOrder.ChangeOrderDataTuple.buildChangeObjectDataByRule方法正常构建变更对象数据
     */
    @Test
    @DisplayName("正常场景 - 根据规则构建变更对象数据")
    void testChangeOrderDataTupleBuildChangeObjectDataByRuleSuccess() {
        // 准备测试数据
        String changedType = CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_UPDATE;
        BiFunction<String, IObjectData, ObjectDataMapping.MappingData> triMappingFunction =
                (apiName, objectData) -> mappingData;

        ObjectDataExt objectDataExt = mock(ObjectDataExt.class);
        ObjectDescribeExt describeExt = mock(ObjectDescribeExt.class);
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("testField", "testValue");

        // 配置Mock行为
        try (MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<ObjectDataExt> objectDataExtMock = mockStatic(ObjectDataExt.class)) {

            describeExtMock.when(() -> ObjectDescribeExt.of(changeDescribe)).thenReturn(describeExt);
            objectDataExtMock.when(() -> ObjectDataExt.of(any(IObjectData.class))).thenReturn(objectDataExt);
            // 重要：为ObjectDataExt.of(Map)也提供Mock，确保diffData不为null
            ObjectDataExt mockDiffData = mock(ObjectDataExt.class);
            objectDataExtMock.when(() -> ObjectDataExt.of(any(Map.class))).thenReturn(mockDiffData);

            lenient().when(mappingData.getObjectData()).thenReturn(changeObjectData);
            lenient().when(mappingData.isChange()).thenReturn(true);
            when(describeExt.isSlaveObject()).thenReturn(false);
            lenient().when(objectDataExt.toMap()).thenReturn(dataMap);
            lenient().when(objectDataExt.diff(any(IObjectData.class), any(IObjectDescribe.class))).thenReturn(dataMap);
            // 修复：originalApiName来自objectData.getDescribeApiName()，在测试中objectData是masterData
            when(masterData.getDescribeApiName()).thenReturn("originalApiName");
            when(originalData.get("testField")).thenReturn("originalValue");
            // 使用lenient()来避免严格stubbing问题
            lenient().when(objectFieldMapper.recordOriginalValueBySource(anyString(), anyString())).thenReturn(true);

            // 执行被测试方法
            CreateChangeOrder.ChangeOrderDataTuple result = CreateChangeOrder.ChangeOrderDataTuple.buildChangeObjectDataByRule(
                    objectFieldMapper, changeDescribe, masterData, originalData, changedType, triMappingFunction);

            // 验证结果
            assertNotNull(result);
            assertEquals(changeObjectData, result.getChangeObjectData());
        }

        // 验证Mock交互
        verify(mappingData, times(2)).getObjectData();
        verify(objectFieldMapper).recordOriginalValueBySource("originalApiName", "testField");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CreateChangeOrder.ChangeOrderDataTuple.buildChangeObjectDataByRule方法处理从对象场景
     */
    @Test
    @DisplayName("正常场景 - 根据规则构建从对象变更数据")
    void testChangeOrderDataTupleBuildChangeObjectDataByRuleForSlaveObjectSuccess() {
        // 配置需要的Mock行为
        when(originalData.getId()).thenReturn("originalDataId");

        // 准备测试数据
        String changedType = CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_ADD;
        BiFunction<String, IObjectData, ObjectDataMapping.MappingData> triMappingFunction =
                (apiName, objectData) -> mappingData;

        ObjectDataExt objectDataExt = mock(ObjectDataExt.class);
        ObjectDescribeExt describeExt = mock(ObjectDescribeExt.class);
        IFieldDescribe fieldDescribe = mock(IFieldDescribe.class);

        when(masterData.getOrderBy()).thenReturn(1);

        // 配置Mock行为
        try (MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<ObjectDataExt> objectDataExtMock = mockStatic(ObjectDataExt.class)) {

            describeExtMock.when(() -> ObjectDescribeExt.of(changeDescribe)).thenReturn(describeExt);
            objectDataExtMock.when(() -> ObjectDataExt.of(any(IObjectData.class))).thenReturn(objectDataExt);
            // 重要：为ObjectDataExt.of(Map)也提供Mock，确保diffData不为null
            ObjectDataExt mockDiffData = mock(ObjectDataExt.class);
            objectDataExtMock.when(() -> ObjectDataExt.of(any(Map.class))).thenReturn(mockDiffData);

            lenient().when(mappingData.getObjectData()).thenReturn(changeObjectData);
            lenient().when(mappingData.isChange()).thenReturn(true);
            when(describeExt.isSlaveObject()).thenReturn(true);
            // 使用lenient()来避免不必要的stubbing警告
            lenient().when(describeExt.getActiveFieldDescribeSilently(ObjectDataExt.ORIGINAL_DETAIL_DATA))
                    .thenReturn(java.util.Optional.of(fieldDescribe));
            lenient().when(fieldDescribe.getApiName()).thenReturn(ObjectDataExt.ORIGINAL_DETAIL_DATA);
            when(objectDataExt.toMap()).thenReturn(Maps.newHashMap());
            // 修复：diff方法应该返回一个有效的Map，而不是空Map，因为ObjectDataExt.of()需要处理这个结果
            Map<String, Object> diffMap = Maps.newHashMap();
            diffMap.put("testField", "testValue");
            when(objectDataExt.diff(any(IObjectData.class), any(IObjectDescribe.class))).thenReturn(diffMap);

            // 执行被测试方法
            CreateChangeOrder.ChangeOrderDataTuple result = CreateChangeOrder.ChangeOrderDataTuple.buildChangeObjectDataByRule(
                    objectFieldMapper, changeDescribe, masterData, originalData, changedType, triMappingFunction);

            // 验证结果
            assertNotNull(result);
            assertEquals(changeObjectData, result.getChangeObjectData());
        }

        // 验证Mock交互
        verify(changeObjectData).setOrderBy(1);
        verify(changeObjectData).set(ObjectDataExt.CHANGED_TYPE, changedType);
        verify(changeObjectData).set(ObjectDataExt.ORIGINAL_DETAIL_DATA, "originalDataId");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CreateChangeOrder.ChangeOrderDataTuple.buildChangeObjectDataByRule方法处理原始数据为null的场景
     */
    @Test
    @DisplayName("正常场景 - 原始数据为null时构建变更对象数据")
    void testChangeOrderDataTupleBuildChangeObjectDataByRuleWithNullOriginalDataSuccess() {
        // 准备测试数据
        String changedType = CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_ADD;
        BiFunction<String, IObjectData, ObjectDataMapping.MappingData> triMappingFunction =
                (apiName, objectData) -> mappingData;

        ObjectDescribeExt describeExt = mock(ObjectDescribeExt.class);

        // 配置Mock行为
        try (MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class)) {

            describeExtMock.when(() -> ObjectDescribeExt.of(changeDescribe)).thenReturn(describeExt);

            when(mappingData.getObjectData()).thenReturn(changeObjectData);
            when(describeExt.isSlaveObject()).thenReturn(false);

            // 执行被测试方法
            CreateChangeOrder.ChangeOrderDataTuple result = CreateChangeOrder.ChangeOrderDataTuple.buildChangeObjectDataByRule(
                    objectFieldMapper, changeDescribe, masterData, null, changedType, triMappingFunction);

            // 验证结果
            assertNotNull(result);
            assertEquals(changeObjectData, result.getChangeObjectData());
            assertNull(result.getOriginalObjectData());
        }

        // 验证Mock交互
        verify(mappingData).getObjectData();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CreateChangeOrder常量定义的正确性
     */
    @Test
    @DisplayName("正常场景 - 验证常量定义")
    void testChangeOrderDataTupleConstantsSuccess() {
        // 验证常量值
        assertEquals("add", CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_ADD);
        assertEquals("deleted", CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_DELETED);
        assertEquals("update", CreateChangeOrder.ChangeOrderDataTuple.CHANGED_TYPE_UPDATE);
    }
} 