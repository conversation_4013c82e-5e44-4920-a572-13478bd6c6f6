package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.changeorder.*;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.changeorder.*;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectChangeOrderService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectChangeOrderService单元测试")
class ObjectChangeOrderServiceTest {

    @Mock
    private ChangeOrderLogicService changeOrderLogicService;
    
    @Mock
    private ChangeOrderRuleLogicService changeOrderRuleLogicService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private LicenseService licenseService;
    
    @InjectMocks
    private ObjectChangeOrderService objectChangeOrderService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "test_object__c";
    private final String RULE_API_NAME = "test_rule";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "change_order", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找支持变更顺序的对象成功的正常场景
     */
    @Test
    @DisplayName("测试findSupportObjects成功")
    void testFindSupportObjectsSuccess() {
        // Arrange
        DescribeInfo describeInfo1 = mock(DescribeInfo.class);
        DescribeInfo describeInfo2 = mock(DescribeInfo.class);
        List<DescribeInfo> mockDescribeInfos = Arrays.asList(describeInfo1, describeInfo2);

        when(changeOrderLogicService.findSupportChangeOrderDescribes(TENANT_ID)).thenReturn(mockDescribeInfos);

        // Act
        FindDescribeApiNames.Result result = objectChangeOrderService.findSupportObjects(serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getObjectList());
        assertEquals(2, result.getObjectList().size());
        verify(changeOrderLogicService).findSupportChangeOrderDescribes(TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据原始API名称查找描述成功的正常场景
     */
    @Test
    @DisplayName("测试findDescribesByOriginalApiName成功")
    void testFindDescribesByOriginalApiNameSuccess() {
        // Arrange
        FindDescribesByOriginalApiName.Arg arg = new FindDescribesByOriginalApiName.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // 返回null来测试null处理逻辑
        when(changeOrderLogicService.findDescribesByOriginalApiName(user, DESCRIBE_API_NAME)).thenReturn(null);

        // Act
        FindDescribesByOriginalApiName.Result result = objectChangeOrderService.findDescribesByOriginalApiName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getChangeOrderDescribes());
        verify(changeOrderLogicService).findDescribesByOriginalApiName(user, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试开启变更顺序成功的正常场景
     */
    @Test
    @DisplayName("测试openChangeOrder成功")
    void testOpenChangeOrderSuccess() {
        // Arrange
        OpenChangeOrder.Arg arg = new OpenChangeOrder.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        OpenChangeOrderResult mockResult = mock(OpenChangeOrderResult.class);
        when(mockResult.success()).thenReturn(true);
        when(changeOrderLogicService.openChangeOrder(user, DESCRIBE_API_NAME)).thenReturn(mockResult);

        // Act
        OpenChangeOrder.Result result = objectChangeOrderService.openChangeOrder(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(changeOrderLogicService).openChangeOrder(user, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试开启变更顺序失败的场景
     */
    @Test
    @DisplayName("测试openChangeOrder失败")
    void testOpenChangeOrderFailure() {
        // Arrange
        OpenChangeOrder.Arg arg = new OpenChangeOrder.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        OpenChangeOrderResult mockResult = mock(OpenChangeOrderResult.class);
        when(mockResult.success()).thenReturn(false);
        when(changeOrderLogicService.openChangeOrder(user, DESCRIBE_API_NAME)).thenReturn(mockResult);

        // Act
        OpenChangeOrder.Result result = objectChangeOrderService.openChangeOrder(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNotNull(result.getOpenChangeOrderResult());
        verify(changeOrderLogicService).openChangeOrder(user, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试关闭变更顺序成功的正常场景
     */
    @Test
    @DisplayName("测试closeChangeOrder成功")
    void testCloseChangeOrderSuccess() {
        // Arrange
        OpenChangeOrder.Arg arg = new OpenChangeOrder.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        doNothing().when(changeOrderLogicService).closeChangeOrder(user, DESCRIBE_API_NAME);

        // Act
        OpenChangeOrder.Result result = objectChangeOrderService.closeChangeOrder(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(changeOrderLogicService).closeChangeOrder(user, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查变更规则数量成功的正常场景
     */
    @Test
    @DisplayName("测试checkChangeRuleCount成功")
    void testCheckChangeRuleCountSuccess() {
        // Arrange
        OpenChangeOrder.Arg arg = new OpenChangeOrder.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        doNothing().when(changeOrderRuleLogicService).checkCount(user, DESCRIBE_API_NAME);

        // Act
        OpenChangeOrder.Result result = objectChangeOrderService.checkChangeRuleCount(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(changeOrderRuleLogicService).checkCount(user, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找所有变更规则成功的正常场景
     */
    @Test
    @DisplayName("测试findChangeRules成功")
    void testFindChangeRulesSuccess() {
        // Arrange
        MtChangeOrderRule rule1 = mock(MtChangeOrderRule.class);
        MtChangeOrderRule rule2 = mock(MtChangeOrderRule.class);
        List<MtChangeOrderRule> mockRules = Arrays.asList(rule1, rule2);

        when(rule1.getChangeDescribeApiName()).thenReturn("change1");
        when(rule1.getOriginalDescribeApiName()).thenReturn("original1");
        when(rule2.getChangeDescribeApiName()).thenReturn("change2");
        when(rule2.getOriginalDescribeApiName()).thenReturn("original2");

        Map<String, String> mockDisplayNames = new HashMap<>();
        mockDisplayNames.put("change1", "变更对象1");
        mockDisplayNames.put("original1", "原始对象1");
        mockDisplayNames.put("change2", "变更对象2");
        mockDisplayNames.put("original2", "原始对象2");

        when(changeOrderRuleLogicService.findAll(user)).thenReturn(mockRules);
        when(describeLogicService.findDisplayNameByApiNames(eq(TENANT_ID), any())).thenReturn(mockDisplayNames);

        // Act
        FindChangeRule.FindAllResult result = objectChangeOrderService.findChangeRules(serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getChangeRules());
        assertEquals(2, result.getChangeRules().size());
        verify(changeOrderRuleLogicService).findAll(user);
        verify(describeLogicService).findDisplayNameByApiNames(eq(TENANT_ID), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据规则名称查找变更规则成功的正常场景
     */
    @Test
    @DisplayName("测试findChangeRule成功")
    void testFindChangeRuleSuccess() {
        // Arrange
        FindChangeRule.Arg arg = new FindChangeRule.Arg();
        arg.setRuleApiName(RULE_API_NAME);

        MtChangeOrderRule mockRule = mock(MtChangeOrderRule.class);
        when(changeOrderRuleLogicService.findByRuleName(user, RULE_API_NAME, true))
                .thenReturn(Optional.of(mockRule));

        // Act
        FindChangeRule.FindOneResult result = objectChangeOrderService.findChangeRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockRule, result.getChangeRule());
        verify(changeOrderRuleLogicService).findByRuleName(user, RULE_API_NAME, true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据规则名称查找变更规则不存在时抛出异常
     */
    @Test
    @DisplayName("测试findChangeRule规则不存在抛出异常")
    void testFindChangeRuleNotFoundThrowsException() {
        // Arrange
        FindChangeRule.Arg arg = new FindChangeRule.Arg();
        arg.setRuleApiName(RULE_API_NAME);

        when(changeOrderRuleLogicService.findByRuleName(user, RULE_API_NAME, true))
                .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            objectChangeOrderService.findChangeRule(arg, serviceContext);
        });
        verify(changeOrderRuleLogicService).findByRuleName(user, RULE_API_NAME, true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建变更规则成功的正常场景
     */
    @Test
    @DisplayName("测试createChangeRule成功")
    void testCreateChangeRuleSuccess() {
        // Arrange
        SaveChangeRule.Arg arg = new SaveChangeRule.Arg();
        MtChangeOrderRule mockRule = mock(MtChangeOrderRule.class);
        arg.setChangeRule(mockRule);

        doNothing().when(changeOrderRuleLogicService).create(user, mockRule);

        // Act
        SaveChangeRule.Result result = objectChangeOrderService.createChangeRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(changeOrderRuleLogicService).create(user, mockRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新变更规则成功的正常场景
     */
    @Test
    @DisplayName("测试updateChangeRule成功")
    void testUpdateChangeRuleSuccess() {
        // Arrange
        SaveChangeRule.Arg arg = new SaveChangeRule.Arg();
        MtChangeOrderRule mockRule = mock(MtChangeOrderRule.class);
        arg.setChangeRule(mockRule);

        doNothing().when(changeOrderRuleLogicService).update(user, mockRule);

        // Act
        SaveChangeRule.Result result = objectChangeOrderService.updateChangeRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(changeOrderRuleLogicService).update(user, mockRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用变更规则成功的正常场景
     */
    @Test
    @DisplayName("测试enableChangeRule成功")
    void testEnableChangeRuleSuccess() {
        // Arrange
        FindChangeRule.Arg arg = new FindChangeRule.Arg();
        arg.setRuleApiName(RULE_API_NAME);

        doNothing().when(changeOrderRuleLogicService).enable(user, RULE_API_NAME);

        // Act
        FindChangeRule.Result result = objectChangeOrderService.enableChangeRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(changeOrderRuleLogicService).enable(user, RULE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用变更规则成功的正常场景
     */
    @Test
    @DisplayName("测试disableChangeRule成功")
    void testDisableChangeRuleSuccess() {
        // Arrange
        FindChangeRule.Arg arg = new FindChangeRule.Arg();
        arg.setRuleApiName(RULE_API_NAME);

        doNothing().when(changeOrderRuleLogicService).disable(user, RULE_API_NAME);

        // Act
        FindChangeRule.Result result = objectChangeOrderService.disableChangeRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(changeOrderRuleLogicService).disable(user, RULE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除变更规则成功的正常场景
     */
    @Test
    @DisplayName("测试deleteChangeRule成功")
    void testDeleteChangeRuleSuccess() {
        // Arrange
        FindChangeRule.Arg arg = new FindChangeRule.Arg();
        arg.setRuleApiName(RULE_API_NAME);

        doNothing().when(changeOrderRuleLogicService).delete(user, RULE_API_NAME);

        // Act
        FindChangeRule.Result result = objectChangeOrderService.deleteChangeRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(changeOrderRuleLogicService).delete(user, RULE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试同步变更字段成功的正常场景
     */
    @Test
    @DisplayName("测试syncChangeFieldsByOriginalDescribe成功")
    void testSyncChangeFieldsByOriginalDescribeSuccess() {
        // Arrange
        OpenChangeOrder.Arg arg = new OpenChangeOrder.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        doNothing().when(changeOrderLogicService).syncChangeOrderDescribeWithOriginalDescribe(user, DESCRIBE_API_NAME);

        // Act
        OpenChangeOrder.Result result = objectChangeOrderService.syncChangeFieldsByOriginalDescribe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(changeOrderLogicService).syncChangeOrderDescribeWithOriginalDescribe(user, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否开启变更顺序成功的正常场景
     */
    @Test
    @DisplayName("测试isOpenChangeOrder成功")
    void testIsOpenChangeOrderSuccess() {
        // Arrange
        IsOpenChangeOrder.Arg arg = new IsOpenChangeOrder.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        when(changeOrderLogicService.isOpenChangeOrder(user, DESCRIBE_API_NAME)).thenReturn(true);

        // Act
        IsOpenChangeOrder.Result result = objectChangeOrderService.isOpenChangeOrder(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getResult());
        verify(changeOrderLogicService).isOpenChangeOrder(user, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否开启变更顺序参数为空时抛出异常
     */
    @Test
    @DisplayName("测试isOpenChangeOrder参数为空抛出异常")
    void testIsOpenChangeOrderBlankApiNameThrowsException() {
        // Arrange
        IsOpenChangeOrder.Arg arg = new IsOpenChangeOrder.Arg();
        arg.setDescribeApiName("");

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            objectChangeOrderService.isOpenChangeOrder(arg, serviceContext);
        });
        verify(changeOrderLogicService, never()).isOpenChangeOrder(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查主对象是否开启变更顺序成功的正常场景
     */
    @Test
    @DisplayName("测试isOpenChangeOrderForMasterObj成功")
    void testIsOpenChangeOrderForMasterObjSuccess() {
        // Arrange
        IsOpenChangeOrder.Arg arg = new IsOpenChangeOrder.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        MasterDetailFieldDescribe mockMasterDetailField = mock(MasterDetailFieldDescribe.class);
        ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);

        when(licenseService.isSupportChangeOrder(TENANT_ID)).thenReturn(true);
        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(mockMasterDetailField.getTargetApiName()).thenReturn("master_object__c");
        when(changeOrderLogicService.isOpenChangeOrder(user, "master_object__c")).thenReturn(true);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            mockedStatic.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.getMasterDetailFieldDescribe()).thenReturn(Optional.of(mockMasterDetailField));

            // Act
            IsOpenChangeOrder.Result result = objectChangeOrderService.isOpenChangeOrderForMasterObj(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertTrue(result.getResult());
            verify(licenseService).isSupportChangeOrder(TENANT_ID);
            verify(describeLogicService).findObject(TENANT_ID, DESCRIBE_API_NAME);
            verify(changeOrderLogicService).isOpenChangeOrder(user, "master_object__c");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查主对象是否开启变更顺序许可证不支持时返回false
     */
    @Test
    @DisplayName("测试isOpenChangeOrderForMasterObj许可证不支持返回false")
    void testIsOpenChangeOrderForMasterObjLicenseNotSupportReturnsFalse() {
        // Arrange
        IsOpenChangeOrder.Arg arg = new IsOpenChangeOrder.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        when(licenseService.isSupportChangeOrder(TENANT_ID)).thenReturn(false);

        // Act
        IsOpenChangeOrder.Result result = objectChangeOrderService.isOpenChangeOrderForMasterObj(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.getResult());
        verify(licenseService).isSupportChangeOrder(TENANT_ID);
        verify(describeLogicService, never()).findObject(any(), any());
        verify(changeOrderLogicService, never()).isOpenChangeOrder(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查主对象是否开启变更顺序无主从关系时返回false
     */
    @Test
    @DisplayName("测试isOpenChangeOrderForMasterObj无主从关系返回false")
    void testIsOpenChangeOrderForMasterObjNoMasterDetailReturnsFalse() {
        // Arrange
        IsOpenChangeOrder.Arg arg = new IsOpenChangeOrder.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);

        when(licenseService.isSupportChangeOrder(TENANT_ID)).thenReturn(true);
        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = mockStatic(ObjectDescribeExt.class)) {
            mockedStatic.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.getMasterDetailFieldDescribe()).thenReturn(Optional.empty());

            // Act
            IsOpenChangeOrder.Result result = objectChangeOrderService.isOpenChangeOrderForMasterObj(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertFalse(result.getResult());
            verify(licenseService).isSupportChangeOrder(TENANT_ID);
            verify(describeLogicService).findObject(TENANT_ID, DESCRIBE_API_NAME);
            verify(changeOrderLogicService, never()).isOpenChangeOrder(any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectChangeOrderService);
        assertNotNull(changeOrderLogicService);
        assertNotNull(changeOrderRuleLogicService);
        assertNotNull(describeLogicService);
        assertNotNull(licenseService);
    }
}
