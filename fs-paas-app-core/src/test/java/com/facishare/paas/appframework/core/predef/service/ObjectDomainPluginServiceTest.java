package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.predef.service.dto.domain.*;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.ReferenceLogicService;
import com.facishare.paas.appframework.metadata.domain.DomainPluginLogicService;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.metadata.api.QueryResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectDomainPluginService单元测试类
 * 测试域插件管理服务的各种功能
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("ObjectDomainPluginService单元测试")
public class ObjectDomainPluginServiceTest extends BaseServiceTest {

    @Mock
    private DomainPluginLogicService domainPluginLogicService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private LayoutLogicService layoutLogicService;

    @Mock
    private ReferenceLogicService referenceLogicService;

    @InjectMocks
    private ObjectDomainPluginService objectDomainPluginService;

    private static final String OBJECT_API_NAME = "TestObj__c";
    private static final String PLUGIN_API_NAME = "testPlugin";
    private static final String FIELD_API_NAME = "testField";
    private static final String PLUGIN_ID = "plugin123";

    @Override
    protected String getServiceName() {
        return "domain_plugin";
    }

    @BeforeEach
    void setUp() {
        // 基础设置已在BaseServiceTest中完成
    }

    @Test
    @DisplayName("GenerateByAI - 测试startPluginInstance方法成功场景")
    void testStartPluginInstance_Success() {
        // Arrange
        StartPluginInstance.Arg arg = new StartPluginInstance.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setPluginApiName(PLUGIN_API_NAME);

        DomainPluginInstance mockInstance = mock(DomainPluginInstance.class);
        when(mockInstance.getId()).thenReturn(PLUGIN_ID);

        // Act
        StartPluginInstance.Result result = objectDomainPluginService.startPluginInstance(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(domainPluginLogicService, times(1)).createPluginInstance(eq(testUser), any(DomainPluginInstance.class));
    }

    @Test
    @DisplayName("GenerateByAI - 测试createPluginInstance方法参数验证")
    void testCreatePluginInstance_ParameterValidation() {
        // Arrange
        CreateOrUpdatePluginInstance.Arg arg = new CreateOrUpdatePluginInstance.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.createPluginInstance(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
            // 只要抛出异常就说明参数验证生效了
        }
    }

    @Test
    @DisplayName("GenerateByAI - 测试updatePluginInstance方法参数验证")
    void testUpdatePluginInstance_ParameterValidation() {
        // Arrange
        CreateOrUpdatePluginInstance.Arg arg = new CreateOrUpdatePluginInstance.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.updatePluginInstance(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
            // 只要抛出异常就说明参数验证生效了
        }
    }

    @Test
    @DisplayName("GenerateByAI - 测试enablePluginInstance方法成功场景")
    void testEnablePluginInstance_Success() {
        // Arrange
        EnableOrDisablePluginInstance.Arg arg = new EnableOrDisablePluginInstance.Arg();
        arg.setId(PLUGIN_ID);

        // Act
        EnableOrDisablePluginInstance.Result result = objectDomainPluginService.enablePluginInstance(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(domainPluginLogicService, times(1)).updatePluginInstanceStatusById(testUser, PLUGIN_ID, true);
    }

    @Test
    @DisplayName("GenerateByAI - 测试disablePluginInstance方法成功场景")
    void testDisablePluginInstance_Success() {
        // Arrange
        EnableOrDisablePluginInstance.Arg arg = new EnableOrDisablePluginInstance.Arg();
        arg.setId(PLUGIN_ID);

        // Act
        EnableOrDisablePluginInstance.Result result = objectDomainPluginService.disablePluginInstance(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(domainPluginLogicService, times(1)).updatePluginInstanceStatusById(testUser, PLUGIN_ID, false);
    }

    @Test
    @DisplayName("GenerateByAI - 测试deletePluginInstance方法成功场景")
    void testDeletePluginInstance_Success() {
        // Arrange
        EnableOrDisablePluginInstance.Arg arg = new EnableOrDisablePluginInstance.Arg();
        arg.setId(PLUGIN_ID);

        // Act
        EnableOrDisablePluginInstance.Result result = objectDomainPluginService.deletePluginInstance(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(domainPluginLogicService, times(1)).invalidPluginInstanceById(testUser, PLUGIN_ID);
    }

    @Test
    @DisplayName("GenerateByAI - 测试findPluginInstanceList方法成功场景")
    void testFindPluginInstanceList_Success() {
        // Arrange
        FindPluginInstanceList.Arg arg = new FindPluginInstanceList.Arg();
        // 设置查询信息
        arg.setSearchQueryInfo("{}");

        @SuppressWarnings("unchecked")
        QueryResult<DomainPluginInstance> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList());
        when(mockQueryResult.getTotalNumber()).thenReturn(0);
        when(domainPluginLogicService.findPluginInstanceByQuery(eq(testUser), any()))
                .thenReturn(mockQueryResult);

        // Mock DescribeLogicService
        when(describeLogicService.findDisplayNameByApiNames(anyString(), any()))
                .thenReturn(Maps.newHashMap());

        // Act
        FindPluginInstanceList.Result result = objectDomainPluginService.findPluginInstanceList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getTotalNumber());
        verify(domainPluginLogicService, times(1)).findPluginInstanceByQuery(eq(testUser), any());
    }

    @Test
    @DisplayName("GenerateByAI - 测试findAvailablePluginList方法成功场景")
    void testFindAvailablePluginList_Success() {
        // Arrange
        FindAvailablePluginList.Arg arg = new FindAvailablePluginList.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);

        when(domainPluginLogicService.findAvailablePluginDefinitionsForManagement(TENANT_ID, OBJECT_API_NAME))
                .thenReturn(Lists.newArrayList());

        // Act
        FindAvailablePluginList.Result result = objectDomainPluginService.findAvailablePluginList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPluginList());
        verify(domainPluginLogicService, times(1)).findAvailablePluginDefinitionsForManagement(TENANT_ID, OBJECT_API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试findPluginStatus方法成功场景")
    void testFindPluginStatus_Success() {
        // Arrange
        FindPluginStatus.Arg arg = new FindPluginStatus.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setPluginApiNames(Lists.newArrayList(PLUGIN_API_NAME));

        Map<String, Boolean> mockStatusMap = Maps.newHashMap();
        mockStatusMap.put(PLUGIN_API_NAME, true);
        when(domainPluginLogicService.findPluginStatus(TENANT_ID, OBJECT_API_NAME, Lists.newArrayList(PLUGIN_API_NAME)))
                .thenReturn(mockStatusMap);

        // Act
        FindPluginStatus.Result result = objectDomainPluginService.findPluginStatus(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPluginStatusMap());
        assertTrue(result.getPluginStatusMap().get(PLUGIN_API_NAME));
        verify(domainPluginLogicService, times(1)).findPluginStatus(TENANT_ID, OBJECT_API_NAME, Lists.newArrayList(PLUGIN_API_NAME));
    }

    @Test
    @DisplayName("GenerateByAI - 测试findAllEnabledPlugin方法成功场景")
    void testFindAllEnabledPlugin_Success() {
        // Arrange
        FindAllEnabledPlugin.Arg arg = new FindAllEnabledPlugin.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);

        when(domainPluginLogicService.findPluginInstances(TENANT_ID, OBJECT_API_NAME, null, null))
                .thenReturn(Lists.newArrayList());

        // Act
        FindAllEnabledPlugin.Result result = objectDomainPluginService.findAllEnabledPlugin(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(domainPluginLogicService, times(1)).findPluginInstances(TENANT_ID, OBJECT_API_NAME, null, null);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findPluginInstanceListManagement方法，正常获取管理端插件实例列表
     */
    @Test
    @DisplayName("GenerateByAI - 测试findPluginInstanceListManagement方法成功场景")
    void testFindPluginInstanceListManagement_Success() {
        // Arrange
        @SuppressWarnings("unchecked")
        QueryResult<DomainPluginInstance> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList());
        when(domainPluginLogicService.findPluginInstanceByQuery(eq(testUser), any()))
                .thenReturn(mockQueryResult);

        when(domainPluginLogicService.filterPluginInstancesByDefinitions(anyString(), any()))
                .thenReturn(Lists.newArrayList());

        when(domainPluginLogicService.findPluginLabelByApiNames(any()))
                .thenReturn(Maps.newHashMap());

        // Act
        FindPluginInstanceList.Result result = objectDomainPluginService.findPluginInstanceListManagement(serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPluginInstanceList());
        verify(domainPluginLogicService, times(1)).findPluginInstanceByQuery(eq(testUser), any());
        verify(domainPluginLogicService, times(1)).filterPluginInstancesByDefinitions(eq(TENANT_ID), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findPluginDefinition方法，正常获取插件定义
     */
    @Test
    @DisplayName("GenerateByAI - 测试findPluginDefinition方法成功场景")
    void testFindPluginDefinition_Success() {
        // Arrange
        FindPluginDefinition.Arg arg = new FindPluginDefinition.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);

        // Act
        FindPluginDefinition.Result result = objectDomainPluginService.findPluginDefinition(arg, serviceContext);

        // Assert
        assertNotNull(result);
        // 由于DomainPluginDefinitionHolder是静态方法，这里主要测试方法不抛异常
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findPluginDefinition方法，空参数验证
     */
    @Test
    @DisplayName("GenerateByAI - 测试findPluginDefinition方法参数验证")
    void testFindPluginDefinition_ParameterValidation() {
        // Arrange
        FindPluginDefinition.Arg arg = new FindPluginDefinition.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.findPluginDefinition(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试startPluginInstance方法，空参数验证
     */
    @Test
    @DisplayName("GenerateByAI - 测试startPluginInstance方法参数验证")
    void testStartPluginInstance_ParameterValidation() {
        // Arrange
        StartPluginInstance.Arg arg = new StartPluginInstance.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.startPluginInstance(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findAvailablePluginList方法，空参数验证
     */
    @Test
    @DisplayName("GenerateByAI - 测试findAvailablePluginList方法参数验证")
    void testFindAvailablePluginList_ParameterValidation() {
        // Arrange
        FindAvailablePluginList.Arg arg = new FindAvailablePluginList.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.findAvailablePluginList(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findPluginStatus方法，空参数验证
     */
    @Test
    @DisplayName("GenerateByAI - 测试findPluginStatus方法参数验证")
    void testFindPluginStatus_ParameterValidation() {
        // Arrange
        FindPluginStatus.Arg arg = new FindPluginStatus.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.findPluginStatus(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findAllEnabledPlugin方法，空参数验证
     */
    @Test
    @DisplayName("GenerateByAI - 测试findAllEnabledPlugin方法参数验证")
    void testFindAllEnabledPlugin_ParameterValidation() {
        // Arrange
        FindAllEnabledPlugin.Arg arg = new FindAllEnabledPlugin.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.findAllEnabledPlugin(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enablePluginInstance方法，空参数验证
     */
    @Test
    @DisplayName("GenerateByAI - 测试enablePluginInstance方法参数验证")
    void testEnablePluginInstance_ParameterValidation() {
        // Arrange
        EnableOrDisablePluginInstance.Arg arg = new EnableOrDisablePluginInstance.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.enablePluginInstance(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试disablePluginInstance方法，空参数验证
     */
    @Test
    @DisplayName("GenerateByAI - 测试disablePluginInstance方法参数验证")
    void testDisablePluginInstance_ParameterValidation() {
        // Arrange
        EnableOrDisablePluginInstance.Arg arg = new EnableOrDisablePluginInstance.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.disablePluginInstance(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deletePluginInstance方法，空参数验证
     */
    @Test
    @DisplayName("GenerateByAI - 测试deletePluginInstance方法参数验证")
    void testDeletePluginInstance_ParameterValidation() {
        // Arrange
        EnableOrDisablePluginInstance.Arg arg = new EnableOrDisablePluginInstance.Arg();

        // Act & Assert - 测试空参数验证
        try {
            objectDomainPluginService.deletePluginInstance(arg, serviceContext);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期的异常，测试通过
            assertNotNull(e);
        }
    }
}
