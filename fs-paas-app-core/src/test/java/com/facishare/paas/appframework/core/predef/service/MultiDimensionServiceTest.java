package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * MultiDimensionService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("MultiDimensionService单元测试")
class MultiDimensionServiceTest {

    @Mock
    private DescribeLogicService describeLogicService;
    
    @InjectMocks
    private MultiDimensionService multiDimensionService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "dimension", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试MultiDimensionService实例化是否成功
     */
    @Test
    @DisplayName("测试Service实例化成功")
    void testServiceInstantiationSuccess() {
        // Arrange & Act & Assert
        assertNotNull(multiDimensionService);
        assertNotNull(serviceContext);
        assertNotNull(user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试依赖注入是否正确配置
     */
    @Test
    @DisplayName("测试依赖注入配置正确")
    void testDependencyInjectionSuccess() {
        // Arrange & Act & Assert
        assertNotNull(multiDimensionService);
        // 验证Mock对象已正确注入
        assertNotNull(describeLogicService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Arrange & Act & Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getTenantId());
        assertEquals(user, serviceContext.getUser());
        assertEquals("dimension", serviceContext.getServiceName());
        assertEquals("test_method", serviceContext.getServiceMethod());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Arrange & Act & Assert
        assertNotNull(user);
        assertEquals(TENANT_ID, user.getTenantId());
        assertEquals(USER_ID, user.getUserId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service类的基本属性和注解配置
     */
    @Test
    @DisplayName("测试Service类基本配置")
    void testServiceClassConfiguration() {
        // Arrange & Act
        Class<?> serviceClass = multiDimensionService.getClass();
        
        // Assert
        assertNotNull(serviceClass);
        assertTrue(serviceClass.isAnnotationPresent(org.springframework.stereotype.Service.class));
        assertTrue(serviceClass.isAnnotationPresent(com.facishare.paas.appframework.core.annotation.ServiceModule.class));
        
        // 验证ServiceModule注解的值
        com.facishare.paas.appframework.core.annotation.ServiceModule serviceModuleAnnotation = 
                serviceClass.getAnnotation(com.facishare.paas.appframework.core.annotation.ServiceModule.class);
        assertEquals("dimension", serviceModuleAnnotation.value());
    }
}
