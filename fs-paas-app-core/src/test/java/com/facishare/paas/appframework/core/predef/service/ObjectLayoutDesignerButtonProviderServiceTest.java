package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.button.GetLayoutDesignerButton;
import com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonManager;
import com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonProvider;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectLayoutDesignerButtonProviderService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectLayoutDesignerButtonProviderService单元测试")
class ObjectLayoutDesignerButtonProviderServiceTest {

    @Mock
    private LayoutDesignerButtonManager layoutDesignerButtonManager;
    
    @Mock
    private LayoutDesignerButtonProvider provider;
    
    @InjectMocks
    private ObjectLayoutDesignerButtonProviderService service;
    
    private User testUser;
    private ServiceContext serviceContext;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        testUser = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(testUser)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "layout_designer_button", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法，正常获取布局设计器按钮
     */
    @Test
    @DisplayName("正常场景 - 测试getLayoutDesignerButton方法")
    void testGetLayoutDesignerButton_NormalCase() {
        // 准备测试数据
        GetLayoutDesignerButton.Arg arg = new GetLayoutDesignerButton.Arg();
        arg.setDescribeApiName("TestObj__c");
        
        Map<String, Object> buttonMap = Maps.newHashMap();
        buttonMap.put("apiName", "TestButton");
        buttonMap.put("displayName", "测试按钮");
        arg.setButtonList(Lists.newArrayList(buttonMap));
        
        IButton mockButton = new Button(buttonMap);
        List<IButton> mockButtons = Lists.newArrayList(mockButton);
        
        // 配置Mock行为
        when(layoutDesignerButtonManager.getLocalProvider(anyString())).thenReturn(provider);
        when(provider.getButtons(anyList(), any(User.class))).thenReturn(mockButtons);
        
        // 执行被测试方法
        GetLayoutDesignerButton.Result result = service.getLayoutDesignerButton(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(1, result.getResult().size());
        
        // 验证Mock交互
        verify(layoutDesignerButtonManager).getLocalProvider(eq("TestObj__c"));
        verify(provider).getButtons(anyList(), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法，空按钮列表场景
     */
    @Test
    @DisplayName("边界场景 - 测试getLayoutDesignerButton方法空按钮列表")
    void testGetLayoutDesignerButton_EmptyButtonList() {
        // 准备测试数据
        GetLayoutDesignerButton.Arg arg = new GetLayoutDesignerButton.Arg();
        arg.setDescribeApiName("TestObj__c");
        arg.setButtonList(Lists.newArrayList());
        
        // 配置Mock行为
        when(layoutDesignerButtonManager.getLocalProvider(anyString())).thenReturn(provider);
        when(provider.getButtons(anyList(), any(User.class))).thenReturn(Lists.newArrayList());
        
        // 执行被测试方法
        GetLayoutDesignerButton.Result result = service.getLayoutDesignerButton(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertTrue(result.getResult().isEmpty());
        
        // 验证Mock交互
        verify(layoutDesignerButtonManager).getLocalProvider(eq("TestObj__c"));
        verify(provider).getButtons(anyList(), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法，null按钮列表场景
     */
    @Test
    @DisplayName("边界场景 - 测试getLayoutDesignerButton方法null按钮列表")
    void testGetLayoutDesignerButton_NullButtonList() {
        // 准备测试数据
        GetLayoutDesignerButton.Arg arg = new GetLayoutDesignerButton.Arg();
        arg.setDescribeApiName("TestObj__c");
        arg.setButtonList(null);
        
        // 配置Mock行为
        when(layoutDesignerButtonManager.getLocalProvider(anyString())).thenReturn(provider);
        when(provider.getButtons(anyList(), any(User.class))).thenReturn(Lists.newArrayList());
        
        // 执行被测试方法
        GetLayoutDesignerButton.Result result = service.getLayoutDesignerButton(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertTrue(result.getResult().isEmpty());
        
        // 验证Mock交互
        verify(layoutDesignerButtonManager).getLocalProvider(eq("TestObj__c"));
        verify(provider).getButtons(anyList(), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法，多个按钮场景
     */
    @Test
    @DisplayName("正常场景 - 测试getLayoutDesignerButton方法多个按钮")
    void testGetLayoutDesignerButton_MultipleButtons() {
        // 准备测试数据
        GetLayoutDesignerButton.Arg arg = new GetLayoutDesignerButton.Arg();
        arg.setDescribeApiName("TestObj__c");
        
        Map<String, Object> button1Map = Maps.newHashMap();
        button1Map.put("apiName", "Button1");
        button1Map.put("displayName", "按钮1");
        
        Map<String, Object> button2Map = Maps.newHashMap();
        button2Map.put("apiName", "Button2");
        button2Map.put("displayName", "按钮2");
        
        arg.setButtonList(Lists.newArrayList(button1Map, button2Map));
        
        IButton mockButton1 = new Button(button1Map);
        IButton mockButton2 = new Button(button2Map);
        List<IButton> mockButtons = Lists.newArrayList(mockButton1, mockButton2);
        
        // 配置Mock行为
        when(layoutDesignerButtonManager.getLocalProvider(anyString())).thenReturn(provider);
        when(provider.getButtons(anyList(), any(User.class))).thenReturn(mockButtons);
        
        // 执行被测试方法
        GetLayoutDesignerButton.Result result = service.getLayoutDesignerButton(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(2, result.getResult().size());
        
        // 验证Mock交互
        verify(layoutDesignerButtonManager).getLocalProvider(eq("TestObj__c"));
        verify(provider).getButtons(anyList(), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法，null参数异常场景
     */
    @Test
    @DisplayName("异常场景 - 测试getLayoutDesignerButton方法null参数")
    void testGetLayoutDesignerButtonThrowsException_NullArg() {
        // 执行并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            service.getLayoutDesignerButton(null, serviceContext);
        });
        
        // 验证异常信息
        assertNotNull(exception);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(layoutDesignerButtonManager);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法，按钮属性修改场景
     */
    @Test
    @DisplayName("正常场景 - 测试getLayoutDesignerButton方法按钮属性修改")
    void testGetLayoutDesignerButton_ButtonPropertiesModified() {
        // 准备测试数据
        GetLayoutDesignerButton.Arg arg = new GetLayoutDesignerButton.Arg();
        arg.setDescribeApiName("AccountObj");

        Map<String, Object> buttonData = Maps.newHashMap();
        buttonData.put("api_name", "edit");
        buttonData.put("name", "edit");
        buttonData.put("label", "编辑");
        buttonData.put("order", 10);
        arg.setButtonList(Lists.newArrayList(buttonData));

        // 配置Mock行为
        when(layoutDesignerButtonManager.getLocalProvider("AccountObj")).thenReturn(provider);
        when(provider.getButtons(any(), any())).thenAnswer(invocation -> {
            @SuppressWarnings("unchecked")
            List<IButton> buttons = (List<IButton>) invocation.getArgument(0);
            // 修改按钮属性
            for (IButton button : buttons) {
                button.set("label", "修改后的编辑");
                button.set("disabled", true);
            }
            return buttons;
        });

        // 执行被测试方法
        GetLayoutDesignerButton.Result result = service.getLayoutDesignerButton(arg, serviceContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(1, result.getResult().size());
        assertEquals("edit", result.getResult().get(0).get("api_name"));
        assertEquals("修改后的编辑", result.getResult().get(0).get("label"));
        assertTrue((Boolean) result.getResult().get(0).get("disabled"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法，按钮过滤场景
     */
    @Test
    @DisplayName("正常场景 - 测试getLayoutDesignerButton方法按钮过滤")
    void testGetLayoutDesignerButton_ButtonFiltered() {
        // 准备测试数据
        GetLayoutDesignerButton.Arg arg = new GetLayoutDesignerButton.Arg();
        arg.setDescribeApiName("AccountObj");

        Map<String, Object> buttonData1 = Maps.newHashMap();
        buttonData1.put("api_name", "edit");
        buttonData1.put("name", "edit");
        buttonData1.put("label", "编辑");
        buttonData1.put("order", 10);

        Map<String, Object> buttonData2 = Maps.newHashMap();
        buttonData2.put("api_name", "delete");
        buttonData2.put("name", "delete");
        buttonData2.put("label", "删除");
        buttonData2.put("order", 20);

        arg.setButtonList(Lists.newArrayList(buttonData1, buttonData2));

        // 配置Mock行为
        when(layoutDesignerButtonManager.getLocalProvider("AccountObj")).thenReturn(provider);
        when(provider.getButtons(any(), any())).thenAnswer(invocation -> {
            @SuppressWarnings("unchecked")
            List<IButton> buttons = (List<IButton>) invocation.getArgument(0);
            // 只保留edit按钮
            return buttons.stream()
                    .filter(button -> "edit".equals(button.getName()))
                    .collect(java.util.stream.Collectors.toList());
        });

        // 执行被测试方法
        GetLayoutDesignerButton.Result result = service.getLayoutDesignerButton(arg, serviceContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(1, result.getResult().size());
        assertEquals("edit", result.getResult().get(0).get("name"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法，按钮添加场景
     */
    @Test
    @DisplayName("正常场景 - 测试getLayoutDesignerButton方法按钮添加")
    void testGetLayoutDesignerButton_ButtonAdded() {
        // 准备测试数据
        GetLayoutDesignerButton.Arg arg = new GetLayoutDesignerButton.Arg();
        arg.setDescribeApiName("AccountObj");

        Map<String, Object> buttonData = Maps.newHashMap();
        buttonData.put("api_name", "edit");
        buttonData.put("name", "edit");
        buttonData.put("label", "编辑");
        buttonData.put("order", 10);
        arg.setButtonList(Lists.newArrayList(buttonData));

        // 配置Mock行为
        when(layoutDesignerButtonManager.getLocalProvider("AccountObj")).thenReturn(provider);
        when(provider.getButtons(any(), any())).thenAnswer(invocation -> {
            @SuppressWarnings("unchecked")
            List<IButton> buttons = (List<IButton>) invocation.getArgument(0);
            // 添加一个新按钮
            Button newButton = new Button();
            newButton.set("api_name", "custom");
            newButton.set("name", "custom");
            newButton.set("label", "自定义按钮");
            newButton.set("order", 30);
            buttons.add(newButton);
            return buttons;
        });

        // 执行被测试方法
        GetLayoutDesignerButton.Result result = service.getLayoutDesignerButton(arg, serviceContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(2, result.getResult().size());

        // 验证原有按钮
        boolean hasEditButton = result.getResult().stream()
                .anyMatch(button -> "edit".equals(button.get("name")));
        assertTrue(hasEditButton);

        // 验证新添加的按钮
        boolean hasCustomButton = result.getResult().stream()
                .anyMatch(button -> "custom".equals(button.get("name")));
        assertTrue(hasCustomButton);
    }
}
