package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.privilege.service.RoleService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.recordType.*;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.I18nSettingService;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraLogicService;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectRecordTypeService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectRecordTypeService单元测试")
class ObjectRecordTypeServiceTest {

    @Mock
    private RecordTypeLogicServiceImpl recordTypeService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private RoleService roleService;
    
    @Mock
    private FieldBackgroundExtraLogicService fieldBackgroundExtraLogicService;
    
    @Mock
    private I18nSettingService i18nSettingService;
    
    @InjectMocks
    private ObjectRecordTypeService objectRecordTypeService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OBJECT_API_NAME = "test_object__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "record_type", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型成功的场景
     */
    @Test
    @DisplayName("测试创建记录类型成功")
    void testCreateRecordTypeSuccess() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRemark("Test remark");
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        pojo.setLabel("Test Record Type");
        arg.setRecord_type(JSON.toJSONString(pojo));
        
        List<I18nInfo> i18nInfoList = Lists.newArrayList();
        I18nInfo i18nInfo = new I18nInfo();
        i18nInfo.setApiName("test_record_type");
        i18nInfo.setValue("Test Record Type EN");
        i18nInfoList.add(i18nInfo);
        arg.setI18nInfoList(i18nInfoList);

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any()))
                .thenReturn(recordTypeResult);

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService).createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any());
        verify(i18nSettingService).synTranslateToMeta(TENANT_ID, OBJECT_API_NAME, i18nInfoList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时记录类型为空的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - 记录类型为空")
    void testCreateRecordTypeWithBlankRecordType() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRecord_type("");

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService, never()).createRecordType(any(), any(), any(), any());
        verify(i18nSettingService, never()).synTranslateToMeta(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时对象API名称为空的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - 对象API名称为空")
    void testCreateRecordTypeWithBlankDescribeApiName() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName("");
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        arg.setRecord_type(JSON.toJSONString(pojo));

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService, never()).createRecordType(any(), any(), any(), any());
        verify(i18nSettingService, never()).synTranslateToMeta(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型失败的场景
     */
    @Test
    @DisplayName("测试创建记录类型失败")
    void testCreateRecordTypeFailure() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        pojo.setLabel("Test Record Type");
        arg.setRecord_type(JSON.toJSONString(pojo));
        arg.setI18nInfoList(Collections.emptyList());

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(false);

        when(recordTypeService.createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any()))
                .thenReturn(recordTypeResult);

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService).createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any());
        verify(i18nSettingService).synTranslateToMeta(TENANT_ID, OBJECT_API_NAME, Collections.emptyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时JSON解析异常的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - JSON解析异常")
    void testCreateRecordTypeWithInvalidJson() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRecord_type("invalid json");

        // Act & Assert
        assertThrows(Exception.class, () -> {
            objectRecordTypeService.createRecordType(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时包含国际化信息的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - 包含国际化信息")
    void testCreateRecordTypeWithI18nInfo() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        pojo.setLabel("Test Record Type");
        arg.setRecord_type(JSON.toJSONString(pojo));
        
        List<I18nInfo> i18nInfoList = Lists.newArrayList();
        I18nInfo i18nInfo1 = new I18nInfo();
        i18nInfo1.setApiName("test_record_type_en");
        i18nInfo1.setValue("Test Record Type EN");

        I18nInfo i18nInfo2 = new I18nInfo();
        i18nInfo2.setApiName("test_record_type_zh");
        i18nInfo2.setValue("测试记录类型");
        
        i18nInfoList.add(i18nInfo1);
        i18nInfoList.add(i18nInfo2);
        arg.setI18nInfoList(i18nInfoList);

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any()))
                .thenReturn(recordTypeResult);

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService).createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any());
        verify(i18nSettingService).synTranslateToMeta(TENANT_ID, OBJECT_API_NAME, i18nInfoList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时国际化信息为null的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - 国际化信息为null")
    void testCreateRecordTypeWithNullI18nInfo() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        pojo.setLabel("Test Record Type");
        arg.setRecord_type(JSON.toJSONString(pojo));
        arg.setI18nInfoList(null);

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any()))
                .thenReturn(recordTypeResult);

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService).createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any());
        verify(i18nSettingService).synTranslateToMeta(TENANT_ID, OBJECT_API_NAME, null);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新记录类型成功的场景
     */
    @Test
    @DisplayName("测试更新记录类型成功")
    void testUpdateRecordTypeSuccess() {
        // Arrange
        UpdateRecordType.Arg arg = new UpdateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRecord_type("{\"apiName\":\"test_record_type\",\"displayName\":\"Updated Record Type\"}");

        List<I18nInfo> i18nInfoList = Lists.newArrayList();
        I18nInfo i18nInfo = new I18nInfo();
        i18nInfo.setApiName("test_record_type");
        i18nInfo.setValue("Updated Record Type");
        i18nInfoList.add(i18nInfo);
        arg.setI18nInfoList(i18nInfoList);

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.updateRecordType(any(User.class), eq(OBJECT_API_NAME), any()))
                .thenReturn(recordTypeResult);

        // Act
        UpdateRecordType.Result result = objectRecordTypeService.updateRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(recordTypeService).updateRecordType(any(User.class), eq(OBJECT_API_NAME), any());
        verify(i18nSettingService).synTranslateToMeta(TENANT_ID, OBJECT_API_NAME, i18nInfoList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除记录类型成功的场景
     */
    @Test
    @DisplayName("测试删除记录类型成功")
    void testDeleteRecordTypeSuccess() {
        // Arrange
        DeleteRecordType.Arg arg = new DeleteRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRecordApiName("test_record_type");

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.deleteRecordType(any(User.class), eq(OBJECT_API_NAME), eq("test_record_type")))
                .thenReturn(recordTypeResult);

        // Act
        DeleteRecordType.Result result = objectRecordTypeService.deleteRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(recordTypeService).deleteRecordType(any(User.class), eq(OBJECT_API_NAME), eq("test_record_type"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用记录类型成功的场景
     */
    @Test
    @DisplayName("测试启用记录类型成功")
    void testEnableRecordTypeSuccess() {
        // Arrange
        EnableRecordType.Arg arg = new EnableRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRecordApiName("test_record_type");

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.enableRecordType(any(User.class), eq(OBJECT_API_NAME), eq("test_record_type")))
                .thenReturn(recordTypeResult);

        // Act
        EnableRecordType.Result result = objectRecordTypeService.enableRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(recordTypeService).enableRecordType(any(User.class), eq(OBJECT_API_NAME), eq("test_record_type"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用记录类型成功的场景
     */
    @Test
    @DisplayName("测试禁用记录类型成功")
    void testDisableRecordTypeSuccess() {
        // Arrange
        DisableRecordType.Arg arg = new DisableRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRecordApiName("test_record_type");

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.disableRecordType(any(User.class), eq(OBJECT_API_NAME), eq("test_record_type")))
                .thenReturn(recordTypeResult);

        RecordTypeResult roleAndRecordTypeResult = new RecordTypeResult();
        roleAndRecordTypeResult.setRole_list(Lists.newArrayList());

        when(recordTypeService.findRoleAndRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(User.class)))
                .thenReturn(roleAndRecordTypeResult);

        // Act
        DisableRecordType.Result result = objectRecordTypeService.disableRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(recordTypeService).disableRecordType(any(User.class), eq(OBJECT_API_NAME), eq("test_record_type"));
        verify(recordTypeService).findRoleAndRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找角色信息列表成功的场景
     */
    @Test
    @DisplayName("测试查找角色信息列表成功")
    void testFindRoleInfoListSuccess() {
        // Arrange
        FindRoleInfoList.Arg arg = new FindRoleInfoList.Arg();

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setRole_list(Lists.newArrayList());

        when(recordTypeService.findRoleInfoList(any(User.class)))
                .thenReturn(recordTypeResult);

        // Act
        FindRoleInfoList.Result result = objectRecordTypeService.findRoleInfoList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRole_list());
        verify(recordTypeService).findRoleInfoList(any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找记录信息成功的场景
     */
    @Test
    @DisplayName("测试查找记录信息成功")
    void testFindRecordInfoSuccess() {
        // Arrange
        FindRecordInfo.Arg arg = new FindRecordInfo.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRecordApiName("test_record_type");
        arg.setIncludeRemark(false);
        arg.setNoNeedReplaceI18n(false);

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setRecordTypeOption(Maps.newHashMap());

        when(recordTypeService.findRecordInfo(any(User.class), eq(OBJECT_API_NAME), eq("test_record_type"), eq(false)))
                .thenReturn(recordTypeResult);

        // Act
        FindRecordInfo.Result result = objectRecordTypeService.findRecordInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRecordTypeOption());
        verify(recordTypeService).findRecordInfo(any(User.class), eq(OBJECT_API_NAME), eq("test_record_type"), eq(false));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试分配记录成功的场景
     */
    @Test
    @DisplayName("测试分配记录成功")
    void testAssignRecordSuccess() {
        // Arrange
        AssignRecord.Arg arg = new AssignRecord.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRole_list("[{\"roleId\":\"role1\",\"recordTypes\":[\"type1\",\"type2\"]}]");

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.assignRecord(eq(TENANT_ID), eq(OBJECT_API_NAME), any(), any(), any()))
                .thenReturn(recordTypeResult);

        // Act
        AssignRecord.Result result = objectRecordTypeService.assignRecord(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(recordTypeService).assignRecord(eq(TENANT_ID), eq(OBJECT_API_NAME), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试排序记录类型选项成功的场景
     */
    @Test
    @DisplayName("测试排序记录类型选项成功")
    void testSortRecordTypeOptionSuccess() {
        // Arrange
        SortRecordTypeOption.Arg arg = new SortRecordTypeOption.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setSortedRecordTypeOption(Lists.newArrayList("type1", "type2", "type3"));

        doNothing().when(recordTypeService).sortRecordTypeOption(any(), eq(OBJECT_API_NAME), any());

        // Act
        SortRecordTypeOption.Result result = objectRecordTypeService.sortRecordTypeOption(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(recordTypeService).sortRecordTypeOption(any(), eq(OBJECT_API_NAME), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量查找有效记录类型列表成功的场景
     */
    @Test
    @DisplayName("测试批量查找有效记录类型列表成功")
    void testBulkFindValidRecordTypeListSuccess() {
        // Arrange
        BulkFindRecordTypeList.Arg arg = new BulkFindRecordTypeList.Arg();
        arg.setDescribeApiNameList(Lists.newArrayList(OBJECT_API_NAME, "another_object__c"));

        // Mock ObjectDescribe
        Map<String, Object> describeData = Maps.newHashMap();
        describeData.put("api_name", OBJECT_API_NAME);
        describeData.put("display_name", "Test Object");
        ObjectDescribe mockDescribe = new ObjectDescribe(describeData);

        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(OBJECT_API_NAME, mockDescribe);

        when(describeLogicService.findObjects(eq(TENANT_ID), any()))
                .thenReturn(describeMap);

        when(recordTypeService.bulkFindValidRecordTypeList(any(), any()))
                .thenReturn(Maps.newHashMap());

        // Act
        BulkFindRecordTypeList.Result result = objectRecordTypeService.bulkFindValidRecordTypeList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRecordTypeMap());
        verify(describeLogicService).findObjects(eq(TENANT_ID), any());
        verify(recordTypeService).bulkFindValidRecordTypeList(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试初始化对象记录类型成功的场景
     */
    @Test
    @DisplayName("测试初始化对象记录类型成功")
    void testInitObjectRecordTypeSuccess() {
        // Arrange
        InitObjectRecordType.Arg arg = new InitObjectRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setLayoutApiName("test_layout");

        doNothing().when(recordTypeService).recordTypeInit(any(), eq("test_layout"), eq(TENANT_ID), eq(OBJECT_API_NAME));

        // Act
        InitObjectRecordType.Result result = objectRecordTypeService.initObjectRecordType(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(recordTypeService).recordTypeInit(any(), eq("test_layout"), eq(TENANT_ID), eq(OBJECT_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectRecordTypeService);
        assertNotNull(recordTypeService);
        assertNotNull(describeLogicService);
        assertNotNull(roleService);
        assertNotNull(fieldBackgroundExtraLogicService);
        assertNotNull(i18nSettingService);
    }
}
