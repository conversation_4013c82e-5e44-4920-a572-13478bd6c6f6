package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.archive.ArchiveRule;
import com.facishare.paas.appframework.core.predef.service.dto.archive.ArchiveRuleOperate;
import com.facishare.paas.appframework.core.predef.service.dto.archive.FindArchiveRule;
import com.facishare.paas.appframework.metadata.ObjectArchiveService;
import com.facishare.paas.metadata.api.IObjectArchiveRuleInfo;
import com.facishare.paas.metadata.impl.ObjectArchiveRuleInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import com.facishare.paas.I18N;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ArchiveRuleServiceTest {

    @Mock
    private ObjectArchiveService objectArchiveService;
    
    @InjectMocks
    private ArchiveRuleService archiveRuleService;
    
    private ServiceContext serviceContext;
    private User testUser;
    private String tenantId = "test-tenant";

    @BeforeEach
    void setUp() {
        testUser = new User(tenantId, "12345");
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(testUser)
                .build();
        serviceContext = new ServiceContext(requestContext, "testService", "testMethod");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试create方法，验证正常创建归档规则的情况
     */
    @Test
    @DisplayName("测试create - 正常创建归档规则")
    void testCreate_Success() {
        // Arrange
        ArchiveRule.Arg arg = new ArchiveRule.Arg();
        List<MappingRuleDocument> ruleList = new ArrayList<>();
        
        MappingRuleDocument rule1 = new MappingRuleDocument();
        rule1.put("ruleName", "Test Rule 1");
        rule1.put("ruleApiName", "test_rule_1");
        rule1.put("where_type", "custom");
        ruleList.add(rule1);
        
        arg.setRuleList(ruleList);

        // Act
        ArchiveRule.Result result = archiveRuleService.create(serviceContext, arg);

        // Assert
        assertNotNull(result);
        
        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<IObjectArchiveRuleInfo>> captor = ArgumentCaptor.forClass(List.class);
        verify(objectArchiveService).create(eq(testUser), captor.capture());
        
        List<IObjectArchiveRuleInfo> capturedRules = captor.getValue();
        assertEquals(1, capturedRules.size());
        assertEquals(tenantId, capturedRules.get(0).getTenantId());
        assertEquals("12345", capturedRules.get(0).getCreatedBy());
        assertEquals("12345", capturedRules.get(0).getLastModifiedBy());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试create方法，验证whereType为all时处理wheres的情况
     */
    @Test
    @DisplayName("测试create - whereType为all时处理wheres")
    void testCreate_WhereTypeAll() {
        // Arrange
        ArchiveRule.Arg arg = new ArchiveRule.Arg();
        List<MappingRuleDocument> ruleList = new ArrayList<>();
        
        MappingRuleDocument rule = new MappingRuleDocument();
        rule.put("ruleName", "All Rule");
        rule.put("ruleApiName", "all_rule");
        rule.put("where_type", "all");
        rule.put("wheres", Arrays.asList("condition1", "condition2"));
        ruleList.add(rule);
        
        arg.setRuleList(ruleList);

        // Act
        archiveRuleService.create(serviceContext, arg);

        // Assert
        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<IObjectArchiveRuleInfo>> captor = ArgumentCaptor.forClass(List.class);
        verify(objectArchiveService).create(eq(testUser), captor.capture());
        
        List<IObjectArchiveRuleInfo> capturedRules = captor.getValue();
        assertEquals(1, capturedRules.size());
        // 验证基本属性设置正确
        assertEquals(tenantId, capturedRules.get(0).getTenantId());
        assertEquals("12345", capturedRules.get(0).getCreatedBy());
        assertEquals("12345", capturedRules.get(0).getLastModifiedBy());
        assertEquals("all", capturedRules.get(0).getWhereType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试create方法，验证空规则列表抛出异常
     */
    @Test
    @DisplayName("测试create - 空规则列表抛出异常")
    void testCreate_EmptyRuleList() {
        // Arrange
        ArchiveRule.Arg arg = new ArchiveRule.Arg();
        arg.setRuleList(new ArrayList<>());

        // Act & Assert
        assertThrows(ValidateException.class, () -> 
            archiveRuleService.create(serviceContext, arg)
        );
        
        verify(objectArchiveService, never()).create(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试update方法，验证更新归档规则的情况
     */
    @Test
    @DisplayName("测试update - 正常更新归档规则")
    void testUpdate_Success() {
        // Arrange
        ArchiveRule.Arg arg = new ArchiveRule.Arg();
        List<MappingRuleDocument> ruleList = new ArrayList<>();
        
        MappingRuleDocument rule = new MappingRuleDocument();
        rule.put("id", "existing_id");
        rule.put("ruleName", "Updated Rule");
        rule.put("ruleApiName", "updated_rule");
        rule.put("where_type", "custom");
        ruleList.add(rule);
        
        arg.setRuleList(ruleList);

        // Act
        ArchiveRule.Result result = archiveRuleService.update(serviceContext, arg);

        // Assert
        assertNotNull(result);
        
        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<IObjectArchiveRuleInfo>> captor = ArgumentCaptor.forClass(List.class);
        verify(objectArchiveService).update(eq(testUser), captor.capture());
        
        List<IObjectArchiveRuleInfo> capturedRules = captor.getValue();
        assertEquals(1, capturedRules.size());
        // ID会被转换过程中移除，所以无法验证
        assertEquals("12345", capturedRules.get(0).getLastModifiedBy());
        // 有ID时不设置CreatedBy
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试list方法，验证列出归档规则的情况
     */
    @Test
    @DisplayName("测试list - 正常列出归档规则")
    void testList_Success() {
        // Arrange
        FindArchiveRule.Arg arg = new FindArchiveRule.Arg();
        arg.setRuleName("test");
        arg.setRuleApiName("test_api");
        
        List<IObjectArchiveRuleInfo> mockRules = new ArrayList<>();
        ObjectArchiveRuleInfo rule1 = new ObjectArchiveRuleInfo();
        rule1.setRuleName("Test Rule 1");
        rule1.setRuleApiName("test_rule_1");
        mockRules.add(rule1);
        
        when(objectArchiveService.list(testUser, "test", "test_api")).thenReturn(mockRules);

        // Act
        FindArchiveRule.Result result = archiveRuleService.list(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRuleList());
        assertEquals(1, result.getRuleList().size());
        
        verify(objectArchiveService).list(testUser, "test", "test_api");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试find方法，验证查找特定归档规则的情况
     */
    @Test
    @DisplayName("测试find - 正常查找归档规则")
    void testFind_Success() {
        // Arrange
        FindArchiveRule.Arg arg = new FindArchiveRule.Arg();
        arg.setRuleApiName("test_rule_api");
        
        List<IObjectArchiveRuleInfo> mockRules = new ArrayList<>();
        ObjectArchiveRuleInfo rule = new ObjectArchiveRuleInfo();
        rule.setRuleName("Found Rule");
        rule.setRuleApiName("test_rule_api");
        mockRules.add(rule);
        
        when(objectArchiveService.find(testUser, "test_rule_api")).thenReturn(mockRules);

        // Act
        FindArchiveRule.Result result = archiveRuleService.find(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRuleList());
        assertEquals(1, result.getRuleList().size());
        
        verify(objectArchiveService).find(testUser, "test_rule_api");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试find方法，验证空ruleApiName抛出异常
     */
    @Test
    @DisplayName("测试find - 空ruleApiName抛出异常")
    void testFind_EmptyRuleApiName() {
        // Mock I18N静态方法，避免初始化异常
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            mockedI18N.when(() -> I18N.text(any())).thenReturn("Mock I18N Text");

            // Arrange
            FindArchiveRule.Arg arg = new FindArchiveRule.Arg();
            arg.setRuleApiName("");

            // Act & Assert
            assertThrows(ValidateException.class, () ->
                archiveRuleService.find(serviceContext, arg)
            );

            verify(objectArchiveService, never()).find(any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试delete方法，验证删除归档规则的情况
     */
    @Test
    @DisplayName("测试delete - 正常删除归档规则")
    void testDelete_Success() {
        // Arrange
        ArchiveRuleOperate.Arg arg = new ArchiveRuleOperate.Arg();
        arg.setRuleApiName("rule_to_delete");

        // Act
        ArchiveRuleOperate.Result result = archiveRuleService.delete(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(objectArchiveService).delete(testUser, "rule_to_delete");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试delete方法，验证空ruleApiName抛出异常
     */
    @Test
    @DisplayName("测试delete - 空ruleApiName抛出异常")
    void testDelete_EmptyRuleApiName() {
        // Arrange
        ArchiveRuleOperate.Arg arg = new ArchiveRuleOperate.Arg();
        arg.setRuleApiName("");

        // Act & Assert
        assertThrows(ValidateException.class, () -> 
            archiveRuleService.delete(serviceContext, arg)
        );
        
        verify(objectArchiveService, never()).delete(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enable方法，验证启用归档规则的情况
     */
    @Test
    @DisplayName("测试enable - 正常启用归档规则")
    void testEnable_Success() {
        // Arrange
        ArchiveRuleOperate.Arg arg = new ArchiveRuleOperate.Arg();
        arg.setRuleApiName("rule_to_enable");

        // Act
        ArchiveRuleOperate.Result result = archiveRuleService.enable(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(objectArchiveService).enable(testUser, "rule_to_enable");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试disable方法，验证禁用归档规则的情况
     */
    @Test
    @DisplayName("测试disable - 正常禁用归档规则")
    void testDisable_Success() {
        // Arrange
        ArchiveRuleOperate.Arg arg = new ArchiveRuleOperate.Arg();
        arg.setRuleApiName("rule_to_disable");

        // Act
        ArchiveRuleOperate.Result result = archiveRuleService.disable(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(objectArchiveService).disable(testUser, "rule_to_disable");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试create方法，验证多个规则的处理
     */
    @Test
    @DisplayName("测试create - 多个规则")
    void testCreate_MultipleRules() {
        // Arrange
        ArchiveRule.Arg arg = new ArchiveRule.Arg();
        List<MappingRuleDocument> ruleList = new ArrayList<>();
        
        MappingRuleDocument rule1 = new MappingRuleDocument();
        rule1.put("ruleName", "Rule 1");
        rule1.put("ruleApiName", "rule_1");
        rule1.put("where_type", "custom");
        ruleList.add(rule1);

        MappingRuleDocument rule2 = new MappingRuleDocument();
        rule2.put("ruleName", "Rule 2");
        rule2.put("ruleApiName", "rule_2");
        rule2.put("where_type", "all");
        ruleList.add(rule2);
        
        arg.setRuleList(ruleList);

        // Act
        archiveRuleService.create(serviceContext, arg);

        // Assert
        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<IObjectArchiveRuleInfo>> captor = ArgumentCaptor.forClass(List.class);
        verify(objectArchiveService).create(eq(testUser), captor.capture());
        
        List<IObjectArchiveRuleInfo> capturedRules = captor.getValue();
        assertEquals(2, capturedRules.size());
        
        // 验证每个规则都设置了正确的属性
        for (IObjectArchiveRuleInfo rule : capturedRules) {
            assertEquals(tenantId, rule.getTenantId());
            assertEquals("12345", rule.getCreatedBy());
            assertEquals("12345", rule.getLastModifiedBy());
        }
        
        // 验证第二个规则（whereType为all）的基本属性
        IObjectArchiveRuleInfo rule2Info = capturedRules.get(1);
        assertEquals("all", rule2Info.getWhereType());
    }
}