package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.clean.GetCleanInfo;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.SaveDuplicatedSearch;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.DuplicatedSearchService;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectCleanService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectCleanService单元测试")
class ObjectCleanServiceTest {

    @Mock
    private DuplicatedSearchService duplicatedSearchService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @InjectMocks
    private ObjectCleanService objectCleanService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "test_object__c";
    private final String RULE_ID = "rule_123";
    private final String RULE_API_NAME = "test_rule__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "clean", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存清洗规则成功的正常场景
     */
    @Test
    @DisplayName("测试save清洗规则成功")
    void testSaveCleanRuleSuccess() {
        // Arrange
        SaveDuplicatedSearch.Arg arg = new SaveDuplicatedSearch.Arg();
        IDuplicatedSearch mockDuplicateSearch = mock(IDuplicatedSearch.class);
        arg.setDuplicateSearch(mockDuplicateSearch);

        IDuplicatedSearch mockSavedSearch = mock(IDuplicatedSearch.class);

        when(duplicatedSearchService.createOrUpdateCleanRule(eq(user), eq(mockDuplicateSearch), eq(false)))
                .thenReturn(mockSavedSearch);

        // Act
        SaveDuplicatedSearch.Result result = objectCleanService.save(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDuplicateSearch());
        assertEquals(mockSavedSearch, result.getDuplicateSearch());
        
        // Verify that the duplicateSearch was properly configured
        verify(mockDuplicateSearch).setTenantId(TENANT_ID);
        verify(mockDuplicateSearch).setOperatorId(USER_ID);
        verify(mockDuplicateSearch).setEffective(false);
        verify(duplicatedSearchService).createOrUpdateCleanRule(eq(user), eq(mockDuplicateSearch), eq(false));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询清洗规则成功的正常场景
     */
    @Test
    @DisplayName("测试getCleanRule查询清洗规则成功")
    void testGetCleanRuleSuccess() {
        // Arrange
        GetCleanInfo.Arg arg = new GetCleanInfo.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // Mock IObjectDescribe
        IObjectDescribe mockObjectDescribe = mock(IObjectDescribe.class);
        IFieldDescribe mockField1 = mock(IFieldDescribe.class);
        IFieldDescribe mockField2 = mock(IFieldDescribe.class);
        when(mockField1.getApiName()).thenReturn("field1__c");
        when(mockField2.getApiName()).thenReturn("field2__c");
        List<IFieldDescribe> mockFields = Arrays.asList(mockField1, mockField2);
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(mockFields);

        // Mock IDuplicatedSearch
        IDuplicatedSearch mockCleanRule = mock(IDuplicatedSearch.class);
        IDuplicatedSearch.RulesDef mockRulesDef = mock(IDuplicatedSearch.RulesDef.class);
        List<String> showFields = new ArrayList<>(Arrays.asList("field1__c", "field2__c", "invalid_field__c"));
        
        when(mockCleanRule.getId()).thenReturn(RULE_ID);
        when(mockCleanRule.getVersion()).thenReturn(1);
        when(mockCleanRule.isEnable()).thenReturn(true);
        when(mockCleanRule.getName()).thenReturn("Test Clean Rule");
        when(mockCleanRule.getRuleApiName()).thenReturn(RULE_API_NAME);
        when(mockCleanRule.getPendingRules()).thenReturn(mockRulesDef);
        when(mockRulesDef.getShowFields()).thenReturn(showFields);

        Map<IDuplicatedSearch.Type, IDuplicatedSearch> mockSearchMap = new HashMap<>();
        mockSearchMap.put(IDuplicatedSearch.Type.CLEAN, mockCleanRule);

        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectDescribe);
        when(duplicatedSearchService.findDuplicatedSearchByApiName(user, DESCRIBE_API_NAME, true))
                .thenReturn(mockSearchMap);

        // Act
        GetCleanInfo.Result result = objectCleanService.GetCleanInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(RULE_ID, result.getId());
        assertEquals(DESCRIBE_API_NAME, result.getDescribeApiName());
        assertEquals(1, result.getVersion());
        
        assertNotNull(result.getCleanRule());
        assertTrue(result.getCleanRule().getEnable());
        assertEquals(IDuplicatedSearch.Type.CLEAN, result.getCleanRule().getType());
        assertEquals("Test Clean Rule", result.getCleanRule().getName());
        assertEquals(RULE_API_NAME, result.getCleanRule().getRuleApiName());
        assertEquals(mockRulesDef, result.getCleanRule().getPendingRules());

        verify(describeLogicService).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(duplicatedSearchService).findDuplicatedSearchByApiName(user, DESCRIBE_API_NAME, true);
        
        // Verify that invalid fields are filtered out
        verify(mockRulesDef).getShowFields();
        // The showFields list should have been modified to remove invalid_field__c
        assertEquals(2, showFields.size()); // Only field1__c and field2__c should remain
        assertFalse(showFields.contains("invalid_field__c"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询清洗规则时对象描述为空的边界场景
     */
    @Test
    @DisplayName("测试getCleanRule对象描述字段为空")
    void testGetCleanRuleWithEmptyFields() {
        // Arrange
        GetCleanInfo.Arg arg = new GetCleanInfo.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // Mock IObjectDescribe with empty fields
        IObjectDescribe mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(Arrays.asList());

        // Mock IDuplicatedSearch
        IDuplicatedSearch mockCleanRule = mock(IDuplicatedSearch.class);
        IDuplicatedSearch.RulesDef mockRulesDef = mock(IDuplicatedSearch.RulesDef.class);
        List<String> showFields = new ArrayList<>(Arrays.asList("field1__c", "field2__c"));
        
        when(mockCleanRule.getId()).thenReturn(RULE_ID);
        when(mockCleanRule.getVersion()).thenReturn(1);
        when(mockCleanRule.isEnable()).thenReturn(false);
        when(mockCleanRule.getName()).thenReturn("Empty Clean Rule");
        when(mockCleanRule.getRuleApiName()).thenReturn(RULE_API_NAME);
        when(mockCleanRule.getPendingRules()).thenReturn(mockRulesDef);
        when(mockRulesDef.getShowFields()).thenReturn(showFields);

        Map<IDuplicatedSearch.Type, IDuplicatedSearch> mockSearchMap = new HashMap<>();
        mockSearchMap.put(IDuplicatedSearch.Type.CLEAN, mockCleanRule);

        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectDescribe);
        when(duplicatedSearchService.findDuplicatedSearchByApiName(user, DESCRIBE_API_NAME, true))
                .thenReturn(mockSearchMap);

        // Act
        GetCleanInfo.Result result = objectCleanService.GetCleanInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(RULE_ID, result.getId());
        assertEquals(DESCRIBE_API_NAME, result.getDescribeApiName());
        assertEquals(1, result.getVersion());
        
        assertNotNull(result.getCleanRule());
        assertFalse(result.getCleanRule().getEnable());
        assertEquals("Empty Clean Rule", result.getCleanRule().getName());

        verify(describeLogicService).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(duplicatedSearchService).findDuplicatedSearchByApiName(user, DESCRIBE_API_NAME, true);
        
        // All fields should be removed since object has no fields
        assertTrue(showFields.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询清洗规则时RulesDef为null的边界场景
     */
    @Test
    @DisplayName("测试getCleanRule RulesDef为null")
    void testGetCleanRuleWithNullRulesDef() {
        // Arrange
        GetCleanInfo.Arg arg = new GetCleanInfo.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // Mock IObjectDescribe
        IObjectDescribe mockObjectDescribe = mock(IObjectDescribe.class);
        IFieldDescribe mockField = mock(IFieldDescribe.class);
        when(mockField.getApiName()).thenReturn("field1__c");
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(Arrays.asList(mockField));

        // Mock IDuplicatedSearch with null RulesDef
        IDuplicatedSearch mockCleanRule = mock(IDuplicatedSearch.class);
        IDuplicatedSearch.RulesDef mockRulesDef = mock(IDuplicatedSearch.RulesDef.class);
        List<String> showFields = new ArrayList<>(); // Empty list

        when(mockCleanRule.getId()).thenReturn(RULE_ID);
        when(mockCleanRule.getVersion()).thenReturn(1);
        when(mockCleanRule.isEnable()).thenReturn(true);
        when(mockCleanRule.getName()).thenReturn("Null Rules Clean Rule");
        when(mockCleanRule.getRuleApiName()).thenReturn(RULE_API_NAME);
        when(mockCleanRule.getPendingRules()).thenReturn(mockRulesDef);
        when(mockRulesDef.getShowFields()).thenReturn(showFields);

        Map<IDuplicatedSearch.Type, IDuplicatedSearch> mockSearchMap = new HashMap<>();
        mockSearchMap.put(IDuplicatedSearch.Type.CLEAN, mockCleanRule);

        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectDescribe);
        when(duplicatedSearchService.findDuplicatedSearchByApiName(user, DESCRIBE_API_NAME, true))
                .thenReturn(mockSearchMap);

        // Act
        GetCleanInfo.Result result = objectCleanService.GetCleanInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(RULE_ID, result.getId());
        assertEquals(DESCRIBE_API_NAME, result.getDescribeApiName());
        assertEquals(1, result.getVersion());

        assertNotNull(result.getCleanRule());
        assertTrue(result.getCleanRule().getEnable());
        assertEquals("Null Rules Clean Rule", result.getCleanRule().getName());
        assertNotNull(result.getCleanRule().getPendingRules());

        verify(describeLogicService).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(duplicatedSearchService).findDuplicatedSearchByApiName(user, DESCRIBE_API_NAME, true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectCleanService);
        assertNotNull(duplicatedSearchService);
        assertNotNull(describeLogicService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存清洗规则时参数配置的正确性
     */
    @Test
    @DisplayName("测试save方法参数配置正确性")
    void testSaveParameterConfiguration() {
        // Arrange
        SaveDuplicatedSearch.Arg arg = new SaveDuplicatedSearch.Arg();
        IDuplicatedSearch mockDuplicateSearch = mock(IDuplicatedSearch.class);
        arg.setDuplicateSearch(mockDuplicateSearch);

        IDuplicatedSearch mockSavedSearch = mock(IDuplicatedSearch.class);
        when(duplicatedSearchService.createOrUpdateCleanRule(any(), any(), anyBoolean()))
                .thenReturn(mockSavedSearch);

        // Act
        objectCleanService.save(arg, serviceContext);

        // Assert - Verify the exact parameter values
        verify(mockDuplicateSearch).setTenantId(TENANT_ID);
        verify(mockDuplicateSearch).setOperatorId(USER_ID);
        verify(mockDuplicateSearch).setEffective(false); // Always set to false for clean rules
        verify(duplicatedSearchService).createOrUpdateCleanRule(user, mockDuplicateSearch, false);
    }
}
