package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.model.enums.ObjectActionInfo;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.log.BatchQueryObjectActions;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetLogModuleGroup;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetTenantLogInterval;
import com.facishare.paas.appframework.log.dto.LogAnalysis;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.lenient;

/**
 * ObjectLogService 单元测试
 * 从 Groovy Spock 测试迁移到 JUnit5
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectLogService 测试")
class ObjectLogServiceGroovyTest {

    private ObjectLogService objectLogService;
    private final String tenantId = "74255";

    @Mock
    private ServiceContext context;

    @Mock
    private ServiceFacade serviceFacade;

    @BeforeAll
    static void setupSpec() throws Exception {
        // 创建 mock 实例
        I18nClient i18nClient = mock(I18nClient.class);
        I18nServiceImpl i18nServiceImpl = mock(I18nServiceImpl.class);

        // 给 mock 设置返回值
        when(i18nClient.getAllLanguage()).thenReturn(Collections.emptyList());

        // 设置内部字段 impl
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl);

        // 设置 SINGLETON
        Whitebox.setInternalState(I18nClient.class, "SINGLETON", i18nClient);
    }

    @BeforeEach
    void setup() throws Exception {
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(User.systemUser(tenantId))
                .build();
        RequestContextManager.setContext(requestContext);
        objectLogService = new ObjectLogService();
        context = mock(ServiceContext.class);

        // 注入 serviceFacade
        Whitebox.setInternalState(objectLogService, "serviceFacade", serviceFacade);

        // Mock serviceFacade.findObject 方法 - 使用 lenient 避免不必要的 stubbing 错误
        ObjectDescribe mockObjectDescribe = new ObjectDescribe();
        mockObjectDescribe.setApiName("EmployeeObjectUsage");
        mockObjectDescribe.setDisplayName("员工对象使用情况");
        lenient().when(serviceFacade.findObject(anyString(), anyString())).thenReturn(mockObjectDescribe);
        lenient().when(serviceFacade.findDescribeListWithoutFields(anyString(), any())).thenReturn(Collections.emptyList());
    }

    @Test
    @DisplayName("测试 getLogModuleGroup")
    void testGetLogModuleGroup() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getLogModuleGroup");
        GetLogModuleGroup.Arg arg = new GetLogModuleGroup.Arg();
        
        // When & Then
        assertDoesNotThrow(() -> objectLogService.getLogModuleGroup(arg, context));
    }

    @Test
    @DisplayName("测试 updateLogAnalysis")
    void testUpdateLogAnalysis() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "updateLogAnalysis");
        LogAnalysis.OperationLog operationLog = new LogAnalysis.OperationLog();
        operationLog.setDescribeApiName("AccountObj");
        operationLog.setOperation(Arrays.asList("1", "2"));
        
        LogAnalysis.Arg arg = new LogAnalysis.Arg();
        arg.setLoginLog(true);
        arg.setOperationLog(true);
        arg.setOperationLogArgs(Arrays.asList(operationLog));
        
        // When & Then
        assertDoesNotThrow(() -> objectLogService.updateLogAnalysis(arg, context));
    }

    @Test
    @DisplayName("测试 findLogAnalysis")
    void testFindLogAnalysis() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "findLogAnalysis");
        LogAnalysis.Arg arg = new LogAnalysis.Arg();
        
        // When & Then
        assertDoesNotThrow(() -> objectLogService.findLogAnalysis(arg, context));
    }

    @Test
    @DisplayName("测试 findOperationLogRelationship")
    void testFindOperationLogRelationship() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "findOperationLogRelationship");
        LogAnalysis.Arg arg = new LogAnalysis.Arg();
        
        // When & Then
        assertDoesNotThrow(() -> objectLogService.findOperationLogRelationship(arg, context));
    }

    @Test
    @DisplayName("测试 getTenantLogInterval")
    void testGetTenantLogInterval() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getTenantLogInterval");
        GetTenantLogInterval.Arg arg = new GetTenantLogInterval.Arg();
        arg.setLogType("crm_log");
        
        // When & Then
        assertDoesNotThrow(() -> objectLogService.getTenantLogInterval(arg, context));
    }

    @ParameterizedTest
    @MethodSource("provideBatchQueryObjectActionsTestData")
    @DisplayName("测试 batchQueryObjectActions 参数化测试")
    void testBatchQueryObjectActions(String scenario, List<String> actionCodes, int expectedSize, 
                                   String expectedActionCode, String expectedActionLabelKey, 
                                   String expectedDefaultActionLabel, String expectedButtonApiName, 
                                   String expectedActionLabel) {
        // Given
        BatchQueryObjectActions.Arg arg = new BatchQueryObjectActions.Arg();
        arg.setActionCodes(actionCodes);

        // When
        BatchQueryObjectActions.Result result = objectLogService.batchQueryObjectActions(arg, context);

        // Then
        assertEquals(expectedSize, result.getActionInfos().size());
        if (expectedSize > 0) {
            ObjectActionInfo actionInfo = result.getActionInfos().get(0);
            assertEquals(expectedActionCode, actionInfo.getActionCode());
            assertEquals(expectedActionLabelKey, actionInfo.getActionLabelKey());
            assertEquals(expectedDefaultActionLabel, actionInfo.getDefaultActionLabel());
            assertEquals(expectedButtonApiName, actionInfo.getButtonApiName());
            assertEquals(expectedActionLabel, actionInfo.getActionLabel());
        }
    }

    static Stream<Arguments> provideBatchQueryObjectActionsTestData() {
        return Stream.of(
            Arguments.of("空列表", Collections.emptyList(), 0, null, null, null, null, null),
            Arguments.of("null列表", null, 0, null, null, null, null, null),
            Arguments.of("未知action", Arrays.asList("UnknownAction"), 0, null, null, null, null, null),
            Arguments.of("单个有效action", Arrays.asList("Delete"), 1, "Delete", "paas.udobj.action.delete", "删除", "Delete_button_default", "删除"),
            Arguments.of("多个action", Arrays.asList("Delete", "Add"), 2, "Delete", "paas.udobj.action.delete", "删除", "Delete_button_default", "删除"),
            Arguments.of("特殊字符action", Arrays.asList("Delete__c"), 0, null, null, null, null, null),
            Arguments.of("空字符串action", Arrays.asList(""), 0, null, null, null, null, null),
            Arguments.of("中文action", Arrays.asList("删除"), 0, null, null, null, null, null)
        );
    }

    @Test
    @DisplayName("测试 batchQueryObjectActions 多个有效actions")
    void testBatchQueryObjectActionsWithMultipleValidActions() {
        // Given
        BatchQueryObjectActions.Arg arg = new BatchQueryObjectActions.Arg();
        arg.setActionCodes(Arrays.asList("Delete", "Add", "Edit"));

        // When
        BatchQueryObjectActions.Result result = objectLogService.batchQueryObjectActions(arg, context);

        // Then
        assertEquals(3, result.getActionInfos().size());

        // 验证每个action info的内容
        for (ObjectActionInfo actionInfo : result.getActionInfos()) {
            ObjectAction expectedAction = ObjectAction.of(actionInfo.getActionCode());
            assertEquals(expectedAction.getActionCode(), actionInfo.getActionCode());
            assertEquals(expectedAction.getI18NKey(), actionInfo.getActionLabelKey());
            assertEquals(expectedAction.getDefaultActionLabel(), actionInfo.getDefaultActionLabel());
            assertEquals(expectedAction.getButtonApiName(), actionInfo.getButtonApiName());
            assertEquals(expectedAction.getActionLabel(), actionInfo.getActionLabel());
        }
    }
}
