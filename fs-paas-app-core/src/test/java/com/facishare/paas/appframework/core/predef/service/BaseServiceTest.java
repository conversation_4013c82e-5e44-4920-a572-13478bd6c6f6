package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

/**
 * GenerateByAI
 * 统一的服务测试基类，提供通用的Mock配置和测试数据构造
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public abstract class BaseServiceTest {

    /**
     * 标准测试租户ID
     */
    protected static final String TENANT_ID = "74255";
    
    /**
     * 标准测试用户ID
     */
    protected static final String USER_ID = "1000";
    
    /**
     * 测试用户对象
     */
    protected User testUser;
    
    /**
     * 服务上下文对象
     */
    protected ServiceContext serviceContext;
    
    /**
     * 请求上下文对象
     */
    protected RequestContext requestContext;

    /**
     * 初始化测试基础数据
     * 在每个测试方法执行前调用
     */
    @BeforeEach
    void setUpBase() {
        // 创建测试用户，遵循框架标准示例
        testUser = new User(TENANT_ID, USER_ID);
        
        // 创建请求上下文，参考框架标准示例
        requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(testUser)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        
        // 创建服务上下文
        serviceContext = new ServiceContext(requestContext, getServiceName(), "test");
    }

    /**
     * 获取服务名称，由子类实现
     * 用于ServiceContext的构造
     * 
     * @return 服务名称
     */
    protected abstract String getServiceName();

    /**
     * 创建带有指定用户ID的User对象
     * 
     * @param userId 用户ID
     * @return User对象
     */
    protected User createUser(String userId) {
        return new User(TENANT_ID, userId);
    }

    /**
     * 创建带有指定租户ID和用户ID的User对象
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return User对象
     */
    protected User createUser(String tenantId, String userId) {
        return new User(tenantId, userId);
    }

    /**
     * 创建带有指定服务名称和方法名称的ServiceContext
     * 
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @return ServiceContext对象
     */
    protected ServiceContext createServiceContext(String serviceName, String methodName) {
        return new ServiceContext(requestContext, serviceName, methodName);
    }

    /**
     * 创建带有指定用户的ServiceContext
     * 
     * @param user 用户对象
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @return ServiceContext对象
     */
    protected ServiceContext createServiceContext(User user, String serviceName, String methodName) {
        RequestContext customRequestContext = RequestContext.builder()
                .tenantId(user.getTenantId())
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        return new ServiceContext(customRequestContext, serviceName, methodName);
    }
}
