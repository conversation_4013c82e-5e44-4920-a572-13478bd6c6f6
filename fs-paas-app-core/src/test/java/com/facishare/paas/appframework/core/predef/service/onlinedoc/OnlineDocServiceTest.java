package com.facishare.paas.appframework.core.predef.service.onlinedoc;

import com.facishare.paas.appframework.core.predef.service.BaseServiceTest;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.GetPreviewUrlVo;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.QueryFileListVo;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginAppType;
import com.facishare.paas.appframework.metadata.onlinedoc.DocLogicService;
import com.facishare.paas.appframework.metadata.onlinedoc.model.GetPreviewUrl;
import com.facishare.paas.appframework.metadata.onlinedoc.model.QueryFileList;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * OnlineDocService单元测试类
 * 测试在线文档服务的所有@ServiceMethod方法
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("OnlineDocService单元测试")
public class OnlineDocServiceTest extends BaseServiceTest {

    @Mock
    private DocLogicService docLogicService;

    @InjectMocks
    private OnlineDocService onlineDocService;

    private static final String PLUGIN_API_NAME = "test_plugin";
    private static final String FILE_ID = "test_file_id";
    private static final String FILE_NAME = "test_document.docx";
    private static final String FILE_URL = "https://example.com/file.docx";
    private static final String GROUP_ID = "test_group_id";
    private static final String GROUP_NAME = "test_group";
    private static final Long CREATE_TIME = 1624512345L;
    private static final Long UPDATE_TIME = 1624512400L;

    @Override
    protected String getServiceName() {
        return "onlineDoc";
    }

    @BeforeEach
    void setUp() {
        // 基础设置已在BaseServiceTest中完成
    }

    @Test
    @DisplayName("GenerateByAI - 测试queryFileList成功场景")
    void testQueryFileList_Success() {
        // Arrange
        QueryFileListVo.Arg arg = new QueryFileListVo.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);
        arg.setNeedNextPage(false);
        arg.setExtraInfo(new HashMap<String, Object>());

        // Mock返回结果
        QueryFileList.Result mockResult = new QueryFileList.Result();
        mockResult.setErrorCode(0);
        mockResult.setErrorMessage("");
        mockResult.setHasMore(false);
        mockResult.setAuthUrl("https://auth.example.com");
        mockResult.setExtraInfo(new HashMap<String, Object>());

        // Mock文件列表
        QueryFileList.File mockFile = new QueryFileList.File();
        mockFile.setFileId(FILE_ID);
        mockFile.setFileName(FILE_NAME);
        mockFile.setFileUrl(FILE_URL);
        mockFile.setFileType("file");
        mockFile.setFileExt("docx");
        mockFile.setFileSize(1024);
        mockFile.setCreateTime(CREATE_TIME);
        mockFile.setUpdateTime(UPDATE_TIME);
        mockFile.setPluginApiName(PLUGIN_API_NAME);
        mockResult.setFileList(Lists.newArrayList(mockFile));

        // Mock团队列表
        QueryFileList.Group mockGroup = new QueryFileList.Group();
        mockGroup.setId(GROUP_ID);
        mockGroup.setName(GROUP_NAME);
        mockGroup.setCreateTime(CREATE_TIME);
        mockGroup.setUpdateTime(UPDATE_TIME);
        mockResult.setGroupList(Lists.newArrayList(mockGroup));

        when(docLogicService.queryFileList(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                eq(false),
                isNull(),
                isNull(),
                any(Map.class)
        )).thenReturn(mockResult);

        // Act
        QueryFileListVo.Result result = onlineDocService.queryFileList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getErrorCode());
        assertEquals("", result.getErrorMessage());
        assertEquals("https://auth.example.com", result.getAuthUrl());
        assertFalse(result.isHasMore());
        assertNotNull(result.getFileList());
        assertEquals(1, result.getFileList().size());
        
        QueryFileListVo.FileInfo fileInfo = result.getFileList().get(0);
        assertEquals(FILE_ID, fileInfo.getFileId());
        assertEquals(FILE_NAME, fileInfo.getFileName());
        assertEquals(FILE_URL, fileInfo.getFileUrl());
        assertEquals("file", fileInfo.getFileType());
        assertEquals("docx", fileInfo.getFileExt());
        assertEquals(1024, fileInfo.getFileSize());
        assertEquals(CREATE_TIME, fileInfo.getCreateTime());
        assertEquals(UPDATE_TIME, fileInfo.getUpdateTime());
        assertEquals(PLUGIN_API_NAME, fileInfo.getPluginApiName());

        assertNotNull(result.getGroupList());
        assertEquals(1, result.getGroupList().size());
        
        QueryFileListVo.GroupInfo groupInfo = result.getGroupList().get(0);
        assertEquals(GROUP_ID, groupInfo.getId());
        assertEquals(GROUP_NAME, groupInfo.getName());
        assertEquals(CREATE_TIME, groupInfo.getCreateTime());
        assertEquals(UPDATE_TIME, groupInfo.getUpdateTime());

        verify(docLogicService, times(1)).queryFileList(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                eq(false),
                isNull(),
                isNull(),
                any(Map.class)
        );
    }

    @Test
    @DisplayName("GenerateByAI - 测试queryFileList空结果场景")
    void testQueryFileList_EmptyResult() {
        // Arrange
        QueryFileListVo.Arg arg = new QueryFileListVo.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);
        arg.setNeedNextPage(false);

        // Mock返回空结果
        QueryFileList.Result mockResult = new QueryFileList.Result();
        mockResult.setErrorCode(0);
        mockResult.setErrorMessage("");
        mockResult.setHasMore(false);
        mockResult.setFileList(null); // 空文件列表
        mockResult.setGroupList(null); // 空团队列表

        when(docLogicService.queryFileList(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                eq(false),
                isNull(),
                isNull(),
                isNull()
        )).thenReturn(mockResult);

        // Act
        QueryFileListVo.Result result = onlineDocService.queryFileList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getErrorCode());
        assertNotNull(result.getFileList());
        assertTrue(result.getFileList().isEmpty());
        assertNotNull(result.getGroupList());
        assertTrue(result.getGroupList().isEmpty());

        verify(docLogicService, times(1)).queryFileList(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                eq(false),
                isNull(),
                isNull(),
                isNull()
        );
    }

    @Test
    @DisplayName("GenerateByAI - 测试queryFileList带文件夹查询")
    void testQueryFileList_WithFolder() {
        // Arrange
        QueryFileListVo.Arg arg = new QueryFileListVo.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);
        arg.setNeedNextPage(true);

        // 设置文件夹信息
        QueryFileListVo.FileInfo folderInfo = new QueryFileListVo.FileInfo();
        folderInfo.setFileId("folder_id");
        folderInfo.setFileType("folder");
        arg.setFolder(folderInfo);

        // 设置团队信息
        QueryFileListVo.GroupInfo groupInfo = new QueryFileListVo.GroupInfo();
        groupInfo.setId(GROUP_ID);
        groupInfo.setName(GROUP_NAME);
        arg.setGroup(groupInfo);

        // Mock返回结果
        QueryFileList.Result mockResult = new QueryFileList.Result();
        mockResult.setErrorCode(0);
        mockResult.setHasMore(true);
        mockResult.setFileList(Lists.newArrayList());
        mockResult.setGroupList(Lists.newArrayList());

        when(docLogicService.queryFileList(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                eq(true),
                any(QueryFileList.File.class),
                any(QueryFileList.Group.class),
                isNull()
        )).thenReturn(mockResult);

        // Act
        QueryFileListVo.Result result = onlineDocService.queryFileList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getErrorCode());
        assertTrue(result.isHasMore());
        assertNotNull(result.getFileList());
        assertNotNull(result.getGroupList());

        verify(docLogicService, times(1)).queryFileList(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                eq(true),
                any(QueryFileList.File.class),
                any(QueryFileList.Group.class),
                isNull()
        );
    }

    @Test
    @DisplayName("GenerateByAI - 测试getPreviewUrl成功场景")
    void testGetPreviewUrl_Success() {
        // Arrange
        GetPreviewUrlVo.Arg arg = new GetPreviewUrlVo.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);

        // 设置文件信息
        QueryFileListVo.FileInfo fileInfo = new QueryFileListVo.FileInfo();
        fileInfo.setFileId(FILE_ID);
        fileInfo.setFileUrl(FILE_URL);
        fileInfo.setFileName(FILE_NAME);
        fileInfo.setFileType("file");
        fileInfo.setFileSize(1024);
        fileInfo.setCreateTime(CREATE_TIME);
        fileInfo.setUpdateTime(UPDATE_TIME);
        arg.setFileInfo(fileInfo);

        // Mock返回结果
        GetPreviewUrl.Result mockResult = new GetPreviewUrl.Result();
        mockResult.setErrorCode(0);
        mockResult.setErrorMessage("");
        mockResult.setFileUrl("https://preview.example.com/file.docx");

        when(docLogicService.getPreviewUrl(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                isNull(),
                any(QueryFileList.File.class)
        )).thenReturn(mockResult);

        // Act
        GetPreviewUrlVo.Result result = onlineDocService.getPreviewUrl(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getErrorCode());
        assertEquals("", result.getErrorMessage());
        assertEquals("https://preview.example.com/file.docx", result.getPreviewUrl());

        verify(docLogicService, times(1)).getPreviewUrl(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                isNull(),
                any(QueryFileList.File.class)
        );
    }

    @Test
    @DisplayName("GenerateByAI - 测试getPreviewUrl带团队信息")
    void testGetPreviewUrl_WithGroup() {
        // Arrange
        GetPreviewUrlVo.Arg arg = new GetPreviewUrlVo.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);

        // 设置文件信息
        QueryFileListVo.FileInfo fileInfo = new QueryFileListVo.FileInfo();
        fileInfo.setFileId(FILE_ID);
        fileInfo.setFileUrl(FILE_URL);
        fileInfo.setFileName(FILE_NAME);
        fileInfo.setFileType("file");
        fileInfo.setFileSize(1024);
        fileInfo.setCreateTime(CREATE_TIME);
        fileInfo.setUpdateTime(UPDATE_TIME);
        arg.setFileInfo(fileInfo);

        // 设置团队信息
        QueryFileListVo.GroupInfo groupInfo = new QueryFileListVo.GroupInfo();
        groupInfo.setId(GROUP_ID);
        groupInfo.setName(GROUP_NAME);
        groupInfo.setCreateTime(CREATE_TIME);
        groupInfo.setUpdateTime(UPDATE_TIME);
        arg.setGroupInfo(groupInfo);

        // Mock返回结果
        GetPreviewUrl.Result mockResult = new GetPreviewUrl.Result();
        mockResult.setErrorCode(0);
        mockResult.setErrorMessage("");
        mockResult.setFileUrl("https://preview.example.com/group/file.docx");

        when(docLogicService.getPreviewUrl(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                any(QueryFileList.Group.class),
                any(QueryFileList.File.class)
        )).thenReturn(mockResult);

        // Act
        GetPreviewUrlVo.Result result = onlineDocService.getPreviewUrl(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getErrorCode());
        assertEquals("", result.getErrorMessage());
        assertEquals("https://preview.example.com/group/file.docx", result.getPreviewUrl());

        verify(docLogicService, times(1)).getPreviewUrl(
                eq(testUser),
                eq(PackagePluginAppType.ONLINE_DOC.getType()),
                eq(PLUGIN_API_NAME),
                any(QueryFileList.Group.class),
                any(QueryFileList.File.class)
        );
    }
}
