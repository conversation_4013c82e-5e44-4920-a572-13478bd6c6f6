package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.queryscene.*;
import com.facishare.paas.appframework.metadata.CustomSceneService;
import com.facishare.paas.appframework.metadata.SceneLogicService;
import com.facishare.paas.appframework.metadata.SearchTemplateLogicService;
import com.facishare.paas.appframework.metadata.dto.CustomSceneResult;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectQuerySceneService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectQuerySceneService单元测试")
class ObjectQuerySceneServiceTest {

    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private InfraServiceFacade infraServiceFacade;
    
    @Mock
    private CustomSceneService customSceneService;
    
    @Mock
    private SearchTemplateLogicService searchTemplateLogicService;
    
    @Mock
    private SceneLogicService sceneLogicService;
    
    @Mock
    private ServiceContext serviceContext;
    
    @InjectMocks
    private ObjectQuerySceneService service;
    
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        when(serviceContext.getUser()).thenReturn(user);
        when(serviceContext.getTenantId()).thenReturn(TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createCustomScene方法
     */
    @Test
    @DisplayName("测试createCustomScene方法")
    void testCreateCustomScene() {
        // Arrange
        CreateCustomScene.Arg arg = new CreateCustomScene.Arg();
        arg.setScene_data("test_scene_data");

        CustomSceneResult mockResult = new CustomSceneResult();
        Map<String, Object> searchTemplate = new HashMap<>();
        searchTemplate.put("id", "test_id");
        searchTemplate.put("name", "test_scene");
        mockResult.setSearchTemplate(searchTemplate);

        when(customSceneService.create(eq("test_scene_data"), any(User.class))).thenReturn(mockResult);

        // Act
        CreateCustomScene.Result result = service.createCustomScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSearchTemplate());
        assertEquals("test_id", result.getSearchTemplate().get("id"));
        verify(customSceneService).create(eq("test_scene_data"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateCustomScene方法
     */
    @Test
    @DisplayName("测试updateCustomScene方法")
    void testUpdateCustomScene() {
        // Arrange
        UpdateCustomScene.Arg arg = new UpdateCustomScene.Arg();
        arg.setScene_data("updated_scene_data");

        CustomSceneResult mockResult = new CustomSceneResult();
        Map<String, Object> searchTemplate = new HashMap<>();
        searchTemplate.put("id", "updated_id");
        searchTemplate.put("name", "updated_scene");
        mockResult.setSearchTemplate(searchTemplate);

        when(customSceneService.update(eq("updated_scene_data"), any(User.class))).thenReturn(mockResult);

        // Act
        UpdateCustomScene.Result result = service.updateCustomScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSearchTemplate());
        assertTrue(result.isSuccess());
        assertEquals("updated_id", result.getSearchTemplate().get("id"));
        verify(customSceneService).update(eq("updated_scene_data"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findCustomSceneById方法
     */
    @Test
    @DisplayName("测试findCustomSceneById方法")
    void testFindCustomSceneById() {
        // Arrange
        FindCustomeSceneById.Arg arg = new FindCustomeSceneById.Arg();
        arg.setScene_id("scene_123");
        arg.setDescribe_api_name("AccountObj");
        arg.setScene_type("custom");

        CustomSceneResult mockResult = new CustomSceneResult();
        Map<String, Object> searchTemplate = new HashMap<>();
        searchTemplate.put("id", "scene_123");
        searchTemplate.put("name", "test_scene");
        mockResult.setSearchTemplate(searchTemplate);

        when(customSceneService.findByIdWithMerge(eq("scene_123"), eq("AccountObj"), eq("custom"), any(User.class)))
                .thenReturn(mockResult);
        when(sceneLogicService.fillBaseSceneLabel(any(), eq("AccountObj"), any(User.class), any()))
                .thenReturn("Base Scene Label");

        // Act
        FindCustomeSceneById.Result result = service.findCustomSceneById(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSearchTemplate());
        assertTrue(result.isSuccess());
        assertEquals("scene_123", result.getSearchTemplate().get("id"));
        verify(customSceneService).findByIdWithMerge(eq("scene_123"), eq("AccountObj"), eq("custom"), any(User.class));
        verify(sceneLogicService).fillBaseSceneLabel(any(), eq("AccountObj"), any(User.class), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试adjustCustomSceneOrder方法
     */
    @Test
    @DisplayName("测试adjustCustomSceneOrder方法")
    void testAdjustCustomSceneOrder() {
        // Arrange
        AdjustCustomSceneOrder.Arg arg = new AdjustCustomSceneOrder.Arg();
        arg.setScene_data("order_scene_data");
        arg.setDescribe_api_name("AccountObj");

        CustomSceneResult mockResult = new CustomSceneResult();
        Map<String, Object> searchTemplate = new HashMap<>();
        searchTemplate.put("order", 1);
        mockResult.setSearchTemplate(searchTemplate);

        when(customSceneService.adjustOrder(eq("order_scene_data"), eq("AccountObj"), any(User.class)))
                .thenReturn(mockResult);

        // Act
        AdjustCustomSceneOrder.Result result = service.adjustCustomSceneOrder(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSearchTemplate());
        assertTrue(result.isSuccess());
        assertEquals(1, result.getSearchTemplate().get("order"));
        verify(customSceneService).adjustOrder(eq("order_scene_data"), eq("AccountObj"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findBaseSceneList方法
     */
    @Test
    @DisplayName("测试findBaseSceneList方法")
    void testFindBaseSceneList() {
        // Arrange
        FindBaseScene.Arg arg = new FindBaseScene.Arg();
        arg.setDescribeApiName("AccountObj");

        IScene mockScene = mock(IScene.class);
        when(mockScene.getApiName()).thenReturn("base_scene");
        when(mockScene.getDisplayName()).thenReturn("Base Scene");
        
        List<IScene> mockScenes = Arrays.asList(mockScene);
        when(sceneLogicService.findBaseScenes(eq("AccountObj"), isNull(), any(User.class)))
                .thenReturn(mockScenes);

        // Act
        FindBaseScene.Result result = service.findBaseSceneList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getBaseScenes());
        assertEquals(1, result.getBaseScenes().size());
        assertEquals("base_scene", result.getBaseScenes().get(0).getApiName());
        verify(sceneLogicService).findBaseScenes(eq("AccountObj"), isNull(), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setDefaultScene方法
     */
    @Test
    @DisplayName("测试setDefaultScene方法")
    void testSetDefaultScene() {
        // Arrange
        UpdateCustomScene.Arg arg = new UpdateCustomScene.Arg();
        arg.setScene_data("default_scene_data");

        CustomSceneResult mockResult = new CustomSceneResult();
        Map<String, Object> searchTemplate = new HashMap<>();
        searchTemplate.put("isDefault", true);
        mockResult.setSearchTemplate(searchTemplate);

        when(customSceneService.setDefaultScene(eq("default_scene_data"), any(User.class)))
                .thenReturn(mockResult);

        // Act
        UpdateCustomScene.Result result = service.setDefaultScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSearchTemplate());
        assertTrue(result.isSuccess());
        assertTrue((Boolean) result.getSearchTemplate().get("isDefault"));
        verify(customSceneService).setDefaultScene(eq("default_scene_data"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试saveAs方法
     */
    @Test
    @DisplayName("测试saveAs方法")
    void testSaveAs() {
        // Arrange
        CreateCustomScene.Arg arg = new CreateCustomScene.Arg();
        arg.setScene_data("save_as_scene_data");

        CustomSceneResult mockResult = new CustomSceneResult();
        Map<String, Object> searchTemplate = new HashMap<>();
        searchTemplate.put("id", "new_scene_id");
        searchTemplate.put("name", "Saved Scene");
        mockResult.setSearchTemplate(searchTemplate);

        when(customSceneService.saveAs(eq("save_as_scene_data"), any(User.class))).thenReturn(mockResult);

        // Act
        CreateCustomScene.Result result = service.saveAs(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSearchTemplate());
        assertEquals("new_scene_id", result.getSearchTemplate().get("id"));
        assertEquals("Saved Scene", result.getSearchTemplate().get("name"));
        verify(customSceneService).saveAs(eq("save_as_scene_data"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(serviceFacade);
        assertNotNull(infraServiceFacade);
        assertNotNull(customSceneService);
        assertNotNull(searchTemplateLogicService);
        assertNotNull(sceneLogicService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getTemplate方法
     */
    @Test
    @DisplayName("测试getTemplate方法")
    void testGetTemplate() {
        // Arrange
        GetTemplate.Arg arg = new GetTemplate.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setExtendAttribute("test_extend");

        List<ISearchTemplate> mockTemplates = Lists.newArrayList();

        Map<String, Object> describeData = Maps.newHashMap();
        describeData.put("api_name", "AccountObj");
        describeData.put("display_name", "Account Object");
        ObjectDescribe mockDescribe = new ObjectDescribe(describeData);

        when(searchTemplateLogicService.findByDescribeApiNameAndExtendAttribute(eq("AccountObj"), eq("test_extend"), any(User.class)))
                .thenReturn(mockTemplates);
        when(serviceFacade.findObject(eq(TENANT_ID), eq("AccountObj")))
                .thenReturn(mockDescribe);

        // Act
        GetTemplate.Result result = service.getTemplate(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getTemplates());
        assertTrue(result.getTemplates().isEmpty());
        verify(searchTemplateLogicService).findByDescribeApiNameAndExtendAttribute(eq("AccountObj"), eq("test_extend"), any(User.class));
        verify(serviceFacade).findObject(eq(TENANT_ID), eq("AccountObj"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOutTemplate方法
     */
    @Test
    @DisplayName("测试getOutTemplate方法")
    void testGetOutTemplate() {
        // Arrange
        GetOutTemplate.Arg arg = new GetOutTemplate.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setExtendAttribute("out_extend");

        List<ISearchTemplate> mockTemplates = Lists.newArrayList();

        when(searchTemplateLogicService.findOutSearchTemplate(eq("AccountObj"), any(User.class), eq("out_extend")))
                .thenReturn(mockTemplates);

        // Act
        GetOutTemplate.Result result = service.getOutTemplate(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getTemplates());
        assertTrue(result.getTemplates().isEmpty());
        verify(searchTemplateLogicService).findOutSearchTemplate(eq("AccountObj"), any(User.class), eq("out_extend"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteCustomScene方法
     */
    @Test
    @DisplayName("测试deleteCustomScene方法")
    void testDeleteCustomScene() {
        // Arrange
        DeleteCustomScene.Arg arg = new DeleteCustomScene.Arg();
        arg.setScene_id("scene_to_delete");
        arg.setDescribe_api_name("AccountObj");

        CustomSceneResult mockResult = new CustomSceneResult();
        mockResult.setSuccess(true);

        when(customSceneService.delete(eq("scene_to_delete"), eq("AccountObj"), any(User.class)))
                .thenReturn(mockResult);

        // Act
        DeleteCustomScene.Result result = service.deleteCustomScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(customSceneService).delete(eq("scene_to_delete"), eq("AccountObj"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getTemplate方法空结果场景
     */
    @Test
    @DisplayName("测试getTemplate方法空结果场景")
    void testGetTemplateEmptyResult() {
        // Arrange
        GetTemplate.Arg arg = new GetTemplate.Arg();
        arg.setDescribeApiName("EmptyObj");
        arg.setExtendAttribute("empty_extend");

        List<ISearchTemplate> emptyTemplates = Lists.newArrayList();

        Map<String, Object> describeData = Maps.newHashMap();
        describeData.put("api_name", "EmptyObj");
        describeData.put("display_name", "Empty Object");
        ObjectDescribe mockDescribe = new ObjectDescribe(describeData);

        when(searchTemplateLogicService.findByDescribeApiNameAndExtendAttribute(eq("EmptyObj"), eq("empty_extend"), any(User.class)))
                .thenReturn(emptyTemplates);
        when(serviceFacade.findObject(eq(TENANT_ID), eq("EmptyObj")))
                .thenReturn(mockDescribe);

        // Act
        GetTemplate.Result result = service.getTemplate(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getTemplates());
        assertTrue(result.getTemplates().isEmpty());
        verify(searchTemplateLogicService).findByDescribeApiNameAndExtendAttribute(eq("EmptyObj"), eq("empty_extend"), any(User.class));
        verify(serviceFacade).findObject(eq(TENANT_ID), eq("EmptyObj"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findCustomSceneById方法空场景
     */
    @Test
    @DisplayName("测试findCustomSceneById方法空场景")
    void testFindCustomSceneByIdWithNullResult() {
        // Arrange
        FindCustomeSceneById.Arg arg = new FindCustomeSceneById.Arg();
        arg.setScene_id("non_existent_scene");
        arg.setDescribe_api_name("AccountObj");
        arg.setScene_type("custom");

        CustomSceneResult mockResult = new CustomSceneResult();
        Map<String, Object> emptySearchTemplate = Maps.newHashMap();
        mockResult.setSearchTemplate(emptySearchTemplate);
        mockResult.setSuccess(false);

        when(customSceneService.findByIdWithMerge(eq("non_existent_scene"), eq("AccountObj"), eq("custom"), any(User.class)))
                .thenReturn(mockResult);
        when(sceneLogicService.fillBaseSceneLabel(any(), eq("AccountObj"), any(User.class), any()))
                .thenReturn("Base Scene Label");

        // Act
        FindCustomeSceneById.Result result = service.findCustomSceneById(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess()); // findCustomSceneById总是返回success=true
        verify(customSceneService).findByIdWithMerge(eq("non_existent_scene"), eq("AccountObj"), eq("custom"), any(User.class));
    }
}
