package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.DataSearchService;
import com.facishare.paas.appframework.common.service.dto.SearchData;
import com.facishare.paas.appframework.common.service.SearchDataByName;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.search.FindSearchObjectList;
import com.facishare.paas.appframework.core.predef.service.dto.search.ObjectSearchData;
import com.facishare.paas.appframework.core.predef.service.dto.search.SearchByName;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.wishFul.RecordingService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectSearchService单元测试类
 * 从Groovy测试迁移而来
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectSearchService单元测试")
class ObjectSearchServiceTest {

    @Mock
    private MetaDataService metaDataService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private FunctionPrivilegeService functionPrivilegeService;

    @Mock
    private DataSearchService dataSearchService;



    @Mock
    private RecordingService recordingService;

    @InjectMocks
    private ObjectSearchService objectSearchService;

    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OBJECT_API_NAME = "test_object__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);

        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();

        serviceContext = new ServiceContext(requestContext, "search", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sortList方法按idList大小倒序排序功能
     * 迁移自Groovy测试：test sort list after search()
     */
    @Test
    @DisplayName("测试sortList方法按idList大小倒序排序")
    void testSortListAfterSearch() {
        // Arrange - 准备测试数据
        List<ObjectSearchData.SearchResult> list = Lists.newArrayList();
        
        // 创建不同idList大小的SearchResult对象
        ObjectSearchData.SearchResult result1 = ObjectSearchData.SearchResult.builder()
                .idList(Lists.newArrayList("1", "2"))
                .build();
        
        ObjectSearchData.SearchResult result2 = ObjectSearchData.SearchResult.builder()
                .idList(Lists.newArrayList("1", "2", "3", "4"))
                .build();
        
        ObjectSearchData.SearchResult result3 = ObjectSearchData.SearchResult.builder()
                .idList(Lists.newArrayList())
                .build();
        
        ObjectSearchData.SearchResult result4 = ObjectSearchData.SearchResult.builder()
                .idList(Lists.newArrayList("2", "4", "5"))
                .build();
        
        list.add(result1);
        list.add(result2);
        list.add(result3);
        list.add(result4);

        // Act - 执行被测方法
        // 使用反射调用private方法sortList
        try {
            java.lang.reflect.Method sortListMethod = ObjectSearchService.class.getDeclaredMethod("sortList", List.class);
            sortListMethod.setAccessible(true);
            List<ObjectSearchData.SearchResult> sortedList = (List<ObjectSearchData.SearchResult>) sortListMethod.invoke(objectSearchService, list);

            // Assert - 验证结果
            assertNotNull(sortedList);
            assertEquals(4, sortedList.size());
            
            // 验证排序结果：第一个元素应该是idList最大的（4个元素）
            assertEquals(4, sortedList.get(0).getIdList().size());
            
            // 验证完整排序顺序：4个元素 > 3个元素 > 2个元素 > 0个元素
            assertEquals(4, sortedList.get(0).getIdList().size()); // result2
            assertEquals(3, sortedList.get(1).getIdList().size()); // result4
            assertEquals(2, sortedList.get(2).getIdList().size()); // result1
            assertEquals(0, sortedList.get(3).getIdList().size()); // result3
            
        } catch (Exception e) {
            fail("反射调用sortList方法失败: " + e.getMessage());
        }
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectSearchService);
        assertNotNull(metaDataService);
        assertNotNull(describeLogicService);
        assertNotNull(functionPrivilegeService);
        assertNotNull(dataSearchService);
    }
}
