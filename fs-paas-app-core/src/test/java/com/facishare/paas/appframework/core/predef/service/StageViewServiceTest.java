package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.stageview.CreateStageView;
import com.facishare.paas.appframework.core.predef.service.dto.stageview.DeleteStageView;
import com.facishare.paas.appframework.core.predef.service.dto.stageview.QueryStageView;
import com.facishare.paas.appframework.metadata.StageViewLogicService;
import com.facishare.paas.appframework.metadata.dto.StageViewInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * StageViewService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("StageViewService单元测试")
class StageViewServiceTest {

    @Mock
    private StageViewLogicService stageViewLogicService;
    
    @InjectMocks
    private StageViewService stageViewService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "test_object__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "stage_view", "test_method");
    }    /**
     * GenerateByAI
     * 测试内容描述：测试创建阶段视图成功的正常场景
     */
    @Test
    @DisplayName("测试创建阶段视图成功")
    void testCreateStageViewSuccess() {
        // Arrange
        CreateStageView.Arg arg = new CreateStageView.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        
        StageViewInfo stageViewInfo1 = new StageViewInfo();
        stageViewInfo1.setApiName("stage1");
        stageViewInfo1.setLabel("阶段1");

        StageViewInfo stageViewInfo2 = new StageViewInfo();
        stageViewInfo2.setApiName("stage2");
        stageViewInfo2.setLabel("阶段2");
        
        arg.setStageViewInfos(Arrays.asList(stageViewInfo1, stageViewInfo2));

        when(stageViewLogicService.saveStageView(eq(DESCRIBE_API_NAME), eq(arg.getStageViewInfos()), eq(user))).thenReturn(true);

        // Act
        CreateStageView.Result result = stageViewService.createStageView(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(stageViewLogicService).saveStageView(eq(DESCRIBE_API_NAME), eq(arg.getStageViewInfos()), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询阶段视图成功的正常场景
     */
    @Test
    @DisplayName("测试查询阶段视图成功")
    void testQueryStageViewSuccess() {
        // Arrange
        QueryStageView.Arg arg = new QueryStageView.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        StageViewInfo stageViewInfo1 = new StageViewInfo();
        stageViewInfo1.setApiName("stage1");
        stageViewInfo1.setLabel("阶段1");

        StageViewInfo stageViewInfo2 = new StageViewInfo();
        stageViewInfo2.setApiName("stage2");
        stageViewInfo2.setLabel("阶段2");
        
        List<StageViewInfo> mockStageViewList = Arrays.asList(stageViewInfo1, stageViewInfo2);

        when(stageViewLogicService.findStageView(eq(DESCRIBE_API_NAME), eq(user))).thenReturn(mockStageViewList);

        // Act
        QueryStageView.Result result = stageViewService.queryStageView(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getStageViewInfos());
        assertEquals(2, result.getStageViewInfos().size());
        assertEquals("stage1", result.getStageViewInfos().get(0).getApiName());
        assertEquals("stage2", result.getStageViewInfos().get(1).getApiName());
        verify(stageViewLogicService).findStageView(eq(DESCRIBE_API_NAME), eq(user));
    }
}