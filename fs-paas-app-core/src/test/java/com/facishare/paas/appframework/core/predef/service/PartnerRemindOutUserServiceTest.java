package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.model.OutInfoChangeModel;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * PartnerRemindOutUserService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("PartnerRemindOutUserService单元测试")
class PartnerRemindOutUserServiceTest {

    @Mock
    private OrgService orgService;
    
    @Mock
    private OutChannelCrmNotificationService outChannelCrmNotificationService;
    
    @InjectMocks
    private PartnerRemindOutUserService partnerRemindOutUserService;
    
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String API_NAME = "TestObj__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        user.setUserName("TestUser");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提醒外部用户成功的场景
     */
    @Test
    @DisplayName("测试remindOutUser成功")
    void testRemindOutUserSuccess() {
        // Arrange
        OutInfoChangeModel outInfoChangeModel1 = new OutInfoChangeModel();
        outInfoChangeModel1.setOldOutEI(1);
        outInfoChangeModel1.setNewOutEI(2);
        outInfoChangeModel1.setOldOutUserId(100);
        outInfoChangeModel1.setNewOutUserId(200);
        outInfoChangeModel1.setIsPreDefineObj(true);

        OutInfoChangeModel outInfoChangeModel2 = new OutInfoChangeModel();
        outInfoChangeModel2.setOldOutEI(3);
        outInfoChangeModel2.setNewOutEI(4);
        outInfoChangeModel2.setOldOutUserId(300);
        outInfoChangeModel2.setNewOutUserId(400);
        outInfoChangeModel2.setIsPreDefineObj(true);
        
        List<OutInfoChangeModel> outInfoChangeModels = Arrays.asList(outInfoChangeModel1, outInfoChangeModel2);
        
        RequestContext mockRequestContext = mock(RequestContext.class);
        User mockContextUser = new User(TENANT_ID, "2000");
        when(mockRequestContext.getUser()).thenReturn(mockContextUser);

        try (MockedStatic<RequestContextManager> mockedManager = mockStatic(RequestContextManager.class)) {
            mockedManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            // Act
            partnerRemindOutUserService.remindOutUser(user, API_NAME, 91, outInfoChangeModels);

            // Assert
            verify(orgService, never()).getUser(anyString(), anyString());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提醒外部用户时用户名为空的场景
     */
    @Test
    @DisplayName("测试remindOutUser用户名为空")
    void testRemindOutUserWithEmptyUserName() {
        // Arrange
        User userWithoutName = new User(TENANT_ID, USER_ID);
        userWithoutName.setUserName(null);
        
        User mockUserFromOrg = new User(TENANT_ID, USER_ID);
        mockUserFromOrg.setUserName("UserFromOrg");
        
        OutInfoChangeModel outInfoChangeModel = new OutInfoChangeModel();
        outInfoChangeModel.setOldOutEI(1);
        outInfoChangeModel.setNewOutEI(2);
        outInfoChangeModel.setOldOutUserId(100);
        outInfoChangeModel.setNewOutUserId(200);
        outInfoChangeModel.setIsPreDefineObj(true);
        
        List<OutInfoChangeModel> outInfoChangeModels = Arrays.asList(outInfoChangeModel);
        
        RequestContext mockRequestContext = mock(RequestContext.class);
        User mockContextUser = new User(TENANT_ID, "2000");
        when(mockRequestContext.getUser()).thenReturn(mockContextUser);
        
        when(orgService.getUser(TENANT_ID, USER_ID)).thenReturn(mockUserFromOrg);

        try (MockedStatic<RequestContextManager> mockedManager = mockStatic(RequestContextManager.class)) {
            mockedManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            // Act
            partnerRemindOutUserService.remindOutUser(userWithoutName, API_NAME, 91, outInfoChangeModels);

            // Assert
            verify(orgService).getUser(TENANT_ID, USER_ID);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提醒外部用户时记录类型为92的场景
     */
    @Test
    @DisplayName("测试remindOutUser记录类型为92")
    void testRemindOutUserWithRecordType92() {
        // Arrange
        OutInfoChangeModel outInfoChangeModel = new OutInfoChangeModel();
        outInfoChangeModel.setOldOutEI(1);
        outInfoChangeModel.setNewOutEI(2);
        outInfoChangeModel.setOldOutUserId(100);
        outInfoChangeModel.setNewOutUserId(200);
        outInfoChangeModel.setIsPreDefineObj(true);
        
        List<OutInfoChangeModel> outInfoChangeModels = Arrays.asList(outInfoChangeModel);
        
        RequestContext mockRequestContext = mock(RequestContext.class);
        User mockContextUser = new User(TENANT_ID, "2000");
        when(mockRequestContext.getUser()).thenReturn(mockContextUser);

        try (MockedStatic<RequestContextManager> mockedManager = mockStatic(RequestContextManager.class)) {
            mockedManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            // Act
            partnerRemindOutUserService.remindOutUser(user, API_NAME, 92, outInfoChangeModels);

            // Assert
            verify(orgService, never()).getUser(anyString(), anyString());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提醒外部用户时变更信息相同的场景
     */
    @Test
    @DisplayName("测试remindOutUser变更信息相同")
    void testRemindOutUserWithSameChangeInfo() {
        // Arrange
        OutInfoChangeModel outInfoChangeModel = new OutInfoChangeModel();
        outInfoChangeModel.setOldOutEI(1);
        outInfoChangeModel.setNewOutEI(1); // 相同的值
        outInfoChangeModel.setOldOutUserId(100);
        outInfoChangeModel.setNewOutUserId(100); // 相同的值
        outInfoChangeModel.setIsPreDefineObj(true);
        
        List<OutInfoChangeModel> outInfoChangeModels = Arrays.asList(outInfoChangeModel);

        // Act
        partnerRemindOutUserService.remindOutUser(user, API_NAME, 91, outInfoChangeModels);

        // Assert
        verify(orgService, never()).getUser(anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提醒外部用户时变更模型列表为空的场景
     */
    @Test
    @DisplayName("测试remindOutUser变更模型列表为空")
    void testRemindOutUserWithEmptyChangeModels() {
        // Arrange
        List<OutInfoChangeModel> emptyOutInfoChangeModels = Arrays.asList();

        // Act
        partnerRemindOutUserService.remindOutUser(user, API_NAME, 91, emptyOutInfoChangeModels);

        // Assert
        verify(orgService, never()).getUser(anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提醒外部用户时变更模型列表为null的场景
     */
    @Test
    @DisplayName("测试remindOutUser变更模型列表为null")
    void testRemindOutUserWithNullChangeModels() {
        // Arrange
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            partnerRemindOutUserService.remindOutUser(user, API_NAME, 91, null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提醒外部用户时用户为null的场景
     */
    @Test
    @DisplayName("测试remindOutUser用户为null")
    void testRemindOutUserWithNullUser() {
        // Arrange
        OutInfoChangeModel outInfoChangeModel = new OutInfoChangeModel();
        outInfoChangeModel.setOldOutEI(1);
        outInfoChangeModel.setNewOutEI(2);
        outInfoChangeModel.setOldOutUserId(100);
        outInfoChangeModel.setNewOutUserId(200);
        outInfoChangeModel.setIsPreDefineObj(true);
        
        List<OutInfoChangeModel> outInfoChangeModels = Arrays.asList(outInfoChangeModel);
        
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            partnerRemindOutUserService.remindOutUser(null, API_NAME, 91, outInfoChangeModels);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(partnerRemindOutUserService);
        assertNotNull(orgService);
        assertNotNull(outChannelCrmNotificationService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Assert
        assertNotNull(user);
        assertEquals(TENANT_ID, user.getTenantId());
        assertEquals(USER_ID, user.getUserId());
        assertEquals("TestUser", user.getUserName());
    }
}
