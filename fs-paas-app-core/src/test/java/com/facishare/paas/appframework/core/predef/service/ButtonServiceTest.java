package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.button.service.BrushButtonDescribeService;
import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.button.*;
import com.facishare.paas.appframework.flow.FlowCommonService;
import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataManager;
import com.facishare.paas.appframework.metadata.config.IUdefButtonConfig;
import com.facishare.paas.appframework.metadata.dto.ButtonJobExecuteResult;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.privilege.UserDefinedButtonService;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.IObjectData;

import com.facishare.paas.metadata.service.impl.UdefFunctionService;
import com.facishare.paas.I18N;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * GenerateByAI
 * ButtonService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ButtonService单元测试")
class ButtonServiceTest {

    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private CustomButtonServiceImpl buttonService;
    
    @Mock
    private PostActionService actionService;
    
    @Mock
    private UserDefinedButtonService userDefinedButtonService;
    
    @Mock
    private ObjectMappingService objectMappingService;
    
    @Mock
    private UdefFunctionService udefFunctionService;
    
    @Mock
    private LogService logService;
    
    @Mock
    private FunctionLogicService functionLogicService;
    
    @Mock
    private JobScheduleService jobScheduleService;
    
    @Mock
    private MessagePollingService messagePollingService;
    
    @Mock
    private BrushButtonDescribeService brushButtonDescribeService;
    
    @Mock
    private UIEventLogicService eventLogicService;
    
    @Mock
    private ButtonLogicService buttonLogicService;
    
    @Mock
    private MetaDataFindService metaDataFindService;
    
    @Mock
    private FindAndFilterButtonsByDataManager findAndFilterButtonsByDataManager;
    
    @Mock
    private HandlerLogicService handlerLogicService;
    
    @Mock
    private InfraServiceFacade infraServiceFacade;
    
    @Mock
    private ReferenceLogicService referenceLogicService;
    
    @Mock
    private FlowCommonService flowCommonService;

    @InjectMocks
    private ButtonService buttonServiceUnderTest;

    private ServiceContext serviceContext;
    private User user;

    private static final String TENANT_ID = "78057";
    private static final String USER_ID = "1000";
    private static final String OBJECT_API_NAME = "TestObj";
    private static final String BUTTON_API_NAME = "test_button__c";
    private static final String BUTTON_ID = "button123";
    private static final String DATA_ID = "data123";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .user(user)
                .tenantId(TENANT_ID)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        serviceContext = new ServiceContext(requestContext, "button", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建按钮成功的场景
     */
    @Test
    @DisplayName("测试创建按钮成功")
    void testCreateButtonSuccess() {
        // Arrange
        CreateButton.Arg arg = new CreateButton.Arg();
        arg.setButton(createButtonJson());
        arg.setPost_actions(Lists.newArrayList());
        arg.setRoles(Lists.newArrayList("role1", "role2"));

        IUdefButton mockButton = createMockButton();
        List<IUdefButton> existingButtons = Lists.newArrayList();

        when(buttonService.findButtonList(eq(user), isNull()))
                .thenReturn(existingButtons);
        when(buttonService.createCustomButton(eq(user), any(IUdefButton.class)))
                .thenReturn(mockButton);

        // Act
        CreateButton.Result result = buttonServiceUnderTest.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(buttonService).checkCustomButtonCountLimit(eq(user), isNull(), eq(0));
        verify(buttonService).createCustomButton(eq(user), any(IUdefButton.class));
        verify(userDefinedButtonService).createUserDefinedButton(eq(user), isNull(),
                isNull(), anyString(), eq(Lists.newArrayList("role1", "role2")));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建按钮失败的场景
     */
    @Test
    @DisplayName("测试创建按钮失败")
    void testCreateButtonFailure() {
        // Arrange
        CreateButton.Arg arg = new CreateButton.Arg();
        arg.setButton(createButtonJson());
        arg.setPost_actions(Lists.newArrayList());

        List<IUdefButton> existingButtons = Lists.newArrayList();

        when(buttonService.findButtonList(eq(user), isNull()))
                .thenReturn(existingButtons);
        when(buttonService.createCustomButton(eq(user), any(IUdefButton.class)))
                .thenReturn(null); // 创建失败

        // Act
        CreateButton.Result result = buttonServiceUnderTest.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(buttonService).createCustomButton(eq(user), any(IUdefButton.class));
        verify(userDefinedButtonService, never()).createUserDefinedButton(any(), any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮列表成功的场景
     */
    @Test
    @DisplayName("测试查找按钮列表成功")
    void testFindButtonListSuccess() {
        // Arrange
        FindButtonList.Arg arg = new FindButtonList.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);

        List<IUdefButton> mockButtons = Lists.newArrayList(createRealButton());

        when(buttonService.findButtonList(eq(user), eq(OBJECT_API_NAME), eq(true)))
                .thenReturn(mockButtons);
        when(buttonService.findButtonConfigListByApiName(eq(user), eq(OBJECT_API_NAME), anyList()))
                .thenReturn(Lists.newArrayList());

        // Act
        FindButtonList.Result result = buttonServiceUnderTest.findButtonList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtonList());
        assertEquals(1, result.getButtonList().size());
        verify(buttonService).findButtonList(eq(user), eq(OBJECT_API_NAME), eq(true));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮信息成功的场景
     */
    @Test
    @DisplayName("测试查找按钮信息成功")
    void testFindButtonInfoSuccess() {
        // 使用MockedStatic来模拟I18N
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // Arrange
            FindButtonInfo.Arg arg = new FindButtonInfo.Arg();
            arg.setDescribeApiName(OBJECT_API_NAME);
            arg.setButtonApiName(BUTTON_API_NAME);

            IUdefButton mockButton = createRealButton();
            List<IUdefAction> mockActions = Lists.newArrayList();

            when(buttonService.findButtonByApiNameForDesigner(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME)))
                    .thenReturn(mockButton);
            when(actionService.findActionListForDesigner(eq(user), eq(mockButton), eq(OBJECT_API_NAME)))
                    .thenReturn(mockActions);

            // Mock I18N
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("测试");

            // Act
            FindButtonInfo.Result result = buttonServiceUnderTest.findButtonInfo(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getButton());
            assertEquals(BUTTON_API_NAME, result.getButton().get("apiName"));
            verify(buttonService).findButtonByApiNameForDesigner(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME));
            verify(actionService).findActionListForDesigner(eq(user), eq(mockButton), eq(OBJECT_API_NAME));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮信息时按钮不存在的场景
     */
    @Test
    @DisplayName("测试查找按钮信息时按钮不存在")
    void testFindButtonInfoNotFound() {
        // 使用MockedStatic来模拟I18N
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // Arrange
            FindButtonInfo.Arg arg = new FindButtonInfo.Arg();
            arg.setDescribeApiName(OBJECT_API_NAME);
            arg.setButtonApiName("nonexistent_button");

            when(buttonService.findButtonByApiNameForDesigner(eq(user), eq("nonexistent_button"), eq(OBJECT_API_NAME)))
                    .thenReturn(null);

            // Mock I18N - 返回null而不是字符串，避免抛出ValidateException
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn(null);

            // Act & Assert - 期望抛出ValidateException
            assertThrows(ValidateException.class, () -> {
                buttonServiceUnderTest.findButtonInfo(arg, serviceContext);
            });

            verify(buttonService).findButtonByApiNameForDesigner(eq(user), eq("nonexistent_button"), eq(OBJECT_API_NAME));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试改变按钮状态成功的场景
     */
    @Test
    @DisplayName("测试改变按钮状态成功")
    void testChangeButtonStatusSuccess() {
        // Arrange
        ChangeButtonStatus.Arg arg = new ChangeButtonStatus.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setButtonApiName(BUTTON_API_NAME);
        arg.setActive(true);

        when(buttonService.updateStatus(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME), eq(true)))
                .thenReturn(true);

        // Act
        ChangeButtonStatus.Result result = buttonServiceUnderTest.changeButtonStatus(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(buttonService).updateStatus(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME), eq(true));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启动按钮成功的场景
     */
    @Test
    @DisplayName("测试启动按钮成功")
    void testStartButtonSuccess() {
        // Arrange
        StartButton.Arg arg = new StartButton.Arg();
        arg.setObjectDataId(DATA_ID);
        arg.setArgs(Lists.newArrayList());

        // Act
        StartButton.Result result = buttonServiceUnderTest.startButton(arg, serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试校验按钮数量成功的场景
     */
    @Test
    @DisplayName("测试校验按钮数量成功")
    void testValidateButtonCountSuccess() {
        // Arrange
        ValidateButtonCount.Arg arg = new ValidateButtonCount.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);

        List<IUdefButton> mockButtons = Lists.newArrayList(createMockButton());
        
        when(buttonService.findButtonList(eq(user), eq(OBJECT_API_NAME)))
                .thenReturn(mockButtons);

        // Act
        ValidateButtonCount.Result result = buttonServiceUnderTest.validateButtonCount(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(buttonService).findButtonList(eq(user), eq(OBJECT_API_NAME));
        verify(buttonService).checkCustomButtonCountLimit(eq(user), eq(OBJECT_API_NAME), anyInt());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取按钮信息成功的场景
     */
    @Test
    @DisplayName("测试获取按钮信息成功")
    void testFetchButtonInfoSuccess() {
        // Arrange
        FetchButtonInfo.Arg arg = new FetchButtonInfo.Arg();
        arg.setLastFetchTime(0L);

        List<IUdefButton> mockButtons = Lists.newArrayList(createMockButton());

        when(buttonService.findButtonsByLastModifiedTime(eq(user), eq(0L)))
                .thenReturn(mockButtons);

        // Act
        FetchButtonInfo.Result result = buttonServiceUnderTest.fetchButtonInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtonList());
        verify(buttonService).findButtonsByLastModifiedTime(eq(user), eq(0L));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮配置成功的场景
     */
    @Test
    @DisplayName("测试查找按钮配置成功")
    void testFindButtonConfigSuccess() {
        // Arrange
        FindUdefButtonConfig.Arg arg = new FindUdefButtonConfig.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);

        IUdefButtonConfig mockConfig = mock(IUdefButtonConfig.class);
        
        when(buttonService.findButtonConfigByApiName(eq(OBJECT_API_NAME), any(), eq(user)))
                .thenReturn(mockConfig);

        // Act
        FindUdefButtonConfig.Result result = buttonServiceUnderTest.findButtonConfig(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtonConfig());
        verify(buttonService).findButtonConfigByApiName(eq(OBJECT_API_NAME), any(), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮作业结果成功的场景
     */
    @Test
    @DisplayName("测试查找按钮作业结果成功")
    void testFindButtonJobResultSuccess() {
        // Arrange
        FindButtonJobResult.Arg arg = new FindButtonJobResult.Arg();
        arg.setJobId("job123");

        ButtonJobExecuteResult mockJobResult = mock(ButtonJobExecuteResult.class);
        
        when(jobScheduleService.queryButtonJobExecuteResult(eq(user), any(), eq("job123")))
                .thenReturn(mockJobResult);

        // Act
        FindButtonJobResult.Result result = buttonServiceUnderTest.findButtonJobResult(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockJobResult, result.getJobInfo());
        verify(jobScheduleService).queryButtonJobExecuteResult(eq(user), any(), eq("job123"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新按钮成功的场景
     */
    @Test
    @DisplayName("测试更新按钮成功")
    void testUpdateButtonSuccess() {
        // Arrange
        UpdateButton.Arg arg = new UpdateButton.Arg();
        arg.setButton(createButtonJson());
        arg.setPost_actions(Lists.newArrayList());
        arg.setRoles(Lists.newArrayList("role1"));

        IUdefButton mockButton = createMockButton();

        // Mock findButtonByApiName返回存在的按钮 - 参数可能为null，明确指定String类型
        when(buttonService.findButtonByApiName(any(User.class), (String) isNull(), (String) isNull()))
                .thenReturn(mockButton);

        // Mock infraServiceFacade.callWithTransaction - 使用反射创建UpdateResult
        when(infraServiceFacade.callWithTransaction(any()))
                .thenAnswer(invocation -> {
                    // 使用反射创建私有内部类UpdateResult
                    try {
                        Class<?> updateResultClass = Class.forName("com.facishare.paas.appframework.core.predef.service.ButtonService$UpdateResult");
                        Object updateResult = updateResultClass.getDeclaredConstructor().newInstance();

                        // 设置customButton字段
                        java.lang.reflect.Field customButtonField = updateResultClass.getDeclaredField("customButton");
                        customButtonField.setAccessible(true);
                        customButtonField.set(updateResult, mockButton);

                        return updateResult;
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to create UpdateResult", e);
                    }
                });

        // Act
        UpdateButton.Result result = buttonServiceUnderTest.update(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(buttonService).findButtonByApiName(any(User.class), (String) isNull(), (String) isNull());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除按钮成功的场景
     */
    @Test
    @DisplayName("测试删除按钮成功")
    void testDeleteButtonSuccess() {
        // Arrange
        DeleteButton.Arg arg = new DeleteButton.Arg();
        arg.setButtonApiName(BUTTON_API_NAME);
        arg.setDescribeApiName(OBJECT_API_NAME);

        IUdefButton mockButton = createMockButton();

        when(buttonService.findButtonByApiName(any(User.class), anyString(), anyString()))
                .thenReturn(mockButton);
        when(buttonService.deleteCustomButton(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME)))
                .thenReturn(true);

        // Act
        DeleteButton.Result result = buttonServiceUnderTest.delete(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(buttonService).findButtonByApiName(any(User.class), anyString(), anyString());
        verify(buttonService).deleteCustomButton(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象API名称查找按钮成功的场景
     */
    @Test
    @DisplayName("测试根据对象API名称查找按钮成功")
    void testFindButtonsByDescribeApiNamesSuccess() {
        // Arrange
        FindButtonsByDescribeApiNames.Arg arg = new FindButtonsByDescribeApiNames.Arg();
        arg.setDescribeApiNames(Lists.newArrayList(OBJECT_API_NAME));

        Map<String, List<IUdefButton>> mockButtonMap = new HashMap<>();
        mockButtonMap.put(OBJECT_API_NAME, Lists.newArrayList(createRealButton()));

        when(buttonService.findButtonsByDescribeApiNames(eq(user), eq(arg.getDescribeApiNames()), eq(true)))
                .thenReturn(mockButtonMap);

        // Act
        FindButtonsByDescribeApiNames.Result result = buttonServiceUnderTest.findButtonsByDescribeApiNames(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtonList());
        assertTrue(result.getButtonList().containsKey(OBJECT_API_NAME));
        verify(buttonService).findButtonsByDescribeApiNames(eq(user), eq(arg.getDescribeApiNames()), eq(true));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新按钮URL成功的场景
     */
    @Test
    @DisplayName("测试更新按钮URL成功")
    void testUpdateButtonUrlSuccess() {
        // Arrange
        UpdateButtonUrl.Arg arg = new UpdateButtonUrl.Arg();
        arg.setObjectDescribeApiName(OBJECT_API_NAME);

        // 创建ButtonURL对象
        com.facishare.paas.appframework.metadata.button.ButtonURL buttonURL =
            new com.facishare.paas.appframework.metadata.button.ButtonURL();
        arg.setButtonURL(buttonURL);

        // 使用Set而不是List
        java.util.Set<String> buttonApiNames = new java.util.HashSet<>();
        buttonApiNames.add(BUTTON_API_NAME);
        arg.setButtonApiNames(buttonApiNames);

        doNothing().when(buttonService).updateButtonUrl(eq(user), eq(OBJECT_API_NAME), eq(buttonURL), eq(buttonApiNames));
        doNothing().when(messagePollingService).sendPollingMessage(eq(user), eq("paas_ui_button"), any(), eq(false));

        // Act
        UpdateButtonUrl.Result result = buttonServiceUnderTest.updateButtonUrl(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(buttonService).updateButtonUrl(eq(user), eq(OBJECT_API_NAME), eq(buttonURL), eq(buttonApiNames));
        verify(messagePollingService).sendPollingMessage(eq(user), eq("paas_ui_button"), any(), eq(false));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试刷新按钮描述成功的场景
     */
    @Test
    @DisplayName("测试刷新按钮描述成功")
    void testBrushButtonDescribeSuccess() {
        // Arrange
        BrushButton.Arg arg = new BrushButton.Arg();
        // 使用Set而不是List
        java.util.Set<String> objectApiNames = new java.util.HashSet<>();
        objectApiNames.add(OBJECT_API_NAME);
        arg.setObjectApiNames(objectApiNames);

        doNothing().when(brushButtonDescribeService).brushButtonDescribe(eq(objectApiNames), eq(user));

        // Act
        BrushButton.Result result = buttonServiceUnderTest.brushButtonDescribe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(brushButtonDescribeService).brushButtonDescribe(eq(objectApiNames), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新按钮后置动作成功的场景
     */
    @Test
    @DisplayName("测试更新按钮后置动作成功")
    void testUpdateButtonPostActionSuccess() {
        // Arrange
        UpdateButtonPostAction.Arg arg = new UpdateButtonPostAction.Arg();
        arg.setButtonApiName(BUTTON_API_NAME);
        arg.setDescribeApiName(OBJECT_API_NAME);
        // 创建一个非空的ActionPojo列表
        List<ActionPojo> actionList = Lists.newArrayList();
        ActionPojo actionPojo = new ActionPojo();
        actionPojo.setAction_type("test");
        actionPojo.setLabel("testAction");
        actionPojo.setDescribe_api_name(OBJECT_API_NAME);
        actionList.add(actionPojo);
        arg.setUdefAction(actionList);

        IUdefButton mockButton = createMockButton();

        when(buttonService.findButtonByApiName(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME)))
                .thenReturn(mockButton);
        when(buttonService.updateCustomButton(eq(user), eq(mockButton)))
                .thenReturn(mockButton);

        // Act
        UpdateButtonPostAction.Result result = buttonServiceUnderTest.updateButtonPostAction(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(buttonService).findButtonByApiName(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME));
        verify(buttonService).updateCustomButton(eq(user), eq(mockButton));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除按钮后置动作成功的场景
     */
    @Test
    @DisplayName("测试删除按钮后置动作成功")
    void testDeleteButtonPostActionSuccess() {
        // Arrange
        DeleteButtonPostAction.Arg arg = new DeleteButtonPostAction.Arg();
        arg.setButtonApiName(BUTTON_API_NAME);
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setActionIds(Lists.newArrayList("action1"));

        IUdefButton mockButton = createMockButton();
        // 创建包含action1的Action列表，这样过滤后才会有结果
        IUdefAction mockAction = mock(IUdefAction.class);
        when(mockAction.getId()).thenReturn("action1");
        List<IUdefAction> mockActions = Lists.newArrayList(mockAction);

        when(buttonService.findButtonByApiName(any(User.class), anyString(), anyString()))
                .thenReturn(mockButton);
        when(actionService.findActionListForDesigner(any(User.class), any(IUdefButton.class), anyString()))
                .thenReturn(mockActions);
        // bulkDeleteAction是void方法，不需要doNothing
        when(buttonService.updateCustomButton(eq(user), eq(mockButton)))
                .thenReturn(mockButton);

        // Act
        DeleteButtonPostAction.Result result = buttonServiceUnderTest.deleteButtonPostAction(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(buttonService).findButtonByApiName(any(User.class), anyString(), anyString());
        verify(actionService).bulkDeleteAction(eq(user), eq(arg.getActionIds()), eq(OBJECT_API_NAME));
        verify(buttonService).updateCustomButton(eq(user), eq(mockButton));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据数据查找和过滤按钮成功的场景
     */
    @Test
    @DisplayName("测试根据数据查找和过滤按钮成功")
    void testFindAndFilterButtonsByDataSuccess() {
        // Arrange
        FindAndFilterButtonsByData.Arg arg = new FindAndFilterButtonsByData.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);

        // 创建Mock的ObjectDescribe
        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);

        // 创建Mock的ObjectData
        IObjectData mockObjectData = mock(IObjectData.class);

        List<IUdefButton> mockButtons = Lists.newArrayList(createRealButton());

        when(describeLogicService.findObject(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                .thenReturn(mockDescribe);
        when(metaDataFindService.findObjectData(eq(user), eq(DATA_ID), eq(mockDescribe)))
                .thenReturn(mockObjectData);
        // 实际调用的是findListButtonByButtonFilter方法，返回Map
        Map<String, List<IUdefButton>> mockButtonMap = new HashMap<>();
        mockButtonMap.put(DATA_ID, mockButtons);
        when(buttonService.findListButtonByButtonFilter(eq(mockDescribe), anyList(), eq(user), eq("detail")))
                .thenReturn(mockButtonMap);

        // Mock FindAndFilterButtonsByDataProvider
        com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataProvider mockProvider =
            mock(com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataProvider.class);
        when(findAndFilterButtonsByDataManager.getProvider(eq(OBJECT_API_NAME)))
                .thenReturn(mockProvider);
        when(mockProvider.findAndFilterButtonsByData(eq(user), any()))
                .thenReturn(mockButtons);

        // Act
        FindAndFilterButtonsByData.Result result = buttonServiceUnderTest.findAndFilterButtonsByData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtons());
        verify(describeLogicService).findObject(eq(TENANT_ID), eq(OBJECT_API_NAME));
        verify(metaDataFindService).findObjectData(eq(user), eq(DATA_ID), eq(mockDescribe));
        verify(buttonService).findListButtonByButtonFilter(eq(mockDescribe), anyList(), eq(user), eq("detail"));
    }

    // Helper methods for creating test data
    private String createButtonJson() {
        Map<String, Object> buttonMap = new HashMap<>();
        buttonMap.put("id", BUTTON_ID);
        buttonMap.put("apiName", BUTTON_API_NAME);
        buttonMap.put("describeApiName", OBJECT_API_NAME);
        buttonMap.put("label", "测试按钮");
        buttonMap.put("isActive", true);
        buttonMap.put("isDeleted", false);
        buttonMap.put("actions", Lists.newArrayList());
        buttonMap.put("tenantId", TENANT_ID);
        buttonMap.put("buttonType", "custom");
        buttonMap.put("redirectType", "");
        buttonMap.put("usePage", Lists.newArrayList());
        return JSON.toJSONString(buttonMap);
    }

    private IUdefButton createMockButton() {
        // 使用lenient模式，允许不必要的Mock设置
        IUdefButton mockButton = mock(IUdefButton.class);
        lenient().when(mockButton.isDeleted()).thenReturn(Boolean.FALSE); // delete方法使用
        lenient().when(mockButton.getDescribeApiName()).thenReturn(OBJECT_API_NAME); // delete方法使用
        lenient().when(mockButton.getActions()).thenReturn(Lists.newArrayList()); // delete方法使用
        return mockButton;
    }

    private IUdefButton createRealButton() {
        // 创建真实的UdefButton对象，用于需要ButtonExt.convert的测试
        Map<String, Object> buttonMap = new HashMap<>();
        buttonMap.put("id", BUTTON_ID);
        buttonMap.put("apiName", BUTTON_API_NAME);
        buttonMap.put("describeApiName", OBJECT_API_NAME);
        buttonMap.put("label", "测试按钮");
        buttonMap.put("isActive", true);
        buttonMap.put("isDeleted", Boolean.FALSE);
        buttonMap.put("actions", Lists.newArrayList());
        buttonMap.put("tenantId", TENANT_ID);
        buttonMap.put("buttonType", "custom");
        buttonMap.put("redirectType", "");
        buttonMap.put("usePage", Lists.newArrayList());

        return new com.facishare.paas.metadata.impl.UdefButton(buttonMap);
    }
}