package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.validateRule.*;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.DescribeLayoutValidateModel;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.Rule;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.*;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.reflect.Whitebox;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ObjectValidRuleService JUnit5 测试类
 */
public class ObjectValidRuleServiceTest {

    private static final String TENANT_ID = "74255";

    private ObjectValidRuleService objectValidRuleService;

    @Mock
    private ValidateRuleServiceImpl validateRuleService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private LogService logService;

    @Mock
    private LayoutRuleLogicService layoutRuleLogicService;

    @Mock
    private RecordTypeLogicService recordTypeLogicService;

    @Mock
    private LayoutLogicService layoutService;


    @BeforeAll
    static void setupSpec() {
        // 创建 mock 实例
        I18nClient i18nClient = mock(I18nClient.class);
        I18nServiceImpl i18nServiceImpl = mock(I18nServiceImpl.class);

        // 给 mock 设置返回值
        when(i18nClient.getAllLanguage()).thenReturn(Collections.emptyList());

        // 设置内部字段 impl
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl);

        // 设置 SINGLETON
        Whitebox.setInternalState(I18nClient.class, "SINGLETON", i18nClient);
    }


    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        objectValidRuleService = new ObjectValidRuleService();

        // 使用反射设置私有字段
        Whitebox.setInternalState(objectValidRuleService, "validateRuleService", validateRuleService);
        Whitebox.setInternalState(objectValidRuleService, "describeLogicService", describeLogicService);
        Whitebox.setInternalState(objectValidRuleService, "logService", logService);
        Whitebox.setInternalState(objectValidRuleService, "layoutRuleLogicService", layoutRuleLogicService);
        Whitebox.setInternalState(objectValidRuleService, "recordTypeLogicService", recordTypeLogicService);
        Whitebox.setInternalState(objectValidRuleService, "layoutService", layoutService);
    }

    @AfterEach
    void cleanup() {
        RequestContextManager.removeContext();
    }

    private ServiceContext createServiceContext(String tenantId, String userId) {
        User user = new User(tenantId, userId);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        return new ServiceContext(requestContext, "cacheValidate", "cacheValidate");
    }

    private IObjectDescribe mockDescribe(String apiName, Long version) {
        IObjectDescribe describe = mock(IObjectDescribe.class);
        when(describe.getApiName()).thenReturn(apiName);
        when(describe.getVersion()).thenReturn(version.intValue());
        return describe;
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询验证规则列表的正常场景，成功返回规则列表
     */
    @Test
    @DisplayName("测试查询验证规则列表成功")
    void testFindRuleList_Success() {
        // Arrange
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleName = "test_rule";

        FindValidRuleList.Arg arg = new FindValidRuleList.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setRuleName(ruleName);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(true);
        ruleResult.setRuleList(Lists.newArrayList(new Rule()));

        when(validateRuleService.findRuleList(any(), any(), any())).thenReturn(ruleResult);

        // Act
        FindValidRuleList.Result result = objectValidRuleService.findRuleList(arg, context);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getRuleList().size());
        verify(validateRuleService).findRuleList(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询验证规则列表失败的场景
     */
    @Test
    @DisplayName("测试查询验证规则列表失败")
    void testFindRuleList_Failure() {
        // Arrange
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleName = "test_rule";

        FindValidRuleList.Arg arg = new FindValidRuleList.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setRuleName(ruleName);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(false);

        when(validateRuleService.findRuleList(any(), any(), any())).thenReturn(ruleResult);

        // Act
        FindValidRuleList.Result result = objectValidRuleService.findRuleList(arg, context);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(validateRuleService).findRuleList(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除验证规则的正常场景，验证规则删除成功并记录日志
     */
    @Test
    @DisplayName("测试删除验证规则成功")
    void testDelete_Success() {
        // Arrange
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";

        DeleteRule.Arg arg = new DeleteRule.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setRuleApiName(ruleApiName);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(true);

        when(validateRuleService.delete(any(), any(), any())).thenReturn(ruleResult);

        // Act
        DeleteRule.Result result = objectValidRuleService.delete(arg, context);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(validateRuleService).delete(any(), any(), any());
        verify(logService).log(any(), any(), any(), any(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除验证规则失败的场景，验证规则删除失败时的处理
     */
    @Test
    @DisplayName("测试删除验证规则失败")
    void testDelete_Failure() {
        // Arrange
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";

        DeleteRule.Arg arg = new DeleteRule.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setRuleApiName(ruleApiName);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(false);

        when(validateRuleService.delete(any(), any(), any())).thenReturn(ruleResult);

        // Act
        DeleteRule.Result result = objectValidRuleService.delete(arg, context);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(validateRuleService).delete(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询单个验证规则详情的正常场景，成功返回规则信息
     */
    @Test
    @DisplayName("测试查询单个验证规则详情成功")
    void testFindRuleInfo_Success() {
        // Arrange
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";

        FindRuleInfo.Arg arg = new FindRuleInfo.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setRuleApiName(ruleApiName);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(true);
        Map<String, Object> ruleMap = Maps.newHashMap();
        ruleMap.put("name", "Test Rule");
        ruleResult.setRule(ruleMap);

        when(validateRuleService.findRuleInfo(any(), any(), any())).thenReturn(ruleResult);

        // Act
        FindRuleInfo.Result result = objectValidRuleService.findRuleInfo(arg, context);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("Test Rule", result.getRule().get("name"));
        verify(validateRuleService).findRuleInfo(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询单个验证规则详情失败的场景
     */
    @Test
    @DisplayName("测试查询单个验证规则详情失败")
    void testFindRuleInfo_Failure() {
        // Arrange
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";

        FindRuleInfo.Arg arg = new FindRuleInfo.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setRuleApiName(ruleApiName);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(false);

        when(validateRuleService.findRuleInfo(any(), any(), any())).thenReturn(ruleResult);

        // Act
        FindRuleInfo.Result result = objectValidRuleService.findRuleInfo(arg, context);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(validateRuleService).findRuleInfo(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用验证规则的场景，验证规则成功启用并记录日志
     */
    @Test
    @DisplayName("测试启用验证规则成功")
    void testIsActive_Enable_Success() {
        // Arrange
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";

        ActiveRule.Arg arg = new ActiveRule.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setRuleApiName(ruleApiName);
        arg.setActive(true);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(true);

        when(validateRuleService.isActive(any(), any(), any(), anyBoolean())).thenReturn(ruleResult);

        // Act
        ActiveRule.Result result = objectValidRuleService.isActive(arg, context);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(validateRuleService).isActive(any(), any(), any(), eq(true));
        verify(logService).log(any(), any(), any(), any(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用验证规则的场景，验证规则成功禁用并记录日志
     */
    @Test
    @DisplayName("测试禁用验证规则成功")
    void testIsActive_Disable_Success() {
        // Arrange
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";

        ActiveRule.Arg arg = new ActiveRule.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setRuleApiName(ruleApiName);
        arg.setActive(false);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(true);

        when(validateRuleService.isActive(any(), any(), any(), anyBoolean())).thenReturn(ruleResult);

        // Act
        ActiveRule.Result result = objectValidRuleService.isActive(arg, context);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(validateRuleService).isActive(any(), any(), any(), eq(false));
        verify(logService).log(any(), any(), any(), any(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用/禁用验证规则失败的场景
     */
    @Test
    @DisplayName("测试启用/禁用验证规则失败")
    void testIsActive_Failure() {
        // Arrange
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";

        ActiveRule.Arg arg = new ActiveRule.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setRuleApiName(ruleApiName);
        arg.setActive(true);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(false);

        when(validateRuleService.isActive(any(), any(), any(), anyBoolean())).thenReturn(ruleResult);

        // Act
        ActiveRule.Result result = objectValidRuleService.isActive(arg, context);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(validateRuleService).isActive(any(), any(), any(), anyBoolean());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则的正常场景，规则验证通过并返回相应消息
     */
    @Test
    @DisplayName("测试验证规则成功 - 有阻塞消息")
    void testValidateRule_Success_WithBlockMessages() {
        // Arrange
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("id", "1");
        dataMap.put("name", "test");
        dataMap.put("object_describe_api_name", "AccountObj");
        ObjectData objectData = new ObjectData(dataMap);

        ObjectDataDocument objectDataDocument = ObjectDataDocument.of(objectData);

        ValidateRule.Arg arg = new ValidateRule.Arg();
        arg.setObjectData(objectDataDocument);
        arg.setObjectApiName("AccountObj");
        arg.setDetails(Maps.newHashMap());
        arg.setOption("test_option");

        ObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("AccountObj");

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(true);
        ruleResult.setSave(false);
        ruleResult.setEnableBlocking(true);
        ruleResult.setBlockMessages(Lists.newArrayList("test message"));
        ruleResult.setNonBlockMessages(Lists.newArrayList());

        Map<String, IObjectDescribe> objectDescribes = Maps.newHashMap();
        objectDescribes.put("AccountObj", objectDescribe);
        when(describeLogicService.findObjects(any(), any())).thenReturn(objectDescribes);
        when(validateRuleService.validateRule(any(), any(), any(), any(), any())).thenReturn(ruleResult);

        // Act
        ValidateRule.Result result = objectValidRuleService.validateRule(arg, createServiceContext(TENANT_ID, "1000"));

        // Assert
        assertNotNull(result);
        assertFalse(result.isSave());
        assertEquals("test message", result.getMessage());
        assertEquals(1, result.getBlockMessages().size());
        assertEquals("test message", result.getBlockMessages().get(0));
        verify(describeLogicService).findObjects(any(), any());
        verify(validateRuleService).validateRule(any(), any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则参数为空的异常场景
     */
    @Test
    @DisplayName("测试验证规则参数为空异常")
    void testValidateRule_NullArg_ThrowsException() {
        // Arrange
        ServiceContext context = createServiceContext(TENANT_ID, "1000");

        // Act & Assert
        assertThrows(ValidateException.class, () ->
                objectValidRuleService.validateRule(null, context));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则对象数据为空的异常场景
     */
    @Test
    @DisplayName("测试验证规则对象数据为空异常")
    void testValidateRule_NullObjectData_ThrowsException() {
        // Arrange
        ValidateRule.Arg arg = new ValidateRule.Arg();
        arg.setObjectApiName("AccountObj");
        // arg.setObjectData(null); // 不设置objectData
        ServiceContext context = createServiceContext(TENANT_ID, "1000");

        // Act & Assert
        assertThrows(ValidateException.class, () ->
                objectValidRuleService.validateRule(arg, context));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则不匹配的场景
     */
    @Test
    @DisplayName("测试验证规则不匹配")
    void testValidateRule_NoMatch() {
        // Arrange
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("id", "1");
        dataMap.put("name", "test");
        dataMap.put("object_describe_api_name", "AccountObj");
        ObjectData objectData = new ObjectData(dataMap);

        ObjectDataDocument objectDataDocument = ObjectDataDocument.of(objectData);

        ValidateRule.Arg arg = new ValidateRule.Arg();
        arg.setObjectData(objectDataDocument);
        arg.setObjectApiName("AccountObj");
        arg.setDetails(Maps.newHashMap());
        arg.setOption("test_option");

        ObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("AccountObj");

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(true);
        ruleResult.setSave(true);
        ruleResult.setEnableBlocking(false);
        ruleResult.setBlockMessages(Lists.newArrayList());
        ruleResult.setNonBlockMessages(Lists.newArrayList());

        Map<String, IObjectDescribe> objectDescribes = Maps.newHashMap();
        objectDescribes.put("AccountObj", objectDescribe);
        when(describeLogicService.findObjects(any(), any())).thenReturn(objectDescribes);
        when(validateRuleService.validateRule(any(), any(), any(), any(), any())).thenReturn(ruleResult);

        // Act
        ValidateRule.Result result = objectValidRuleService.validateRule(arg, createServiceContext(TENANT_ID, "1000"));

        // Assert
        assertNotNull(result);
        assertTrue(result.isSave());
        assertNull(result.getMessage());
        verify(describeLogicService).findObjects(any(), any());
        verify(validateRuleService).validateRule(any(), any(), any(), any(), any());
    }

    @Test
    void testCacheValidate_WhenObjectVersionMismatch_ShouldReturnFailure() {
        // 准备测试数据
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        ServiceContext context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate");

        CacheValidate.Arg arg = new CacheValidate.Arg();
        arg.setObjectApiName("testObject");
        arg.setDescribeVersion(1L);
        List<CacheValidate.DetailObjCacheValid> detailCacheValid = new ArrayList<>();
        CacheValidate.DetailObjCacheValid detailObj = new CacheValidate.DetailObjCacheValid();
        detailObj.setObjectApiName("detailObject");
        detailObj.setDescribeVersion(1L);
        detailCacheValid.add(detailObj);
        arg.setDetailCacheValid(detailCacheValid);

        IObjectDescribe describe = mockDescribe("testObject", 2L);
        Map<String, IObjectDescribe> objectDescribes = new HashMap<>();
        objectDescribes.put("testObject", describe);

        // Mock 行为
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(objectDescribes);

        // 调用方法
        CacheValidate.Result result = objectValidRuleService.cacheValidate(arg, context);

        // 验证结果
        assertFalse(result.isSuccess());
        verify(describeLogicService).findObjectsWithoutCopy(eq(TENANT_ID), any());
    }

    @Test
    void testCacheValidate_WhenLayoutRuleValidationFails_ShouldReturnFailure() {
        // 准备测试数据
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        ServiceContext context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate");

        CacheValidate.Arg arg = new CacheValidate.Arg();
        arg.setObjectApiName("testObject");
        arg.setDescribeVersion(2L);

        // 添加从对象缓存校验信息
        CacheValidate.DetailObjCacheValid detailCache = new CacheValidate.DetailObjCacheValid();
        detailCache.setObjectApiName("detailObject");
        detailCache.setDescribeVersion(2L);
        detailCache.setDetailLayoutList(Collections.emptyList());
        arg.setDetailCacheValid(Arrays.asList(detailCache));

        IObjectDescribe masterDescribe = mockDescribe("testObject", 2L);
        IObjectDescribe detailDescribe = mockDescribe("detailObject", 2L);
        when(detailDescribe.isActive()).thenReturn(true);

        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "masterField");
        fieldMap.put("type", "master_detail");
        when(detailDescribe.getFieldDescribes()).thenReturn(Arrays.asList(FieldDescribeFactory.newInstance(fieldMap)));

        Map<String, IObjectDescribe> objectDescribes = new HashMap<>();
        objectDescribes.put("testObject", masterDescribe);
        objectDescribes.put("detailObject", detailDescribe);

        // Mock 行为 - 需要先让 validateDetailObjectLogic 通过，然后 layoutRuleValidate 失败
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(objectDescribes);
        when(describeLogicService.findDetailDescribes(eq(TENANT_ID), eq("testObject"))).thenReturn(Arrays.asList(detailDescribe));
        when(describeLogicService.filterDescribesWithActionCode(eq(requestContext.getUser()), any(), any())).thenReturn(Arrays.asList(detailDescribe));
        when(layoutRuleLogicService.layoutRuleValidate(eq(requestContext.getUser()), any())).thenReturn(false);

        // 调用方法
        CacheValidate.Result result = objectValidRuleService.cacheValidate(arg, context);

        // 验证结果
        assertFalse(result.isSuccess());
        verify(describeLogicService).findObjectsWithoutCopy(eq(TENANT_ID), any());
        verify(layoutRuleLogicService).layoutRuleValidate(eq(requestContext.getUser()), any());
    }

    @Test
    void testCacheValidate_WhenAllValidationsPassed_ShouldReturnSuccess() {
        // 准备测试数据
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        ServiceContext context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate");

        CacheValidate.Arg arg = new CacheValidate.Arg();
        arg.setObjectApiName("testObject");
        arg.setDescribeVersion(2L);

        // 添加从对象缓存校验信息
        CacheValidate.DetailObjCacheValid detailCache = new CacheValidate.DetailObjCacheValid();
        detailCache.setObjectApiName("detailObject");
        detailCache.setDescribeVersion(2L);
        detailCache.setDetailLayoutList(Collections.emptyList());
        arg.setDetailCacheValid(Arrays.asList(detailCache));

        IObjectDescribe masterDescribe = mockDescribe("testObject", 2L);
        IObjectDescribe detailDescribe = mockDescribe("detailObject", 2L);
        when(detailDescribe.isActive()).thenReturn(true);

        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "masterField");
        fieldMap.put("type", "master_detail");
        when(detailDescribe.getFieldDescribes()).thenReturn(Arrays.asList(FieldDescribeFactory.newInstance(fieldMap)));

        Map<String, IObjectDescribe> objectDescribes = new HashMap<>();
        objectDescribes.put("testObject", masterDescribe);
        objectDescribes.put("detailObject", detailDescribe);

        // Mock 行为 - 需要让所有校验都通过
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(objectDescribes);
        when(describeLogicService.findDetailDescribes(eq(TENANT_ID), eq("testObject"))).thenReturn(Arrays.asList(detailDescribe));
        when(describeLogicService.filterDescribesWithActionCode(eq(requestContext.getUser()), any(), any())).thenReturn(Arrays.asList(detailDescribe));
        when(layoutRuleLogicService.layoutRuleValidate(eq(requestContext.getUser()), any())).thenReturn(true);

        // 调用方法
        CacheValidate.Result result = objectValidRuleService.cacheValidate(arg, context);

        // 验证结果
        assertTrue(result.isSuccess());
        verify(describeLogicService).findObjectsWithoutCopy(eq(TENANT_ID), any());
        verify(layoutRuleLogicService).layoutRuleValidate(eq(requestContext.getUser()), any());
    }

    @ParameterizedTest
    @CsvSource({
            "'', 1, false, 0",
            ", 1, false, 0",
            "testObject, 1, true, 1",
            "testObject, -1, false, 1",
            "testObject, 0, false, 1"
    })
    void testCacheValidate_BoundaryConditions(String objectApiName, Long describeVersion, boolean expectedSuccess, int interactions) {
        // 准备测试数据
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        ServiceContext context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate");

        CacheValidate.Arg arg = new CacheValidate.Arg();
        arg.setObjectApiName(objectApiName);
        arg.setDescribeVersion(describeVersion);
        arg.setDetailCacheValid(Collections.emptyList());

        Map<String, IObjectDescribe> objectDescribes = new HashMap<>();
        if ("testObject".equals(objectApiName)) {
            IObjectDescribe masterDescribe = mockDescribe("testObject", 1L);
            objectDescribes.put("testObject", masterDescribe);

            // 只有当版本匹配时才添加从对象和相关的缓存校验信息
            if (describeVersion != null && describeVersion == 1L) {
                IObjectDescribe detailDescribe = mockDescribe("detailObject", 1L);
                when(detailDescribe.isActive()).thenReturn(true);

                Map<String, Object> fieldMap = new HashMap<>();
                fieldMap.put("api_name", "masterField");
                fieldMap.put("type", "master_detail");
                when(detailDescribe.getFieldDescribes()).thenReturn(Arrays.asList(FieldDescribeFactory.newInstance(fieldMap)));

                objectDescribes.put("detailObject", detailDescribe);

                // 添加从对象缓存校验信息
                CacheValidate.DetailObjCacheValid detailCache = new CacheValidate.DetailObjCacheValid();
                detailCache.setObjectApiName("detailObject");
                detailCache.setDescribeVersion(1L);
                detailCache.setDetailLayoutList(Collections.emptyList());
                arg.setDetailCacheValid(Arrays.asList(detailCache));
            }
        }

        // Mock 行为
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(objectDescribes);
        if ("testObject".equals(objectApiName)) {
            if (describeVersion != null && describeVersion == 1L) {
                IObjectDescribe detailDescribe = objectDescribes.get("detailObject");
                when(describeLogicService.findDetailDescribes(eq(TENANT_ID), eq("testObject"))).thenReturn(Arrays.asList(detailDescribe));
                when(describeLogicService.filterDescribesWithActionCode(eq(requestContext.getUser()), any(), any())).thenReturn(Arrays.asList(detailDescribe));
            } else {
                when(describeLogicService.findDetailDescribes(eq(TENANT_ID), eq("testObject"))).thenReturn(Collections.emptyList());
            }
        }
        when(layoutRuleLogicService.layoutRuleValidate(eq(requestContext.getUser()), any())).thenReturn(true);

        // 调用方法
        CacheValidate.Result result = objectValidRuleService.cacheValidate(arg, context);

        // 验证结果
        assertEquals(expectedSuccess, result.isSuccess());
        verify(describeLogicService, times(interactions)).findObjectsWithoutCopy(eq(TENANT_ID), any());
    }

    @Test
    void testCacheValidate_Success_WithDetailObjects() {
        // 准备测试数据
        String tenantId = "74255";
        String describeApiName = "AccountObj";
        String detailApiName = "ContactObj";
        String layoutApiName = "DefaultLayout";
        Long version = 1L;
        Long lastModifiedTime = 1000L;

        CacheValidate.Arg arg = new CacheValidate.Arg();
        arg.setObjectApiName(describeApiName);
        arg.setDescribeVersion(version);
        arg.setLayoutApiName(layoutApiName);
        arg.setLayoutLastModifiedTime(lastModifiedTime);
        arg.setLayoutRule(Collections.emptyList());

        List<CacheValidate.DetailObjCacheValid> detailCacheValid = new ArrayList<>();
        CacheValidate.DetailObjCacheValid detailObj = new CacheValidate.DetailObjCacheValid();
        detailObj.setObjectApiName(detailApiName);
        detailObj.setDescribeVersion(version);
        List<CacheValidate.DetailLayoutRelatedInfo> detailLayoutList = new ArrayList<>();
        CacheValidate.DetailLayoutRelatedInfo layoutInfo = new CacheValidate.DetailLayoutRelatedInfo();
        layoutInfo.setLayoutApiName(layoutApiName);
        layoutInfo.setLayoutLastModifiedTime(lastModifiedTime);
        layoutInfo.setLayoutRule(Collections.emptyList());
        detailLayoutList.add(layoutInfo);
        detailObj.setDetailLayoutList(detailLayoutList);
        detailCacheValid.add(detailObj);
        arg.setDetailCacheValid(detailCacheValid);

        ServiceContext context = createServiceContext(tenantId, "1000");

        ObjectDescribe masterDescribe = new ObjectDescribe();
        masterDescribe.setApiName(describeApiName);
        masterDescribe.setVersion(version.intValue());

        ObjectDescribe detailDescribe = new ObjectDescribe();
        detailDescribe.setApiName(detailApiName);
        detailDescribe.setVersion(version.intValue());
        detailDescribe.setIsActive(true);

        // 添加主从关系字段
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "masterField");
        fieldMap.put("type", "master_detail");
        detailDescribe.setFieldDescribes(Arrays.asList(FieldDescribeFactory.newInstance(fieldMap)));

        Map<String, IObjectDescribe> objectDescribes = new HashMap<>();
        objectDescribes.put(describeApiName, masterDescribe);
        objectDescribes.put(detailApiName, detailDescribe);

        // Mock 行为
        when(describeLogicService.findObjectsWithoutCopy(eq(tenantId), any())).thenReturn(objectDescribes);
        when(describeLogicService.findDetailDescribes(eq(tenantId), eq(describeApiName)))
                .thenReturn(Arrays.asList(detailDescribe));
        when(describeLogicService.filterDescribesWithActionCode(eq(context.getUser()), any(), any()))
                .thenReturn(Arrays.asList(detailDescribe));
        when(layoutRuleLogicService.layoutRuleValidate(eq(context.getUser()), any())).thenReturn(true);

        // 调用方法
        CacheValidate.Result result = objectValidRuleService.cacheValidate(arg, context);

        // 验证结果
        assertTrue(result.isSuccess());
    }

    @Test
    void testCreateRule_Success() {
        // 准备测试数据
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";
        String jsonData = String.format("{\n" +
                "    \"api_name\": \"%s\",\n" +
                "    \"describe_api_name\": \"%s\",\n" +
                "    \"name\": \"Test Rule\",\n" +
                "    \"active\": true,\n" +
                "    \"rule_type\": \"VALIDATE\",\n" +
                "    \"error_level\": \"ERROR\"\n" +
                "}", ruleApiName, describeApiName);

        CreateRule.Arg arg = new CreateRule.Arg();
        arg.setJson_data(jsonData);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(true);

        // Mock 行为
        when(validateRuleService.create(any())).thenReturn(ruleResult);

        // 调用方法
        CreateRule.Result result = objectValidRuleService.create(arg, context);

        // 验证结果
        assertTrue(result.isSuccess());
        verify(validateRuleService).create(any());
        verify(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    @Test
    void testCreateRule_Failure() {
        // 准备测试数据
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";
        String jsonData = String.format("{\n" +
                "    \"api_name\": \"%s\",\n" +
                "    \"describe_api_name\": \"%s\",\n" +
                "    \"name\": \"Test Rule\",\n" +
                "    \"active\": true,\n" +
                "    \"rule_type\": \"VALIDATE\",\n" +
                "    \"error_level\": \"ERROR\"\n" +
                "}", ruleApiName, describeApiName);

        CreateRule.Arg arg = new CreateRule.Arg();
        arg.setJson_data(jsonData);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(false);

        // Mock 行为
        when(validateRuleService.create(any())).thenReturn(ruleResult);

        // 调用方法
        CreateRule.Result result = objectValidRuleService.create(arg, context);

        // 验证结果
        assertFalse(result.isSuccess());
        verify(validateRuleService).create(any());
    }

    @Test
    void testUpdateRule_Success() {
        // 准备测试数据
        String tenantId = "74255";
        String userId = "1000";
        String describeApiName = "AccountObj";
        String ruleApiName = "test_rule";
        String jsonData = String.format("{\n" +
                "    \"api_name\": \"%s\",\n" +
                "    \"describe_api_name\": \"%s\",\n" +
                "    \"name\": \"Updated Test Rule\",\n" +
                "    \"active\": true,\n" +
                "    \"rule_type\": \"VALIDATE\",\n" +
                "    \"error_level\": \"ERROR\"\n" +
                "}", ruleApiName, describeApiName);

        UpdateRule.Arg arg = new UpdateRule.Arg();
        arg.setJson_data(jsonData);
        ServiceContext context = createServiceContext(tenantId, userId);

        RuleResult ruleResult = new RuleResult();
        ruleResult.setSuccess(true);

        // Mock 行为
        when(validateRuleService.update(any(), any())).thenReturn(ruleResult);

        // 调用方法
        UpdateRule.Result result = objectValidRuleService.update(arg, context);

        // 验证结果
        assertTrue(result.isSuccess());
        verify(validateRuleService).update(any(), any());
        verify(logService).log(any(User.class), any(EventType.class), any(ActionType.class), anyString(), anyString());
    }

    /**
     * 测试业务类型匹配校验 - 成功的情况
     */
    @Test
    void testValidateRecordTypeMatchRelationSuccess() throws Exception {
        // 准备测试数据
        User user = new User(TENANT_ID, "user123");
        CacheValidate.Arg arg = new CacheValidate.Arg();
        arg.setRecordType("MasterRecordType");

        // 设置从对象缓存校验信息
        CacheValidate.DetailObjCacheValid detailCache = new CacheValidate.DetailObjCacheValid();
        detailCache.setObjectApiName("DetailObj__c");

        CacheValidate.DetailLayoutRelatedInfo layoutInfo = new CacheValidate.DetailLayoutRelatedInfo();
        layoutInfo.setRecordType("DetailRecordType1"); // 匹配的业务类型
        detailCache.setDetailLayoutList(Arrays.asList(layoutInfo));

        arg.setDetailCacheValid(Arrays.asList(detailCache));

        // Mock 主对象描述
        IObjectDescribe masterDescribe = mock(IObjectDescribe.class);
        when(masterDescribe.getApiName()).thenReturn("MasterObj__c");

        // Mock 从对象描述
        IObjectDescribe detailDescribe = mock(IObjectDescribe.class);
        when(detailDescribe.getApiName()).thenReturn("DetailObj__c");
        List<IObjectDescribe> detailDescribes = Arrays.asList(detailDescribe);

        // Mock 业务类型选项 - 包含匹配的业务类型
        IRecordTypeOption recordTypeOption = mock(IRecordTypeOption.class);
        when(recordTypeOption.getApiName()).thenReturn("DetailRecordType1");

        Map<String, List<IRecordTypeOption>> validRecordTypeMap = new HashMap<>();
        validRecordTypeMap.put("DetailObj__c", Arrays.asList(recordTypeOption));

        // Mock 服务行为
        when(recordTypeLogicService.findValidRecordTypeListMap(any(), any())).thenReturn(validRecordTypeMap);
        when(recordTypeLogicService.filterUnMatchRecordTypes(anyString(), any(), anyString(), anyString())).thenReturn(validRecordTypeMap);

        // 构建 layoutRuleValidateModels
        List<DescribeLayoutValidateModel> layoutRuleValidateModels = Arrays.asList(
                DescribeLayoutValidateModel.builder()
                        .objectApiName("MasterObj__c")
                        .isMaster(true)
                        .masterRecordType("MasterRecordType")
                        .build(),
                DescribeLayoutValidateModel.builder()
                        .objectApiName("DetailObj__c")
                        .isMaster(false)
                        .layoutRuleValidateInfos(Arrays.asList(
                                DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                                        .recordType("DetailRecordType1")
                                        .build()
                        ))
                        .build()
        );

        // 使用反射调用私有方法
        Method method = ObjectValidRuleService.class.getDeclaredMethod("validateRecordTypeMatchRelation",
                User.class, List.class, IObjectDescribe.class, List.class);
        method.setAccessible(true);

        boolean result = (boolean) method.invoke(objectValidRuleService, user, layoutRuleValidateModels, masterDescribe, detailDescribes);

        // 验证结果
        assertTrue(result);
        verify(recordTypeLogicService).findValidRecordTypeListMap(any(), any());
        verify(recordTypeLogicService).filterUnMatchRecordTypes(anyString(), any(), anyString(), anyString());
    }

    /**
     * 测试业务类型匹配校验 - 失败的情况
     */
    @Test
    void testValidateRecordTypeMatchRelationFailure() throws Exception {
        // 准备测试数据
        User user = new User(TENANT_ID, "user123");
        CacheValidate.Arg arg = new CacheValidate.Arg();
        arg.setRecordType("MasterRecordType");

        // 设置从对象缓存校验信息 - 包含不匹配的业务类型
        CacheValidate.DetailObjCacheValid detailCache = new CacheValidate.DetailObjCacheValid();
        detailCache.setObjectApiName("DetailObj__c");

        CacheValidate.DetailLayoutRelatedInfo layoutInfo = new CacheValidate.DetailLayoutRelatedInfo();
        layoutInfo.setRecordType("UnmatchedRecordType"); // 不匹配的业务类型
        detailCache.setDetailLayoutList(Arrays.asList(layoutInfo));

        arg.setDetailCacheValid(Arrays.asList(detailCache));

        // Mock 主对象描述
        IObjectDescribe masterDescribe = mock(IObjectDescribe.class);
        when(masterDescribe.getApiName()).thenReturn("MasterObj__c");

        // Mock 从对象描述
        IObjectDescribe detailDescribe = mock(IObjectDescribe.class);
        when(detailDescribe.getApiName()).thenReturn("DetailObj__c");
        List<IObjectDescribe> detailDescribes = Arrays.asList(detailDescribe);

        // Mock 业务类型选项 - 只包含匹配的业务类型，不包含 UnmatchedRecordType
        IRecordTypeOption recordTypeOption = mock(IRecordTypeOption.class);
        when(recordTypeOption.getApiName()).thenReturn("MatchedRecordType");

        Map<String, List<IRecordTypeOption>> validRecordTypeMap = new HashMap<>();
        validRecordTypeMap.put("DetailObj__c", Arrays.asList(recordTypeOption));

        // Mock 服务行为
        when(recordTypeLogicService.findValidRecordTypeListMap(any(), any())).thenReturn(validRecordTypeMap);
        when(recordTypeLogicService.filterUnMatchRecordTypes(anyString(), any(), anyString(), anyString())).thenReturn(validRecordTypeMap);

        // 构建 layoutRuleValidateModels
        List<DescribeLayoutValidateModel> layoutRuleValidateModels = Arrays.asList(
                DescribeLayoutValidateModel.builder()
                        .objectApiName("MasterObj__c")
                        .isMaster(true)
                        .masterRecordType("MasterRecordType")
                        .build(),
                DescribeLayoutValidateModel.builder()
                        .objectApiName("DetailObj__c")
                        .isMaster(false)
                        .layoutRuleValidateInfos(Arrays.asList(
                                DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                                        .recordType("UnmatchedRecordType")
                                        .build()
                        ))
                        .build()
        );

        // 使用反射调用私有方法
        Method method = ObjectValidRuleService.class.getDeclaredMethod("validateRecordTypeMatchRelation",
                User.class, List.class, IObjectDescribe.class, List.class);
        method.setAccessible(true);

        boolean result = (boolean) method.invoke(objectValidRuleService, user, layoutRuleValidateModels, masterDescribe, detailDescribes);

        // 验证结果 - 应该失败，因为业务类型不匹配
        assertFalse(result);
        verify(recordTypeLogicService).findValidRecordTypeListMap(any(), any());
        verify(recordTypeLogicService).filterUnMatchRecordTypes(anyString(), any(), anyString(), anyString());
    }
}
