package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege.*;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.ObjectDataCommonPrivilegeInfo;
import com.facishare.paas.appframework.privilege.dto.ObjectDataPermissionInfo;
import com.facishare.paas.appframework.privilege.dto.QueryDataAuth;
import com.facishare.paas.appframework.privilege.dto.QueryGroupInfoList;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectDataPrivilegeService测试类
 * 测试数据权限管理相关功能，包括权限检查、权限初始化、临时权限等核心功能
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
class ObjectDataPrivilegeServiceTest extends BaseServiceTest {

    private static final String OBJECT_API_NAME = "TestObject__c";
    private static final String DATA_ID = "test-data-id-001";
    private static final String ACTION_CODE = "view";

    @Mock
    private DataPrivilegeService dataPrivilegeService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private MetaDataMiscService metaDataMiscService;

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private MetaDataService metaDataService;

    @Mock
    private OrgService orgService;

    @InjectMocks
    private ObjectDataPrivilegeService objectDataPrivilegeService;

    @Override
    protected String getServiceName() {
        return "data_privilege";
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取通用权限列表的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试getCommonPrivilegeList方法")
    void testGetCommonPrivilegeList_Success() {
        // Arrange
        GetCommonPrivilegeList.Arg arg = new GetCommonPrivilegeList.Arg();

        List<ObjectDataCommonPrivilegeInfo> mockPrivilegeList = Lists.newArrayList();
        ObjectDataCommonPrivilegeInfo privilegeInfo = new ObjectDataCommonPrivilegeInfo();
        privilegeInfo.setObjectDescribeApiName(OBJECT_API_NAME);
        privilegeInfo.setObjectDescribeDisplayName("测试对象");
        privilegeInfo.setPermissionType("1"); // PRIVATE
        privilegeInfo.setObjectType(0); // 普通对象
        mockPrivilegeList.add(privilegeInfo);

        // 配置Mock行为 - 模拟getObjectDataCommonPrivilegeInfos方法的内部调用
        List<IObjectDescribe> mockDescribeList = Lists.newArrayList();
        Map<String, Object> describeData = Maps.newHashMap();
        describeData.put("api_name", OBJECT_API_NAME);
        describeData.put("display_name", "测试对象");
        describeData.put("tenant_id", TENANT_ID);
        ObjectDescribe mockDescribe = new ObjectDescribe(describeData);
        mockDescribeList.add(mockDescribe);

        when(describeLogicService.findObjectsByTenantId(eq(TENANT_ID), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), any()))
                .thenReturn(mockDescribeList);

        List<ObjectDataPermissionInfo> mockPermissionInfoList = Lists.newArrayList();
        ObjectDataPermissionInfo permissionInfo = new ObjectDataPermissionInfo();
        permissionInfo.setObjectDescribeApiName(OBJECT_API_NAME);
        permissionInfo.setObjectDescribeDisplayName("测试对象");
        permissionInfo.setPermissionType("1");
        mockPermissionInfoList.add(permissionInfo);

        when(dataPrivilegeService.getCommonPrivilegeListResult(any(), any()))
                .thenReturn(mockPermissionInfoList);

        // Act
        GetCommonPrivilegeList.Result result = objectDataPrivilegeService.getCommonPrivilegeList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getObjectDataPermissionInfos());
        assertEquals(1, result.getObjectDataPermissionInfos().size());
        assertEquals(OBJECT_API_NAME, result.getObjectDataPermissionInfos().get(0).getObjectDescribeApiName());

        // 验证Mock交互
        verify(dataPrivilegeService).getCommonPrivilegeListResult(testUser, mockDescribeList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectDataPrivilegeService);
        assertNotNull(dataPrivilegeService);
        assertNotNull(describeLogicService);
        assertNotNull(metaDataMiscService);
        assertNotNull(serviceFacade);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试初始化通用权限的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试initializeCommonPrivilege方法")
    void testInitializeCommonPrivilege_Success() {
        // Arrange
        List<IObjectDescribe> mockDescribeList = Lists.newArrayList();
        Map<String, Object> describeData = Maps.newHashMap();
        describeData.put("api_name", OBJECT_API_NAME);
        describeData.put("display_name", "测试对象");
        describeData.put("tenant_id", TENANT_ID);
        ObjectDescribe mockDescribe = new ObjectDescribe(describeData);
        mockDescribeList.add(mockDescribe);

        List<ObjectDataPermissionInfo> mockPermissionInfoList = Lists.newArrayList();
        ObjectDataPermissionInfo permissionInfo = new ObjectDataPermissionInfo();
        permissionInfo.setObjectDescribeApiName(OBJECT_API_NAME);
        permissionInfo.setObjectDescribeDisplayName("测试对象");
        permissionInfo.setPermissionType("1");
        mockPermissionInfoList.add(permissionInfo);

        // 配置Mock行为
        when(describeLogicService.findObjectsByTenantId(eq(TENANT_ID), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), any()))
                .thenReturn(mockDescribeList);
        when(dataPrivilegeService.getCommonPrivilegeListResult(any(), any()))
                .thenReturn(mockPermissionInfoList);
        doNothing().when(dataPrivilegeService).initCommonPrivilegeListResult(any(), any());

        // Act
        UpdateCommonPrivilegeList.Result result = objectDataPrivilegeService.initializeCommonPrivilege(serviceContext);

        // Assert
        assertNotNull(result);

        // 验证Mock交互
        verify(dataPrivilegeService).getCommonPrivilegeListResult(testUser, mockDescribeList);
        verify(dataPrivilegeService).initCommonPrivilegeListResult(eq(testUser), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象类型获取描述列表的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试getDescribeListByObjectType方法")
    void testGetDescribeListByObjectType_Success() {
        // Arrange
        Map<String, String> queryMap = Maps.newHashMap();
        queryMap.put("objectType", "publicObject");

        List<IObjectDescribe> mockDescribeList = Lists.newArrayList();
        Map<String, Object> describeData = Maps.newHashMap();
        describeData.put("api_name", OBJECT_API_NAME);
        describeData.put("display_name", "测试对象");
        describeData.put("tenant_id", TENANT_ID);
        ObjectDescribe mockDescribe = new ObjectDescribe(describeData);
        mockDescribeList.add(mockDescribe);

        // 配置Mock行为 - 使用ObjectDescribeFinder版本的方法
        when(describeLogicService.findObjectsByTenantId(any()))
                .thenReturn(mockDescribeList);

        // Act
        Map<String, java.util.Set<String>> result = objectDataPrivilegeService.getDescribeListByObjectType(queryMap, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey("publicObject"));
        assertNotNull(result.get("publicObject"));

        // 验证Mock交互
        verify(describeLogicService).findObjectsByTenantId(any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试权限检查的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试checkPrivilege方法")
    void testCheckPrivilege_Success() {
        // Arrange
        CheckPrivilege.Arg arg = new CheckPrivilege.Arg();
        arg.setId(DATA_ID);
        arg.setObjectDescribeApiName(OBJECT_API_NAME);
        arg.setActionCode(ACTION_CODE);

        // 创建Mock的ObjectDescribe
        Map<String, Object> describeData = Maps.newHashMap();
        describeData.put("api_name", OBJECT_API_NAME);
        describeData.put("display_name", "测试对象");
        describeData.put("tenant_id", TENANT_ID);
        ObjectDescribe mockDescribe = new ObjectDescribe(describeData);

        // 创建Mock的ObjectData
        Map<String, Object> objectDataMap = Maps.newHashMap();
        objectDataMap.put("id", DATA_ID);
        objectDataMap.put("name", "测试数据");
        ObjectData mockObjectData = new ObjectData(objectDataMap);

        // 创建Mock的权限结果
        Map<String, Permissions> mockPermissionsMap = Maps.newHashMap();
        mockPermissionsMap.put(DATA_ID, Permissions.READ_WRITE);

        // 配置Mock行为
        when(serviceFacade.findObject(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                .thenReturn(mockDescribe);
        when(serviceFacade.findObjectDataIgnoreFormula(eq(testUser), eq(DATA_ID), eq(OBJECT_API_NAME)))
                .thenReturn(mockObjectData);
        when(metaDataMiscService.checkPrivilege(eq(testUser), any(), eq(mockDescribe), eq(ACTION_CODE)))
                .thenReturn(mockPermissionsMap);

        // Act
        CheckPrivilege.Result result = objectDataPrivilegeService.checkPrivilege(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getValue());
        assertEquals(Permissions.READ_WRITE.getValue(), result.getValue());

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, OBJECT_API_NAME);
        verify(serviceFacade).findObjectDataIgnoreFormula(testUser, DATA_ID, OBJECT_API_NAME);
        verify(metaDataMiscService).checkPrivilege(eq(testUser), any(), eq(mockDescribe), eq(ACTION_CODE));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取数据授权的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试obtainDataAuth方法")
    void testObtainDataAuth_Success() {
        // Arrange
        QueryDataAuth.Arg arg = new QueryDataAuth.Arg();
        arg.setTenantId(TENANT_ID);
        arg.setUserId(USER_ID);
        arg.setUserName("测试用户");
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setName("测试数据");

        Map<String, Object> mockAuthResult = Maps.newHashMap();
        mockAuthResult.put("hasPermission", true);
        mockAuthResult.put("permissionType", "full");
        mockAuthResult.put("dataId", DATA_ID);

        // 配置Mock行为
        when(dataPrivilegeService.obtainDataAuth(any()))
                .thenReturn(mockAuthResult);

        // Act
        Map<String, Object> result = objectDataPrivilegeService.obtainDataAuth(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue((Boolean) result.get("hasPermission"));
        assertEquals("full", result.get("permissionType"));
        assertEquals(DATA_ID, result.get("dataId"));

        // 验证Mock交互
        verify(dataPrivilegeService).obtainDataAuth(arg);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询所有组信息列表的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试queryAllGroupInfoList方法")
    void testQueryAllGroupInfoList_Success() {
        // Arrange
        QueryGroupInfoList.Arg arg = new QueryGroupInfoList.Arg();
        arg.setKeyword("test");
        arg.setStatus(1);

        // Mock orgService返回空列表，避免NullPointerException
        when(orgService.getGroupInfoByGroupName(eq(TENANT_ID), eq(USER_ID), eq("test"), eq(1)))
                .thenReturn(Lists.newArrayList());

        // Act
        List<Map<String, Object>> result = objectDataPrivilegeService.queryAllGroupInfoList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty()); // 由于Mock返回空列表，结果应该为空

        // 验证Mock交互
        verify(orgService, times(1)).getGroupInfoByGroupName(eq(TENANT_ID), eq(USER_ID), eq("test"), eq(1));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试权限检查的无效参数场景
     */
    @Test
    @DisplayName("异常场景 - 测试checkPrivilege无效参数")
    void testCheckPrivilege_InvalidParam() {
        // Arrange
        CheckPrivilege.Arg arg = new CheckPrivilege.Arg();
        arg.setId("invalid-data-id");
        arg.setObjectDescribeApiName("InvalidObject__c");
        arg.setActionCode("invalid_action");

        // 配置Mock行为 - 返回null模拟未找到
        when(serviceFacade.findObject(eq(TENANT_ID), eq("InvalidObject__c")))
                .thenReturn(null);
        when(serviceFacade.findObjectDataIgnoreFormula(eq(testUser), eq("invalid-data-id"), eq("InvalidObject__c")))
                .thenReturn(null);

        Map<String, Permissions> emptyPermissionsMap = Maps.newHashMap();
        when(metaDataMiscService.checkPrivilege(eq(testUser), any(), any(), eq("invalid_action")))
                .thenReturn(emptyPermissionsMap);

        // Act
        CheckPrivilege.Result result = objectDataPrivilegeService.checkPrivilege(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getValue());
        assertEquals(Permissions.NO_PERMISSION.getValue(), result.getValue()); // 默认返回无权限

        // 验证Mock交互
        verify(serviceFacade).findObject(TENANT_ID, "InvalidObject__c");
        verify(serviceFacade).findObjectDataIgnoreFormula(testUser, "invalid-data-id", "InvalidObject__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取数据授权的空上下文场景
     */
    @Test
    @DisplayName("边界场景 - 测试obtainDataAuth空上下文")
    void testObtainDataAuth_NullContext() {
        // Arrange
        QueryDataAuth.Arg arg = new QueryDataAuth.Arg();
        arg.setTenantId(TENANT_ID);
        arg.setUserId(USER_ID);

        Map<String, Object> mockAuthResult = Maps.newHashMap();
        mockAuthResult.put("hasPermission", false);
        mockAuthResult.put("permissionType", "none");

        // 配置Mock行为
        when(dataPrivilegeService.obtainDataAuth(any()))
                .thenReturn(mockAuthResult);

        // Act
        Map<String, Object> result = objectDataPrivilegeService.obtainDataAuth(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse((Boolean) result.get("hasPermission"));
        assertEquals("none", result.get("permissionType"));

        // 验证Mock交互
        verify(dataPrivilegeService).obtainDataAuth(arg);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取通用权限列表的空结果场景
     */
    @Test
    @DisplayName("边界场景 - 测试getCommonPrivilegeList空结果")
    void testGetCommonPrivilegeList_EmptyResult() {
        // Arrange
        GetCommonPrivilegeList.Arg arg = new GetCommonPrivilegeList.Arg();

        List<IObjectDescribe> emptyDescribeList = Lists.newArrayList();
        List<ObjectDataPermissionInfo> emptyPermissionInfoList = Lists.newArrayList();

        // 配置Mock行为
        when(describeLogicService.findObjectsByTenantId(eq(TENANT_ID), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), any()))
                .thenReturn(emptyDescribeList);
        when(dataPrivilegeService.getCommonPrivilegeListResult(any(), any()))
                .thenReturn(emptyPermissionInfoList);

        // Act
        GetCommonPrivilegeList.Result result = objectDataPrivilegeService.getCommonPrivilegeList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getObjectDataPermissionInfos());
        assertTrue(result.getObjectDataPermissionInfos().isEmpty());

        // 验证Mock交互
        verify(dataPrivilegeService).getCommonPrivilegeListResult(testUser, emptyDescribeList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新通用权限列表的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试updateCommonPrivilegeList方法")
    void testUpdateCommonPrivilegeList_Success() {
        // Arrange
        UpdateCommonPrivilegeList.Arg arg = new UpdateCommonPrivilegeList.Arg();
        List<ObjectDataPermissionInfo> permissionInfos = Lists.newArrayList();
        ObjectDataPermissionInfo permissionInfo = new ObjectDataPermissionInfo();
        permissionInfo.setObjectDescribeApiName(OBJECT_API_NAME);
        permissionInfo.setPermissionType("private");
        permissionInfos.add(permissionInfo);
        arg.setObjectDataPermissionInfos(permissionInfos);

        // 配置Mock行为
        doNothing().when(dataPrivilegeService).updateCommonPrivilegeList(testUser, permissionInfos);

        // Act
        UpdateCommonPrivilegeList.Result result = objectDataPrivilegeService.updateCommonPrivilegeList(arg, serviceContext);

        // Assert
        assertNotNull(result);

        // 验证Mock交互
        verify(dataPrivilegeService).updateCommonPrivilegeList(testUser, permissionInfos);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加通用权限列表结果的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试addCommonPrivilegeListResult方法")
    void testAddCommonPrivilegeListResult_Success() {
        // Arrange
        UpdateCommonPrivilegeList.Arg arg = new UpdateCommonPrivilegeList.Arg();
        List<ObjectDataPermissionInfo> permissionInfos = Lists.newArrayList();
        ObjectDataPermissionInfo permissionInfo = new ObjectDataPermissionInfo();
        permissionInfo.setObjectDescribeApiName(OBJECT_API_NAME);
        permissionInfo.setPermissionType("public");
        permissionInfos.add(permissionInfo);
        arg.setObjectDataPermissionInfos(permissionInfos);

        // 配置Mock行为
        doNothing().when(dataPrivilegeService).addCommonPrivilegeListResult(testUser, permissionInfos);

        // Act
        UpdateCommonPrivilegeList.Result result = objectDataPrivilegeService.addCommonPrivilegeListResult(arg, serviceContext);

        // Assert
        assertNotNull(result);

        // 验证Mock交互
        verify(dataPrivilegeService).addCommonPrivilegeListResult(testUser, permissionInfos);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询维度交集状态的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试queryDimensionIntersectionStatus方法")
    void testQueryDimensionIntersectionStatus_Success() {
        // Arrange
        Map<String, String> queryContent = Maps.newHashMap();
        queryContent.put("entityId", "test-entity-id");
        queryContent.put("ruleType", "dimension");

        Map<String, Object> mockResult = Maps.newHashMap();
        mockResult.put("status", "active");
        mockResult.put("count", 5);

        // 配置Mock行为
        when(dataPrivilegeService.queryDimensionIntersectionStatus(testUser, queryContent))
                .thenReturn(mockResult);

        // Act
        Map<String, Object> result = objectDataPrivilegeService.queryDimensionIntersectionStatus(queryContent, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals("active", result.get("status"));
        assertEquals(5, result.get("count"));

        // 验证Mock交互
        verify(dataPrivilegeService).queryDimensionIntersectionStatus(testUser, queryContent);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取数据拥有者的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试getDataOwner方法")
    void testGetDataOwner_Success() {
        // Arrange
        GetDataOwner.Arg arg = new GetDataOwner.Arg();
        Map<String, List<String>> dataIdMap = Maps.newHashMap();
        dataIdMap.put(OBJECT_API_NAME, Lists.newArrayList(DATA_ID));
        arg.setDataIdMap(dataIdMap);

        // 创建Mock的ObjectData
        Map<String, Object> objectDataMap = Maps.newHashMap();
        objectDataMap.put("id", DATA_ID);
        objectDataMap.put("owner", USER_ID);
        ObjectData mockObjectData = new ObjectData(objectDataMap);

        // 配置Mock行为
        when(metaDataService.findObjectDataByIdsIncludeDeleted(testUser, Lists.newArrayList(DATA_ID), OBJECT_API_NAME))
                .thenReturn(Lists.newArrayList(mockObjectData));

        // Act
        GetDataOwner.Result result = objectDataPrivilegeService.getDataOwner(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getOwnerIdMap());

        // 验证Mock交互
        verify(metaDataService).findObjectDataByIdsIncludeDeleted(testUser, Lists.newArrayList(DATA_ID), OBJECT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取数据拥有者的空参数场景
     */
    @Test
    @DisplayName("边界场景 - 测试getDataOwner空参数")
    void testGetDataOwner_EmptyParam() {
        // Arrange
        GetDataOwner.Arg arg = new GetDataOwner.Arg();
        arg.setDataIdMap(Maps.newHashMap()); // 空的dataIdMap

        // Act
        GetDataOwner.Result result = objectDataPrivilegeService.getDataOwner(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getOwnerIdMap());
        assertTrue(result.getOwnerIdMap().isEmpty());

        // 验证没有调用metaDataService
        verify(metaDataService, never()).findObjectDataByIdsIncludeDeleted(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取外部用户的成功场景
     */
    @Test
    @DisplayName("正常场景 - 测试getOutUsers方法")
    void testGetOutUsers_Success() {
        // Act
        ConnectionEnterpriseUser.Result result = objectDataPrivilegeService.getOutUsers(serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询所有组信息列表的空用户场景
     */
    @Test
    @DisplayName("边界场景 - 测试queryAllGroupInfoList空用户")
    void testQueryAllGroupInfoList_NullUser() {
        // Arrange
        QueryGroupInfoList.Arg arg = new QueryGroupInfoList.Arg();
        arg.setKeyword("test");
        arg.setStatus(1);

        // 创建一个没有用户的ServiceContext
        ServiceContext contextWithoutUser = new ServiceContext(
                RequestContext.builder()
                        .tenantId(TENANT_ID)
                        .user(null) // 设置为null
                        .build(),
                "data_privilege",
                "test"
        );

        // Act & Assert - 期望抛出PermissionError异常
        assertThrows(com.facishare.paas.appframework.core.exception.PermissionError.class, () -> {
            objectDataPrivilegeService.queryAllGroupInfoList(arg, contextWithoutUser);
        });

        // 验证没有调用orgService
        verify(orgService, never()).getGroupInfoByGroupName(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询所有组信息列表的空租户ID场景
     */
    @Test
    @DisplayName("边界场景 - 测试queryAllGroupInfoList空租户ID")
    void testQueryAllGroupInfoList_EmptyTenantId() {
        // Arrange
        QueryGroupInfoList.Arg arg = new QueryGroupInfoList.Arg();
        arg.setKeyword("test");
        arg.setStatus(1);

        // 创建一个空租户ID的ServiceContext
        ServiceContext contextWithEmptyTenant = new ServiceContext(
                RequestContext.builder()
                        .tenantId("") // 设置为空字符串
                        .user(testUser)
                        .build(),
                "data_privilege",
                "test"
        );

        // Act
        List<Map<String, Object>> result = objectDataPrivilegeService.queryAllGroupInfoList(arg, contextWithEmptyTenant);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证没有调用orgService
        verify(orgService, never()).getGroupInfoByGroupName(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取描述列表的租户ID为空异常场景
     */
    @Test
    @DisplayName("异常场景 - 测试getDescribeListByObjectType租户ID为空")
    void testGetDescribeListByObjectType_EmptyTenantId() {
        // Arrange
        Map<String, String> queryMap = Maps.newHashMap();
        queryMap.put("objectType", "publicObject");

        // 创建一个空租户ID的ServiceContext
        ServiceContext contextWithEmptyTenant = new ServiceContext(
                RequestContext.builder()
                        .tenantId("") // 设置为空字符串
                        .user(testUser)
                        .build(),
                "data_privilege",
                "test"
        );

        // Act & Assert
        assertThrows(Exception.class, () -> {
            objectDataPrivilegeService.getDescribeListByObjectType(queryMap, contextWithEmptyTenant);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取描述列表的null租户ID异常场景
     */
    @Test
    @DisplayName("异常场景 - 测试getDescribeListByObjectType null租户ID")
    void testGetDescribeListByObjectType_NullTenantId() {
        // Arrange
        Map<String, String> queryMap = Maps.newHashMap();
        queryMap.put("objectType", "publicObject");

        // 创建一个null租户ID的ServiceContext
        ServiceContext contextWithNullTenant = new ServiceContext(
                RequestContext.builder()
                        .tenantId(null) // 设置为null
                        .user(testUser)
                        .build(),
                "data_privilege",
                "test"
        );

        // Act & Assert
        assertThrows(Exception.class, () -> {
            objectDataPrivilegeService.getDescribeListByObjectType(queryMap, contextWithNullTenant);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新通用权限列表的空参数场景
     */
    @Test
    @DisplayName("边界场景 - 测试updateCommonPrivilegeList空参数")
    void testUpdateCommonPrivilegeList_EmptyParam() {
        // Arrange
        UpdateCommonPrivilegeList.Arg arg = new UpdateCommonPrivilegeList.Arg();
        arg.setObjectDataPermissionInfos(Lists.newArrayList()); // 空列表

        // 配置Mock行为
        doNothing().when(dataPrivilegeService).updateCommonPrivilegeList(testUser, Lists.newArrayList());

        // Act
        UpdateCommonPrivilegeList.Result result = objectDataPrivilegeService.updateCommonPrivilegeList(arg, serviceContext);

        // Assert
        assertNotNull(result);

        // 验证Mock交互
        verify(dataPrivilegeService).updateCommonPrivilegeList(testUser, Lists.newArrayList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加通用权限列表结果的空参数场景
     */
    @Test
    @DisplayName("边界场景 - 测试addCommonPrivilegeListResult空参数")
    void testAddCommonPrivilegeListResult_EmptyParam() {
        // Arrange
        UpdateCommonPrivilegeList.Arg arg = new UpdateCommonPrivilegeList.Arg();
        arg.setObjectDataPermissionInfos(Lists.newArrayList()); // 空列表

        // 配置Mock行为
        doNothing().when(dataPrivilegeService).addCommonPrivilegeListResult(testUser, Lists.newArrayList());

        // Act
        UpdateCommonPrivilegeList.Result result = objectDataPrivilegeService.addCommonPrivilegeListResult(arg, serviceContext);

        // Assert
        assertNotNull(result);

        // 验证Mock交互
        verify(dataPrivilegeService).addCommonPrivilegeListResult(testUser, Lists.newArrayList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询维度交集状态的空参数场景
     */
    @Test
    @DisplayName("边界场景 - 测试queryDimensionIntersectionStatus空参数")
    void testQueryDimensionIntersectionStatus_EmptyParam() {
        // Arrange
        Map<String, String> emptyQueryContent = Maps.newHashMap();

        Map<String, Object> mockResult = Maps.newHashMap();
        mockResult.put("status", "inactive");
        mockResult.put("count", 0);

        // 配置Mock行为
        when(dataPrivilegeService.queryDimensionIntersectionStatus(testUser, emptyQueryContent))
                .thenReturn(mockResult);

        // Act
        Map<String, Object> result = objectDataPrivilegeService.queryDimensionIntersectionStatus(emptyQueryContent, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals("inactive", result.get("status"));
        assertEquals(0, result.get("count"));

        // 验证Mock交互
        verify(dataPrivilegeService).queryDimensionIntersectionStatus(testUser, emptyQueryContent);
    }
}
