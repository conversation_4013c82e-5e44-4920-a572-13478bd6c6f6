package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.GetCustomInitRoleActionCodes;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.GetSupportedActionCodes;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.IsAdminInitByDefault;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ObjectFunctionPrivilegeProviderServiceTest {

  private static final String TENANT_ID = "74255";
  private static final String USER_ID = "1000";
  private static final String DESCRIBE_API_NAME = "AccountObj";

  @Mock
  private FunctionPrivilegeProviderManager providerManager;

  @Mock
  private FunctionPrivilegeProvider functionPrivilegeProvider;

  @InjectMocks
  private ObjectFunctionPrivilegeProviderService objectFunctionPrivilegeProviderService;

  private ServiceContext serviceContext;

  @BeforeEach
  void setUp() {
    User user = new User(TENANT_ID, USER_ID);
    RequestContext requestContext = RequestContext.builder()
        .tenantId(TENANT_ID)
        .user(user)
        .build();
    serviceContext = new ServiceContext(requestContext, "FunctionPrivilegeProvider", "getSupportedActionCodes");
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取支持的操作代码成功的场景
   */
  @Test
  @DisplayName("测试获取支持的操作代码成功")
  void testGetSupportedActionCodesSuccess() {
    // Arrange
    GetSupportedActionCodes.Arg arg = GetSupportedActionCodes.Arg.builder()
        .describeApiName(DESCRIBE_API_NAME)
        .build();

    List<String> supportedActionCodes = Lists.newArrayList("create", "read", "update", "delete");

    when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(functionPrivilegeProvider);
    when(functionPrivilegeProvider.getSupportedActionCodes()).thenReturn(supportedActionCodes);

    // Act
    GetSupportedActionCodes.Result result = objectFunctionPrivilegeProviderService.getSupportedActionCodes(arg, serviceContext);

    // Then
    assertNotNull(result);
    assertEquals(supportedActionCodes, result.getSupportedActionCodes());
    verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    verify(functionPrivilegeProvider).getSupportedActionCodes();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取支持的操作代码返回空集合的场景
   */
  @Test
  @DisplayName("测试获取支持的操作代码返回空集合")
  void testGetSupportedActionCodesEmpty() {
    // Arrange
    GetSupportedActionCodes.Arg arg = GetSupportedActionCodes.Arg.builder()
        .describeApiName(DESCRIBE_API_NAME)
        .build();

    List<String> emptySupportedActionCodes = Lists.newArrayList();

    when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(functionPrivilegeProvider);
    when(functionPrivilegeProvider.getSupportedActionCodes()).thenReturn(emptySupportedActionCodes);

    // Act
    GetSupportedActionCodes.Result result = objectFunctionPrivilegeProviderService.getSupportedActionCodes(arg, serviceContext);

    // Then
    assertNotNull(result);
    assertTrue(result.getSupportedActionCodes().isEmpty());
    verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    verify(functionPrivilegeProvider).getSupportedActionCodes();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取自定义初始角色操作代码成功的场景
   */
  @Test
  @DisplayName("测试获取自定义初始角色操作代码成功")
  void testGetCustomInitRoleActionCodesSuccess() {
    // Arrange
    GetCustomInitRoleActionCodes.Arg arg = GetCustomInitRoleActionCodes.Arg.builder()
        .describeApiName(DESCRIBE_API_NAME)
        .build();

    Map<String, List<String>> customInitRoleActionCodes = Maps.newHashMap();
    customInitRoleActionCodes.put("role1", Lists.newArrayList("create", "read"));

    when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(functionPrivilegeProvider);
    when(functionPrivilegeProvider.getCustomInitRoleActionCodes()).thenReturn(customInitRoleActionCodes);

    // Act
    GetCustomInitRoleActionCodes.Result result = objectFunctionPrivilegeProviderService.getCustomInitRoleActionCodes(arg, serviceContext);

    // Then
    assertNotNull(result);
    assertEquals(customInitRoleActionCodes, result.getCustomInitRoleActionCodes());
    verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    verify(functionPrivilegeProvider).getCustomInitRoleActionCodes();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试获取自定义初始角色操作代码返回空列表的场景
   */
  @Test
  @DisplayName("测试获取自定义初始角色操作代码返回空列表")
  void testGetCustomInitRoleActionCodesEmpty() {
    // Arrange
    GetCustomInitRoleActionCodes.Arg arg = GetCustomInitRoleActionCodes.Arg.builder()
        .describeApiName(DESCRIBE_API_NAME)
        .build();

    Map<String, List<String>> emptyCustomInitRoleActionCodes = Maps.newHashMap();

    when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(functionPrivilegeProvider);
    when(functionPrivilegeProvider.getCustomInitRoleActionCodes()).thenReturn(emptyCustomInitRoleActionCodes);

    // Act
    GetCustomInitRoleActionCodes.Result result = objectFunctionPrivilegeProviderService.getCustomInitRoleActionCodes(arg, serviceContext);

    // Then
    assertNotNull(result);
    assertTrue(result.getCustomInitRoleActionCodes().isEmpty());
    verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    verify(functionPrivilegeProvider).getCustomInitRoleActionCodes();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试判断管理员是否默认初始化为true的场景
   */
  @Test
  @DisplayName("测试判断管理员默认初始化为true")
  void testIsAdminInitByDefaultTrue() {
    // Arrange
    IsAdminInitByDefault.Arg arg = IsAdminInitByDefault.Arg.builder()
        .describeApiName(DESCRIBE_API_NAME)
        .build();

    when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(functionPrivilegeProvider);
    when(functionPrivilegeProvider.isAdminInitByDefault()).thenReturn(true);

    // Act
    IsAdminInitByDefault.Result result = objectFunctionPrivilegeProviderService.isAdminInitByDefault(arg, serviceContext);

    // Then
    assertNotNull(result);
    assertTrue(result.getIsAdminInitByDefault());
    verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    verify(functionPrivilegeProvider).isAdminInitByDefault();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试判断管理员是否默认初始化为false的场景
   */
  @Test
  @DisplayName("测试判断管理员默认初始化为false")
  void testIsAdminInitByDefaultFalse() {
    // Arrange
    IsAdminInitByDefault.Arg arg = IsAdminInitByDefault.Arg.builder()
        .describeApiName(DESCRIBE_API_NAME)
        .build();

    when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(functionPrivilegeProvider);
    when(functionPrivilegeProvider.isAdminInitByDefault()).thenReturn(false);

    // Act
    IsAdminInitByDefault.Result result = objectFunctionPrivilegeProviderService.isAdminInitByDefault(arg, serviceContext);

    // Then
    assertNotNull(result);
    assertFalse(result.getIsAdminInitByDefault());
    verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    verify(functionPrivilegeProvider).isAdminInitByDefault();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试使用不同的对象API名称
   */
  @Test
  @DisplayName("测试使用不同的对象API名称")
  void testWithDifferentDescribeApiName() {
    // Arrange
    String differentApiName = "ContactObj";
    GetSupportedActionCodes.Arg arg = GetSupportedActionCodes.Arg.builder()
        .describeApiName(differentApiName)
        .build();

    List<String> supportedActionCodes = Lists.newArrayList("read", "update");

    when(providerManager.getLocalProvider(differentApiName)).thenReturn(functionPrivilegeProvider);
    when(functionPrivilegeProvider.getSupportedActionCodes()).thenReturn(supportedActionCodes);

    // Act
    GetSupportedActionCodes.Result result = objectFunctionPrivilegeProviderService.getSupportedActionCodes(arg, serviceContext);

    // Then
    assertNotNull(result);
    assertEquals(supportedActionCodes, result.getSupportedActionCodes());
    verify(providerManager).getLocalProvider(differentApiName);
    verify(functionPrivilegeProvider).getSupportedActionCodes();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试provider为null的异常场景
   */
  @Test
  @DisplayName("测试provider为null的异常场景")
  void testProviderIsNull() {
    // Arrange
    GetSupportedActionCodes.Arg arg = GetSupportedActionCodes.Arg.builder()
        .describeApiName(DESCRIBE_API_NAME)
        .build();

    when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(null);

    // Act & Then
    assertThrows(NullPointerException.class, () -> {
      objectFunctionPrivilegeProviderService.getSupportedActionCodes(arg, serviceContext);
    });

    verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试参数为null的异常场景
   */
  @Test
  @DisplayName("测试参数为null的异常场景")
  void testArgIsNull() {
    // Act & Then
    assertThrows(NullPointerException.class, () -> {
      objectFunctionPrivilegeProviderService.getSupportedActionCodes(null, serviceContext);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多次调用同一方法的场景
   */
  @Test
  @DisplayName("测试多次调用同一方法")
  void testMultipleCalls() {
    // Arrange
    GetSupportedActionCodes.Arg arg = GetSupportedActionCodes.Arg.builder()
        .describeApiName(DESCRIBE_API_NAME)
        .build();

    List<String> supportedActionCodes = Lists.newArrayList("create", "read");

    when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(functionPrivilegeProvider);
    when(functionPrivilegeProvider.getSupportedActionCodes()).thenReturn(supportedActionCodes);

    // Act
    GetSupportedActionCodes.Result result1 = objectFunctionPrivilegeProviderService.getSupportedActionCodes(arg, serviceContext);
    GetSupportedActionCodes.Result result2 = objectFunctionPrivilegeProviderService.getSupportedActionCodes(arg, serviceContext);

    // Then
    assertNotNull(result1);
    assertNotNull(result2);
    assertEquals(result1.getSupportedActionCodes(), result2.getSupportedActionCodes());
    verify(providerManager, times(2)).getLocalProvider(DESCRIBE_API_NAME);
    verify(functionPrivilegeProvider, times(2)).getSupportedActionCodes();
  }
}
