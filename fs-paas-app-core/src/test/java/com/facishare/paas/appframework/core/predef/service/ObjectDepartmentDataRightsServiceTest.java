package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.departmentDataPrivilege.*;
import com.facishare.paas.appframework.privilege.DepartmentDataRightsLogicService;
import com.facishare.paas.appframework.privilege.dto.DepartmentDataRights;
import com.facishare.paas.appframework.privilege.dto.DepartmentRightsResult;
import com.facishare.paas.appframework.privilege.dto.PageInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectDepartmentDataRightsService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectDepartmentDataRightsService单元测试")
class ObjectDepartmentDataRightsServiceTest {

    @Mock
    private DepartmentDataRightsLogicService departmentDataRightsLogicService;
    
    @InjectMocks
    private ObjectDepartmentDataRightsService objectDepartmentDataRightsService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String ENTITY_ID = "test_object__c";
    private final String DEPT_ID = "dept_123";
    private final String RIGHTS_ID = "rights_456";
    private final int SCENE = 0; // 部门内数据共享

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "departmentDataPrivilege", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新部门数据权限成功的正常场景，type为null且includeSub为true
     */
    @Test
    @DisplayName("测试upsert成功 - type为null且includeSub为true")
    void testUpsertSuccessWithIncludeSubTrue() {
        // Arrange
        UpsertDepartmentDataRights.Arg arg = new UpsertDepartmentDataRights.Arg();
        arg.setEntityIds(Arrays.asList(ENTITY_ID));
        arg.setDeptIds(Arrays.asList(DEPT_ID));
        arg.setType(null);
        arg.setScene(SCENE);
        arg.setIncludeSub(true);

        doNothing().when(departmentDataRightsLogicService).upsert(eq(user), eq(arg.getEntityIds()), 
                eq(arg.getDeptIds()), eq(SCENE), eq(0));

        // Act
        UpsertDepartmentDataRights.Result result = objectDepartmentDataRightsService.upsert(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(departmentDataRightsLogicService).upsert(eq(user), eq(arg.getEntityIds()), 
                eq(arg.getDeptIds()), eq(SCENE), eq(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新部门数据权限成功的正常场景，type为null且includeSub为false
     */
    @Test
    @DisplayName("测试upsert成功 - type为null且includeSub为false")
    void testUpsertSuccessWithIncludeSubFalse() {
        // Arrange
        UpsertDepartmentDataRights.Arg arg = new UpsertDepartmentDataRights.Arg();
        arg.setEntityIds(Arrays.asList(ENTITY_ID));
        arg.setDeptIds(Arrays.asList(DEPT_ID));
        arg.setType(null);
        arg.setScene(SCENE);
        arg.setIncludeSub(false);

        doNothing().when(departmentDataRightsLogicService).upsert(eq(user), eq(arg.getEntityIds()), 
                eq(arg.getDeptIds()), eq(SCENE), eq(1));

        // Act
        UpsertDepartmentDataRights.Result result = objectDepartmentDataRightsService.upsert(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(departmentDataRightsLogicService).upsert(eq(user), eq(arg.getEntityIds()), 
                eq(arg.getDeptIds()), eq(SCENE), eq(1));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新部门数据权限成功的正常场景，type有值时直接使用
     */
    @Test
    @DisplayName("测试upsert成功 - type有值时直接使用")
    void testUpsertSuccessWithTypeValue() {
        // Arrange
        UpsertDepartmentDataRights.Arg arg = new UpsertDepartmentDataRights.Arg();
        arg.setEntityIds(Arrays.asList(ENTITY_ID));
        arg.setDeptIds(Arrays.asList(DEPT_ID));
        arg.setType(2);
        arg.setScene(SCENE);
        arg.setIncludeSub(true); // 这个值应该被忽略

        doNothing().when(departmentDataRightsLogicService).upsert(eq(user), eq(arg.getEntityIds()), 
                eq(arg.getDeptIds()), eq(SCENE), eq(2));

        // Act
        UpsertDepartmentDataRights.Result result = objectDepartmentDataRightsService.upsert(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(departmentDataRightsLogicService).upsert(eq(user), eq(arg.getEntityIds()), 
                eq(arg.getDeptIds()), eq(SCENE), eq(2));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询部门数据权限成功的正常场景
     */
    @Test
    @DisplayName("测试query成功")
    void testQuerySuccess() {
        // Arrange
        QueryDepartmentDataRights.Arg arg = new QueryDepartmentDataRights.Arg();
        arg.setEntityIds(Arrays.asList(ENTITY_ID));
        arg.setDepartmentIds(Arrays.asList(DEPT_ID));
        arg.setScene(SCENE);
        arg.setPage(1);
        arg.setSize(10);

        DepartmentDataRights mockRights = DepartmentDataRights.builder()
                .id(RIGHTS_ID)
                .entityId(ENTITY_ID)
                .deptId(DEPT_ID)
                .scene(SCENE)
                .build();

        PageInfo mockPageInfo = PageInfo.builder()
                .currentPage(1)
                .pageSize(10)
                .total(1)
                .totalPage(1)
                .build();

        Tuple<PageInfo, List<DepartmentDataRights>> mockTuple = 
                new Tuple<>(mockPageInfo, Arrays.asList(mockRights));

        when(departmentDataRightsLogicService.query(eq(user), eq(arg.getEntityIds()), 
                eq(arg.getDepartmentIds()), eq(SCENE), eq(1), eq(10))).thenReturn(mockTuple);

        // Act
        QueryDepartmentDataRights.Result result = objectDepartmentDataRightsService.query(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPageInfo());
        assertNotNull(result.getResult());
        assertEquals(1, result.getResult().size());
        assertEquals(RIGHTS_ID, result.getResult().get(0).getId());
        verify(departmentDataRightsLogicService).query(eq(user), eq(arg.getEntityIds()), 
                eq(arg.getDepartmentIds()), eq(SCENE), eq(1), eq(10));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找部门数据权限成功的正常场景
     */
    @Test
    @DisplayName("测试find成功")
    void testFindSuccess() {
        // Arrange
        FindDepartmentDataRights.Arg arg = new FindDepartmentDataRights.Arg();
        arg.setIds(Arrays.asList(RIGHTS_ID));
        arg.setScene(SCENE);

        DepartmentDataRights mockRights = DepartmentDataRights.builder()
                .id(RIGHTS_ID)
                .entityId(ENTITY_ID)
                .deptId(DEPT_ID)
                .scene(SCENE)
                .build();

        when(departmentDataRightsLogicService.findByIds(eq(user), eq(SCENE), eq(arg.getIds())))
                .thenReturn(Arrays.asList(mockRights));

        // Act
        FindDepartmentDataRights.Result result = objectDepartmentDataRightsService.find(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(1, result.getResult().size());
        assertEquals(RIGHTS_ID, result.getResult().get(0).getId());
        verify(departmentDataRightsLogicService).findByIds(eq(user), eq(SCENE), eq(arg.getIds()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用部门数据权限成功的正常场景
     */
    @Test
    @DisplayName("测试disable成功")
    void testDisableSuccess() {
        // Arrange
        DisableDepartmentDataRights.Arg arg = new DisableDepartmentDataRights.Arg();
        arg.setIds(Arrays.asList(RIGHTS_ID));
        arg.setScene(SCENE);

        doNothing().when(departmentDataRightsLogicService).disableByIds(eq(user), eq(SCENE), eq(arg.getIds()));

        // Act
        DisableDepartmentDataRights.Result result = objectDepartmentDataRightsService.disable(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(departmentDataRightsLogicService).disableByIds(eq(user), eq(SCENE), eq(arg.getIds()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用部门数据权限成功的正常场景
     */
    @Test
    @DisplayName("测试enable成功")
    void testEnableSuccess() {
        // Arrange
        EnableDepartmentDataRights.Arg arg = new EnableDepartmentDataRights.Arg();
        arg.setIds(Arrays.asList(RIGHTS_ID));
        arg.setScene(SCENE);

        doNothing().when(departmentDataRightsLogicService).enable(eq(user), eq(SCENE), eq(arg.getIds()));

        // Act
        EnableDepartmentDataRights.Result result = objectDepartmentDataRightsService.enable(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(departmentDataRightsLogicService).enable(eq(user), eq(SCENE), eq(arg.getIds()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除部门数据权限成功的正常场景
     */
    @Test
    @DisplayName("测试delete成功")
    void testDeleteSuccess() {
        // Arrange
        DeleteDepartmentDataRights.Arg arg = new DeleteDepartmentDataRights.Arg();
        arg.setIds(Arrays.asList(RIGHTS_ID));
        arg.setScene(SCENE);

        doNothing().when(departmentDataRightsLogicService).deleteByIds(eq(user), eq(SCENE), eq(arg.getIds()));

        // Act
        DeleteDepartmentDataRights.Result result = objectDepartmentDataRightsService.delete(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(departmentDataRightsLogicService).deleteByIds(eq(user), eq(SCENE), eq(arg.getIds()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectDepartmentDataRightsService);
        assertNotNull(departmentDataRightsLogicService);
    }
}
