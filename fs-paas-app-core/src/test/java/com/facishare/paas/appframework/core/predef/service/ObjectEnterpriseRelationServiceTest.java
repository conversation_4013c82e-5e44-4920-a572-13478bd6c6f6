package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.enterpriserelation.GetRelationDownstreamInfo;
import com.facishare.paas.appframework.core.predef.service.dto.enterpriserelation.SupportEnterpriseRelation;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.privilege.EnterpriseRelationLogicService;
import com.facishare.paas.appframework.privilege.dto.SupportEnterpriseRelationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ObjectEnterpriseRelationService单元测试类
 * 测试企业关系服务的核心业务方法
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectEnterpriseRelationService单元测试")
class ObjectEnterpriseRelationServiceTest {

    @Mock
    private EnterpriseRelationLogicService enterpriseRelationLogicService;

    @InjectMocks
    private ObjectEnterpriseRelationService objectEnterpriseRelationService;

    private ServiceContext serviceContext;
    private User user;
    private User outUser;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OUT_USER_ID = "out_user_123";
    private final String OUT_TENANT_ID = "out_tenant_456";
    private final String DATA_ID = "data_123";
    private final String DESCRIBE_API_NAME = "Account__c";

    @BeforeEach
    void setUp() {
        // 创建普通用户
        user = new User(TENANT_ID, USER_ID);
        user.setUserName("Test User");

        // 创建外部用户 - 使用 Builder 模式
        outUser = User.builder()
                .tenantId(TENANT_ID)
                .userId(USER_ID)
                .outUserId(OUT_USER_ID)
                .outTenantId(OUT_TENANT_ID)
                .build();
        outUser.setUserName("Out User");

        // 创建服务上下文
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "enterprise_relation", "test");
    }

    // ==================== getOuterUserIdByObjectRelationId 方法测试 ====================

    /**
     * 测试 getOuterUserIdByObjectRelationId 方法 - 正常情况，返回普通用户信息
     */
    @Test
    @DisplayName("测试获取关系下游信息-普通用户")
    void testGetOuterUserIdByObjectRelationId_WithValidData_ShouldReturnUserInfo() {
        // Arrange
        GetRelationDownstreamInfo.Arg arg = new GetRelationDownstreamInfo.Arg();
        arg.setDataId(DATA_ID);
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        Map<String, User> mockResult = new HashMap<>();
        mockResult.put(DATA_ID, user);

        when(enterpriseRelationLogicService.getOwnerOutUserByDataIds(
                eq(user), eq(DESCRIBE_API_NAME), any(Set.class)))
                .thenReturn(mockResult);

        // Act
        GetRelationDownstreamInfo.Result result = objectEnterpriseRelationService
                .getOuterUserIdByObjectRelationId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(USER_ID, result.getUserId());
        assertEquals("Test User", result.getUserName());
        assertNull(result.getOuterTenantId());

        verify(enterpriseRelationLogicService).getOwnerOutUserByDataIds(
                eq(user), eq(DESCRIBE_API_NAME), any(Set.class));
    }

    /**
     * 测试 getOuterUserIdByObjectRelationId 方法 - 外部用户情况
     */
    @Test
    @DisplayName("测试获取关系下游信息-外部用户")
    void testGetOuterUserIdByObjectRelationId_WithOutUser_ShouldReturnOutUserInfo() {
        // Arrange
        GetRelationDownstreamInfo.Arg arg = new GetRelationDownstreamInfo.Arg();
        arg.setDataId(DATA_ID);
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        Map<String, User> mockResult = new HashMap<>();
        mockResult.put(DATA_ID, outUser);

        when(enterpriseRelationLogicService.getOwnerOutUserByDataIds(
                eq(user), eq(DESCRIBE_API_NAME), any(Set.class)))
                .thenReturn(mockResult);

        // Act
        GetRelationDownstreamInfo.Result result = objectEnterpriseRelationService
                .getOuterUserIdByObjectRelationId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(OUT_USER_ID, result.getUserId());
        assertEquals("Out User", result.getUserName());
        assertEquals(OUT_TENANT_ID, result.getOuterTenantId());

        verify(enterpriseRelationLogicService).getOwnerOutUserByDataIds(
                eq(user), eq(DESCRIBE_API_NAME), any(Set.class));
    }

    /**
     * 测试 getOuterUserIdByObjectRelationId 方法 - dataId 为 null 的情况
     */
    @Test
    @DisplayName("测试获取关系下游信息-dataId为null")
    void testGetOuterUserIdByObjectRelationId_WithNullDataId_ShouldReturnEmptyResult() {
        // Arrange
        GetRelationDownstreamInfo.Arg arg = new GetRelationDownstreamInfo.Arg();
        arg.setDataId(null);
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // Act
        GetRelationDownstreamInfo.Result result = objectEnterpriseRelationService
                .getOuterUserIdByObjectRelationId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getUserId());
        assertNull(result.getUserName());
        assertNull(result.getOuterTenantId());

        // 验证没有调用 enterpriseRelationLogicService
        verify(enterpriseRelationLogicService, never()).getOwnerOutUserByDataIds(any(), any(), any());
    }

    /**
     * 测试 getOuterUserIdByObjectRelationId 方法 - dataId 为空字符串的情况
     */
    @Test
    @DisplayName("测试获取关系下游信息-dataId为空字符串")
    void testGetOuterUserIdByObjectRelationId_WithEmptyDataId_ShouldReturnEmptyResult() {
        // Arrange
        GetRelationDownstreamInfo.Arg arg = new GetRelationDownstreamInfo.Arg();
        arg.setDataId("");
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // Act
        GetRelationDownstreamInfo.Result result = objectEnterpriseRelationService
                .getOuterUserIdByObjectRelationId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getUserId());
        assertNull(result.getUserName());
        assertNull(result.getOuterTenantId());

        // 验证没有调用 enterpriseRelationLogicService
        verify(enterpriseRelationLogicService, never()).getOwnerOutUserByDataIds(any(), any(), any());
    }

    /**
     * 测试 getOuterUserIdByObjectRelationId 方法 - 用户未找到的情况
     */
    @Test
    @DisplayName("测试获取关系下游信息-用户未找到")
    void testGetOuterUserIdByObjectRelationId_WithUserNotFound_ShouldReturnEmptyResult() {
        // Arrange
        GetRelationDownstreamInfo.Arg arg = new GetRelationDownstreamInfo.Arg();
        arg.setDataId(DATA_ID);
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        Map<String, User> mockResult = new HashMap<>();
        // 返回空的 Map，模拟用户未找到的情况

        when(enterpriseRelationLogicService.getOwnerOutUserByDataIds(
                eq(user), eq(DESCRIBE_API_NAME), any(Set.class)))
                .thenReturn(mockResult);

        // Act
        GetRelationDownstreamInfo.Result result = objectEnterpriseRelationService
                .getOuterUserIdByObjectRelationId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getUserId());
        assertNull(result.getUserName());
        assertNull(result.getOuterTenantId());

        verify(enterpriseRelationLogicService).getOwnerOutUserByDataIds(
                eq(user), eq(DESCRIBE_API_NAME), any(Set.class));
    }

    /**
     * 测试 getOuterUserIdByObjectRelationId 方法 - 返回的 Map 中没有对应的 dataId
     */
    @Test
    @DisplayName("测试获取关系下游信息-Map中无对应dataId")
    void testGetOuterUserIdByObjectRelationId_WithDataIdNotInMap_ShouldReturnEmptyResult() {
        // Arrange
        GetRelationDownstreamInfo.Arg arg = new GetRelationDownstreamInfo.Arg();
        arg.setDataId(DATA_ID);
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        Map<String, User> mockResult = new HashMap<>();
        mockResult.put("other_data_id", user); // 不同的 dataId

        when(enterpriseRelationLogicService.getOwnerOutUserByDataIds(
                eq(user), eq(DESCRIBE_API_NAME), any(Set.class)))
                .thenReturn(mockResult);

        // Act
        GetRelationDownstreamInfo.Result result = objectEnterpriseRelationService
                .getOuterUserIdByObjectRelationId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getUserId());
        assertNull(result.getUserName());
        assertNull(result.getOuterTenantId());

        verify(enterpriseRelationLogicService).getOwnerOutUserByDataIds(
                eq(user), eq(DESCRIBE_API_NAME), any(Set.class));
    }

    // ==================== isSupportEnterpriseRelation 方法测试 ====================

    /**
     * 测试 isSupportEnterpriseRelation 方法 - 完全支持的情况
     */
    @Test
    @DisplayName("测试企业关系支持检查-完全支持")
    void testIsSupportEnterpriseRelation_WithFullSupport_ShouldReturnTrue() {
        // Arrange
        SupportEnterpriseRelationResult mockResult = SupportEnterpriseRelationResult.of(true, true);

        when(enterpriseRelationLogicService.isSupportEnterpriseRelation(TENANT_ID))
                .thenReturn(mockResult);

        // Act
        SupportEnterpriseRelation.Result result = objectEnterpriseRelationService
                .isSupportEnterpriseRelation(serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getSupport());
        assertTrue(result.getSupportTeamMember());

        verify(enterpriseRelationLogicService).isSupportEnterpriseRelation(TENANT_ID);
    }

    /**
     * 测试 isSupportEnterpriseRelation 方法 - 完全不支持的情况
     */
    @Test
    @DisplayName("测试企业关系支持检查-完全不支持")
    void testIsSupportEnterpriseRelation_WithNoSupport_ShouldReturnFalse() {
        // Arrange
        SupportEnterpriseRelationResult mockResult = SupportEnterpriseRelationResult.of(false, false);

        when(enterpriseRelationLogicService.isSupportEnterpriseRelation(TENANT_ID))
                .thenReturn(mockResult);

        // Act
        SupportEnterpriseRelation.Result result = objectEnterpriseRelationService
                .isSupportEnterpriseRelation(serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.getSupport());
        assertFalse(result.getSupportTeamMember());

        verify(enterpriseRelationLogicService).isSupportEnterpriseRelation(TENANT_ID);
    }

    /**
     * 测试 isSupportEnterpriseRelation 方法 - 部分支持的情况（支持基础功能但不支持团队成员）
     */
    @Test
    @DisplayName("测试企业关系支持检查-部分支持1")
    void testIsSupportEnterpriseRelation_WithPartialSupport1_ShouldReturnMixed() {
        // Arrange
        SupportEnterpriseRelationResult mockResult = SupportEnterpriseRelationResult.of(true, false);

        when(enterpriseRelationLogicService.isSupportEnterpriseRelation(TENANT_ID))
                .thenReturn(mockResult);

        // Act
        SupportEnterpriseRelation.Result result = objectEnterpriseRelationService
                .isSupportEnterpriseRelation(serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getSupport());
        assertFalse(result.getSupportTeamMember());

        verify(enterpriseRelationLogicService).isSupportEnterpriseRelation(TENANT_ID);
    }

    /**
     * 测试 isSupportEnterpriseRelation 方法 - 部分支持的情况（不支持基础功能但支持团队成员）
     */
    @Test
    @DisplayName("测试企业关系支持检查-部分支持2")
    void testIsSupportEnterpriseRelation_WithPartialSupport2_ShouldReturnMixed() {
        // Arrange
        SupportEnterpriseRelationResult mockResult = SupportEnterpriseRelationResult.of(false, true);

        when(enterpriseRelationLogicService.isSupportEnterpriseRelation(TENANT_ID))
                .thenReturn(mockResult);

        // Act
        SupportEnterpriseRelation.Result result = objectEnterpriseRelationService
                .isSupportEnterpriseRelation(serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.getSupport());
        assertTrue(result.getSupportTeamMember());

        verify(enterpriseRelationLogicService).isSupportEnterpriseRelation(TENANT_ID);
    }

    // ==================== 基础功能测试 ====================

    /**
     * 测试服务实例化和依赖注入是否正确
     */
    @Test
    @DisplayName("测试服务实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectEnterpriseRelationService);
        assertNotNull(enterpriseRelationLogicService);
    }

    /**
     * 测试 ServiceContext 构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("enterprise_relation", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }
}
