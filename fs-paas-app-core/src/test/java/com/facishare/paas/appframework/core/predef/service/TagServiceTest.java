package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.tag.*;
import com.facishare.paas.appframework.core.predef.service.dto.switchcache.FindSystemTagGroupByApplyObject;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.TagLogicService;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.appframework.metadata.dto.tag.TagGroupTag;
import com.facishare.paas.metadata.api.DataAndSubTag;
import com.facishare.paas.metadata.api.describe.ISubTagDescribe;
import com.facishare.paas.metadata.api.describe.ITagDescribe;
import com.facishare.paas.metadata.impl.describe.SubTagDescribe;
import com.facishare.paas.metadata.impl.describe.TagDescribe;
import com.facishare.paas.metadata.impl.search.TagQueryInfo;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * TagService单元测试类
 * 从Groovy测试迁移而来
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("TagService单元测试")
class TagServiceTest {

    @Mock
    private TagLogicService tagLogicService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private OrgService orgService;
    
    @Mock
    private EntityReferenceService entityReferenceService;

    @InjectMocks
    private TagService tagService;

    private ServiceContext serviceContext;
    private User user;

    private static final String TENANT_ID = "78057";
    private static final String USER_ID = "1000";
    private static final String DESCRIBE_API_NAME = "AccountObj";
    private static final String DATA_ID = "**********";
    private static final String TAG_ID = "tag123";
    private static final String GROUP_ID = "group456";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .user(user)
                .tenantId(TENANT_ID)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        serviceContext = new ServiceContext(requestContext, "tag", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找所有标签成功的场景
     * 迁移自Groovy测试：test find all tags
     */
    @Test
    @DisplayName("测试查找所有标签成功")
    void testFindAllTagsByDescribeApiNameSuccess() {
        // Arrange
        ListAllTags.Arg arg = new ListAllTags.Arg();
        arg.setName("lab");
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        List<ISubTagDescribe> mockTags = buildTagDescribes();
        List<ITagDescribe> mockTagGroups = buildTagGroups();
        
        when(tagLogicService.findAllTags(eq(DESCRIBE_API_NAME), eq(TENANT_ID), eq("lab"), isNull()))
                .thenReturn(mockTags);
        when(tagLogicService.findTagGroupsByIds(eq(TENANT_ID), any()))
                .thenReturn(mockTagGroups);

        // Act
        ListAllTags.Result result = tagService.findAllTagsByDescribeApiName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGroups());
        assertEquals(1, result.getGroups().size());
        assertEquals("默认分组", result.getGroups().get(0).getTagGroupName());
        assertNotNull(result.getGroups().get(0).getTags());
        assertEquals(1, result.getGroups().get(0).getTags().size());
        
        verify(tagLogicService).findAllTags(eq(DESCRIBE_API_NAME), eq(TENANT_ID), eq("lab"), isNull());
        verify(tagLogicService).findTagGroupsByIds(eq(TENANT_ID), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找数据标签成功的场景
     * 迁移自Groovy测试：test find data tags
     */
    @Test
    @DisplayName("测试查找数据标签成功")
    void testFindDataTagsSuccess() {
        // Arrange
        FindDataTags.Arg arg = new FindDataTags.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);

        List<ISubTagDescribe> mockTags = buildTagDescribes();
        List<ITagDescribe> mockTagGroups = buildTagGroups();
        
        when(tagLogicService.findTagsByDataId(eq(DESCRIBE_API_NAME), eq(DATA_ID), any()))
                .thenReturn(mockTags);
        when(tagLogicService.findTagGroupsByIds(eq(TENANT_ID), any()))
                .thenReturn(mockTagGroups);

        // Act
        FindDataTags.Result result = tagService.findDataTags(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGroups());
        assertEquals(1, result.getGroups().size());
        assertNotNull(result.getGroups().get(0).getTags());
        assertEquals(1, result.getGroups().get(0).getTags().size());
        
        verify(tagLogicService).findTagsByDataId(eq(DESCRIBE_API_NAME), eq(DATA_ID), any());
        verify(tagLogicService).findTagGroupsByIds(eq(TENANT_ID), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找数据标签时标签为空的场景
     */
    @Test
    @DisplayName("测试查找数据标签时标签为空")
    void testFindDataTagsEmpty() {
        // Arrange
        FindDataTags.Arg arg = new FindDataTags.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);

        when(tagLogicService.findTagsByDataId(eq(DESCRIBE_API_NAME), eq(DATA_ID), any()))
                .thenReturn(Lists.newArrayList());

        // Act
        FindDataTags.Result result = tagService.findDataTags(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getGroups());
        
        verify(tagLogicService).findTagsByDataId(eq(DESCRIBE_API_NAME), eq(DATA_ID), any());
        verify(tagLogicService, never()).findTagGroupsByIds(any(), any(), anyBoolean());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建标签成功的场景
     */
    @Test
    @DisplayName("测试创建标签成功")
    void testCreateTagSuccess() {
        // Arrange
        CreateOrUpdateTag.Arg arg = new CreateOrUpdateTag.Arg();
        arg.setTagApiName("test_tag__c");
        arg.setTagName("测试标签");
        arg.setGroupId(GROUP_ID);
        arg.setDescription("测试描述");

        ISubTagDescribe mockCreatedTag = new SubTagDescribe();
        mockCreatedTag.setId(TAG_ID);
        mockCreatedTag.setTagApiName("test_tag__c");
        mockCreatedTag.setName("测试标签");
        mockCreatedTag.setTagId(GROUP_ID);
        mockCreatedTag.setDescription("测试描述");
        
        when(tagLogicService.createTag(any(ISubTagDescribe.class), eq(user)))
                .thenReturn(mockCreatedTag);

        // Act
        CreateOrUpdateTag.Result result = tagService.createTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSubTagDescribe());
        assertEquals("test_tag__c", result.getSubTagDescribe().get(TagDocument.API_NAME));
        assertEquals("测试标签", result.getSubTagDescribe().get(TagDocument.NAME));
        assertEquals(GROUP_ID, result.getSubTagDescribe().get(TagDocument.TAG_GROUP_ID));
        
        verify(tagLogicService).createTag(any(ISubTagDescribe.class), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据标签ID查找标签成功的场景
     */
    @Test
    @DisplayName("测试根据标签ID查找标签成功")
    void testFindTagByIdSuccess() {
        // Arrange
        FindTagById.Arg arg = new FindTagById.Arg();
        arg.setTagId(TAG_ID);
        arg.setSourceInfo("tag_management");

        ISubTagDescribe mockTag = new SubTagDescribe();
        mockTag.setId(TAG_ID);
        mockTag.setName("测试标签");
        mockTag.setTagId(GROUP_ID);
        
        List<ITagDescribe> mockTagGroups = buildTagGroups();
        
        when(tagLogicService.findTagById(eq(TAG_ID), eq(user), eq(true)))
                .thenReturn(mockTag);
        when(tagLogicService.findTagGroupsByIds(eq(TENANT_ID), eq(Lists.newArrayList(GROUP_ID)), eq(true)))
                .thenReturn(mockTagGroups);

        // Act
        FindTagById.Result result = tagService.findTagById(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getTag());
        assertEquals("测试标签", result.getTag().get(TagDocument.NAME));
        assertEquals("默认分组", result.getTag().get(TagDocument.TAG_GROUP_NAME));
        
        verify(tagLogicService).findTagById(eq(TAG_ID), eq(user), eq(true));
        verify(tagLogicService).findTagGroupsByIds(eq(TENANT_ID), eq(Lists.newArrayList(GROUP_ID)), eq(true));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据标签ID查找标签时标签不存在的场景
     */
    @Test
    @DisplayName("测试根据标签ID查找标签时标签不存在")
    void testFindTagByIdNotFound() {
        // Arrange
        FindTagById.Arg arg = new FindTagById.Arg();
        arg.setTagId(TAG_ID);

        when(tagLogicService.findTagById(eq(TAG_ID), eq(user), eq(false)))
                .thenReturn(null);

        // Act
        FindTagById.Result result = tagService.findTagById(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getTag());
        
        verify(tagLogicService).findTagById(eq(TAG_ID), eq(user), eq(false));
        verify(tagLogicService, never()).findTagGroupsByIds(any(), any(), anyBoolean());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除标签成功的场景
     */
    @Test
    @DisplayName("测试删除标签成功")
    void testDeleteTagSuccess() {
        // Arrange
        CreateOrUpdateTag.Arg arg = new CreateOrUpdateTag.Arg();
        arg.setGroupId(GROUP_ID);
        arg.setTagId(TAG_ID);

        // Act
        CreateOrUpdateTag.Result result = tagService.deleteTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagLogicService).deleteTag(eq(GROUP_ID), eq(TAG_ID), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用标签成功的场景
     */
    @Test
    @DisplayName("测试启用标签成功")
    void testEnableTagSuccess() {
        // Arrange
        CreateOrUpdateTag.Arg arg = new CreateOrUpdateTag.Arg();
        arg.setTagId(TAG_ID);

        // Act
        CreateOrUpdateTag.Result result = tagService.enableTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagLogicService).enableTag(eq(TAG_ID), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建标签组成功的场景
     */
    @Test
    @DisplayName("测试创建标签组成功")
    void testCreateTagGroupSuccess() {
        // Arrange
        CreateTagGroup.Arg arg = new CreateTagGroup.Arg();
        arg.setTagGroupName("测试分组");
        arg.setApiName("test_group__c");
        arg.setIsAppliedToAll(false);
        arg.setDescribeApiNames(Lists.newArrayList(DESCRIBE_API_NAME));
        arg.setTagDefineType("custom");
        arg.setRanges(new SceneDTO.Range());
        arg.setTagInfoList(Lists.newArrayList());
        arg.setGroupDescription("测试分组描述");

        ITagDescribe mockTagGroup = new TagDescribe();
        mockTagGroup.setId(GROUP_ID);
        mockTagGroup.setType("测试分组");
        mockTagGroup.setApiName("test_group__c");
        
        when(tagLogicService.createTagGroup(anyString(), anyString(), anyList(), anyBoolean(), anyList(), anyString(), any(), any(), anyString(), eq(user)))
                .thenReturn(mockTagGroup);

        // Act
        CreateTagGroup.Result result = tagService.createTagGroup(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getTagDescribe());
        assertEquals(GROUP_ID, result.getTagDescribe().getId());
        assertEquals("测试分组", result.getTagDescribe().getType());
        
        verify(tagLogicService).createTagGroup(anyString(), anyString(), anyList(), anyBoolean(), anyList(), anyString(), any(), any(), anyString(), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建标签组时参数错误抛出异常
     */
    @Test
    @DisplayName("测试创建标签组时参数错误抛出异常")
    void testCreateTagGroupThrowsValidateExceptionWhenInvalidParams() {
        // Arrange
        CreateTagGroup.Arg arg = new CreateTagGroup.Arg();
        arg.setTagGroupName("测试分组测试分组测试分组"); // 超过10个字符
        arg.setApiName("invalid_api_name"); // 不符合pattern
        arg.setTagInfoList(Lists.newArrayList());

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            tagService.createTagGroup(arg, serviceContext);
        });
        
        verify(tagLogicService, never()).createTagGroup(anyString(), anyString(), anyList(), anyBoolean(), anyList(), anyString(), any(), any(), anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新标签成功的场景
     */
    @Test
    @DisplayName("测试更新标签成功")
    void testUpdateTagSuccess() {
        // Arrange
        CreateOrUpdateTag.Arg arg = new CreateOrUpdateTag.Arg();
        arg.setTagApiName("updated_tag__c");
        arg.setTagName("更新标签");
        arg.setTagId(TAG_ID);
        arg.setGroupId(GROUP_ID);
        arg.setDescription("更新描述");

        ISubTagDescribe mockUpdatedTag = new SubTagDescribe();
        mockUpdatedTag.setId(TAG_ID);
        mockUpdatedTag.setTagApiName("updated_tag__c");
        mockUpdatedTag.setName("更新标签");
        mockUpdatedTag.setTagId(GROUP_ID);
        mockUpdatedTag.setDescription("更新描述");
        
        when(tagLogicService.updateTag(any(ISubTagDescribe.class), anyList(), eq(user)))
                .thenReturn(mockUpdatedTag);

        // Act
        CreateOrUpdateTag.Result result = tagService.updateTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSubTagDescribe());
        assertEquals("updated_tag__c", result.getSubTagDescribe().get(TagDocument.API_NAME));
        assertEquals("更新标签", result.getSubTagDescribe().get(TagDocument.NAME));
        
        verify(tagLogicService).updateTag(any(ISubTagDescribe.class), anyList(), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新标签时参数错误抛出异常
     */
    @Test
    @DisplayName("测试更新标签时参数错误抛出异常")
    void testUpdateTagThrowsValidateExceptionWhenInvalidParams() {
        // Arrange
        CreateOrUpdateTag.Arg arg = new CreateOrUpdateTag.Arg();
        arg.setTagApiName("invalid_api_name"); // 不符合pattern
        arg.setTagName("标签名称超过十个字符长度限制"); // 超过10个字符
        arg.setTagId(TAG_ID);
        arg.setGroupId(GROUP_ID);

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            tagService.updateTag(arg, serviceContext);
        });
        
        verify(tagLogicService, never()).updateTag(any(), anyList(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找标签分组成功的场景
     */
    @Test
    @DisplayName("测试查找标签分组成功")
    void testFindTagGroupsSuccess() {
        // Arrange
        FindTagGroups.Arg arg = new FindTagGroups.Arg();
        arg.setSourceInfo("tag_management");

        List<ITagDescribe> mockTagGroups = buildTagGroups();
        Map<String, Long> mockTagCountMap = Maps.newHashMap();
        mockTagCountMap.put(GROUP_ID, 5L);
        
        when(tagLogicService.findTagGroups(eq(TENANT_ID), eq(true)))
                .thenReturn(mockTagGroups);
        when(tagLogicService.findTagCountByGroup(eq(TENANT_ID)))
                .thenReturn(mockTagCountMap);
        when(describeLogicService.findDescribeListWithoutFields(eq(TENANT_ID), anyList()))
                .thenReturn(Lists.newArrayList());

        // Act
        FindTagGroups.Result result = tagService.findTagGroups(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGroups());
        assertEquals(1, result.getGroups().size());
        assertEquals(GROUP_ID, result.getGroups().get(0).getTagGroupId());
        assertEquals(5L, result.getGroups().get(0).getTagCount());
        
        verify(tagLogicService).findTagGroups(eq(TENANT_ID), eq(true));
        verify(tagLogicService).findTagCountByGroup(eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用标签成功的场景
     */
    @Test
    @DisplayName("测试禁用标签成功")
    void testDisableTagSuccess() {
        // Arrange
        CreateOrUpdateTag.Arg arg = new CreateOrUpdateTag.Arg();
        arg.setTagId(TAG_ID);
        arg.setGroupApiName("test_group__c");

        when(entityReferenceService.findByTargetTypeAndTargetValue(anyString(), anyString(), anyString(), any(EntityReferenceService.MatchType.class), anyInt()))
                .thenReturn(Lists.newArrayList());

        // Act
        CreateOrUpdateTag.Result result = tagService.disableTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagLogicService).disableTag(eq(TAG_ID), eq(user));
        verify(entityReferenceService, times(2)).findByTargetTypeAndTargetValue(anyString(), anyString(), anyString(), any(EntityReferenceService.MatchType.class), anyInt());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用标签时标签ID为空抛出异常
     */
    @Test
    @DisplayName("测试禁用标签时标签ID为空抛出异常")
    void testDisableTagThrowsValidateExceptionWhenTagIdBlank() {
        // Arrange
        CreateOrUpdateTag.Arg arg = new CreateOrUpdateTag.Arg();
        arg.setTagId(""); // 空的标签ID
        arg.setGroupApiName("test_group__c");

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            tagService.disableTag(arg, serviceContext);
        });
        
        verify(tagLogicService, never()).disableTag(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据分组ID查找标签成功的场景
     */
    @Test
    @DisplayName("测试根据分组ID查找标签成功")
    void testFindTagsByGroupIdSuccess() {
        // Arrange
        FindTagsByGroupId.Arg arg = new FindTagsByGroupId.Arg();
        arg.setTag_group_id(GROUP_ID);
        arg.setSourceInfo("tag_management");

        List<ISubTagDescribe> mockTags = buildTagDescribes();
        List<ITagDescribe> mockTagGroups = buildTagGroups();
        Map<String, String> mockUserMap = Maps.newHashMap();
        mockUserMap.put("creator123", "创建者");
        
        // 设置创建者
        mockTags.get(0).setCreatedBy("creator123");
        
        when(tagLogicService.findSubTagByQueryInfo(eq(TENANT_ID), eq(GROUP_ID), any(TagQueryInfo.class), eq(true)))
                .thenReturn(mockTags);
        when(orgService.getUserNameMapByIds(eq(TENANT_ID), eq(USER_ID), anyList()))
                .thenReturn(mockUserMap);
        when(tagLogicService.findTagGroupsByIds(eq(TENANT_ID), any(), eq(true)))
                .thenReturn(mockTagGroups);

        // Act
        FindTagsByGroupId.Result result = tagService.findTagsByGroupId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getTags());
        assertEquals(1, result.getTags().size());
        assertEquals("创建者", result.getTags().get(0).get(TagDocument.CREATE_USER__R));
        assertEquals("默认分组", result.getTags().get(0).get(TagDocument.TAG_GROUP_NAME));
        
        verify(tagLogicService).findSubTagByQueryInfo(eq(TENANT_ID), eq(GROUP_ID), any(TagQueryInfo.class), eq(true));
        verify(orgService).getUserNameMapByIds(eq(TENANT_ID), eq(USER_ID), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称查找标签分组成功的场景
     */
    @Test
    @DisplayName("测试根据名称查找标签分组成功")
    void testFindTagGroupByNameSuccess() {
        // Arrange
        FindTagGroupByName.Arg arg = new FindTagGroupByName.Arg();
        arg.setGroupApiName("test_group__c");

        ITagDescribe mockTagGroup = new TagDescribe();
        mockTagGroup.setId(GROUP_ID);
        mockTagGroup.setType("测试分组");
        mockTagGroup.setApiName("test_group__c");
        mockTagGroup.setTagRange(Lists.newArrayList(DESCRIBE_API_NAME));
        mockTagGroup.setIsAll(false);
        
        when(tagLogicService.findTagGroupByApiName(eq("test_group__c"), eq(user)))
                .thenReturn(mockTagGroup);

        // Act
        FindTagGroupByName.Result result = tagService.findTagGroupByName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals("测试分组", result.getName());
        assertEquals(GROUP_ID, result.getTagGroupId());
        assertEquals(Lists.newArrayList(DESCRIBE_API_NAME), result.getApiNameList());
        assertFalse(result.getIsAppliedToAll());
        
        verify(tagLogicService).findTagGroupByApiName(eq("test_group__c"), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称查找标签分组时分组不存在的场景
     */
    @Test
    @DisplayName("测试根据名称查找标签分组时分组不存在")
    void testFindTagGroupByNameNotFound() {
        // Arrange
        FindTagGroupByName.Arg arg = new FindTagGroupByName.Arg();
        arg.setGroupApiName("nonexistent_group__c");

        when(tagLogicService.findTagGroupByApiName(eq("nonexistent_group__c"), eq(user)))
                .thenReturn(null);

        // Act
        FindTagGroupByName.Result result = tagService.findTagGroupByName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getName());
        assertNull(result.getTagGroupId());
        
        verify(tagLogicService).findTagGroupByApiName(eq("nonexistent_group__c"), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量查询数据标签成功的场景
     */
    @Test
    @DisplayName("测试批量查询数据标签成功")
    void testFindAllTagByBulkDataIdSuccess() {
        // Arrange
        FindAllTagByBulkDataId.Arg arg = new FindAllTagByBulkDataId.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataIds(Sets.newHashSet(DATA_ID, "data456"));

        List<DataAndSubTag> mockDataAndTags = Lists.newArrayList();
        DataAndSubTag dataAndTag = new DataAndSubTag();
        dataAndTag.setDataId(DATA_ID);
        dataAndTag.setResultList(buildTagDescribes());
        mockDataAndTags.add(dataAndTag);

        when(tagLogicService.findAllTagByBulkDataId(eq(DESCRIBE_API_NAME), eq(Sets.newHashSet(DATA_ID, "data456")), eq(user)))
                .thenReturn(mockDataAndTags);

        // Act
        FindAllTagByBulkDataId.Result result = tagService.findAllTagByBulkDataId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        assertEquals(DATA_ID, result.getList().get(0).getDataId());
        
        verify(tagLogicService).findAllTagByBulkDataId(eq(DESCRIBE_API_NAME), eq(Sets.newHashSet(DATA_ID, "data456")), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查标签规则灰度配置的场景
     */
    @Test
    @DisplayName("测试检查标签规则灰度配置")
    void testHasTagRuleGray() {
        // 使用MockedStatic来模拟AppFrameworkConfig静态方法
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // Arrange
            mockedConfig.when(() -> AppFrameworkConfig.getIsSupportSystemTag(TENANT_ID))
                    .thenReturn(true);

            // Act
            Boolean result = tagService.hasTagRuleGray(serviceContext);

            // Assert
            assertTrue(result);
            mockedConfig.verify(() -> AppFrameworkConfig.getIsSupportSystemTag(TENANT_ID));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据适用对象查找系统标签分组成功的场景
     */
    @Test
    @DisplayName("测试根据适用对象查找系统标签分组成功")
    void testFindSystemTagGroupByApplyObjectSuccess() {
        // Arrange
        FindSystemTagGroupByApplyObject.Arg arg = new FindSystemTagGroupByApplyObject.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        List<ITagDescribe> mockSystemTagGroups = Lists.newArrayList();
        ITagDescribe systemGroup = new TagDescribe();
        systemGroup.setId(GROUP_ID);
        systemGroup.setType("系统标签组");
        systemGroup.setApiName("system_group__c");
        systemGroup.setTagDefineType("system");
        systemGroup.setIsActive(true);
        mockSystemTagGroups.add(systemGroup);
        
        List<ISubTagDescribe> mockSubTags = buildTagDescribes();
        mockSubTags.get(0).setIsActive(true);
        
        Map<String, List<ISubTagDescribe>> mockGroupSubTagMap = Maps.newHashMap();
        mockGroupSubTagMap.put(GROUP_ID, mockSubTags);
        
        when(tagLogicService.findAllTagGroupByObjectApiNameInObjRange(eq(DESCRIBE_API_NAME), eq(TENANT_ID)))
                .thenReturn(mockSystemTagGroups);
        when(tagLogicService.bulkFindSubTagByTagIds(eq(serviceContext), eq(Lists.newArrayList(GROUP_ID))))
                .thenReturn(mockGroupSubTagMap);

        // Act
        FindSystemTagGroupByApplyObject.Result result = tagService.findSystemTagGroupByApplyObject(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(1, result.getResult().size());
        assertEquals(GROUP_ID, result.getResult().get(0).getTagGroupId());
        assertEquals("系统标签组", result.getResult().get(0).getTagGroupName());
        assertEquals(1, result.getResult().get(0).getTagList().size());
        
        verify(tagLogicService).findAllTagGroupByObjectApiNameInObjRange(eq(DESCRIBE_API_NAME), eq(TENANT_ID));
        verify(tagLogicService).bulkFindSubTagByTagIds(eq(serviceContext), eq(Lists.newArrayList(GROUP_ID)));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量数据打标签成功的场景
     */
    @Test
    @DisplayName("测试批量数据打标签成功")
    void testBulkDataTagSuccess() {
        // Arrange
        BulkDataTag.Arg arg = new BulkDataTag.Arg();
        Map<String, List<String>> dataIdToTagId = Maps.newHashMap();
        dataIdToTagId.put(DATA_ID, Lists.newArrayList(TAG_ID));
        arg.setDataIdToTagId(dataIdToTagId);
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        Map<String, List<String>> mockResult = Maps.newHashMap();
        mockResult.put(DATA_ID, Lists.newArrayList(TAG_ID));
        
        when(tagLogicService.multiDataBatchBoundTags(eq(dataIdToTagId), eq(DESCRIBE_API_NAME), eq(user)))
                .thenReturn(mockResult);

        // Act
        Map<String, List<String>> result = tagService.bulkDataTag(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(DATA_ID));
        assertEquals(Lists.newArrayList(TAG_ID), result.get(DATA_ID));
        
        verify(tagLogicService).multiDataBatchBoundTags(eq(dataIdToTagId), eq(DESCRIBE_API_NAME), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量数据打标签时参数为空返回空结果的场景
     */
    @Test
    @DisplayName("测试批量数据打标签时参数为空返回空结果")
    void testBulkDataTagEmptyParams() {
        // Arrange
        BulkDataTag.Arg arg = new BulkDataTag.Arg();
        arg.setDataIdToTagId(Maps.newHashMap()); // 空的数据
        arg.setDescribeApiName(""); // 空的对象名

        // Act
        Map<String, List<String>> result = tagService.bulkDataTag(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(tagLogicService, never()).multiDataBatchBoundTags(any(), any(), any());
    }

    // Helper methods for building test data

    private List<ISubTagDescribe> buildTagDescribes() {
        ISubTagDescribe tag = new SubTagDescribe();
        tag.setName("tag1");
        tag.setDescribeApiName(DESCRIBE_API_NAME);
        tag.setGrade(1);
        tag.setId("123");
        tag.setTagId(GROUP_ID);
        return Lists.newArrayList(tag);
    }

    private List<ITagDescribe> buildTagGroups() {
        ITagDescribe group = new TagDescribe();
        group.setId(GROUP_ID);
        group.setDescribeApiName(DESCRIBE_API_NAME);
        group.setType("默认分组");
        return Lists.newArrayList(group);
    }
}
