package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.scene.*;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.config.IUdefConfig;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.ISystemScene;
import com.facishare.paas.appframework.metadata.dto.scene.ITenantScene;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.common.MetadataContext;
import com.facishare.paas.metadata.impl.search.OrderBy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectTenantSceneService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectTenantSceneService单元测试")
class ObjectTenantSceneServiceTest {

    @Mock
    private SceneLogicService sceneLogicService;
    
    @Mock
    private MetaDataService metaDataService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @InjectMocks
    private ObjectTenantSceneService service;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";
    private final String SCENE_API_NAME = "TestScene";
    private final String EXTEND_ATTRIBUTE = "list";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        serviceContext = mock(ServiceContext.class);
        when(serviceContext.getUser()).thenReturn(user);
        when(serviceContext.getTenantId()).thenReturn(TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findScenes方法 - 查找场景列表
     */
    @Test
    @DisplayName("测试findScenes方法 - 查找场景列表")
    void testFindScenes_Success() {
        // Arrange
        FindSceneList.Arg arg = new FindSceneList.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);

        // Mock scenes
        IScene scene1 = mock(IScene.class);
        IScene scene2 = mock(IScene.class);
        List<IScene> scenes = Arrays.asList(scene1, scene2);
        List<IScene> baseScenes = Arrays.asList(scene1);
        List<IUdefConfig> sceneConfigs = Collections.emptyList();

        when(sceneLogicService.findScenes(DESCRIBE_API_NAME, user, EXTEND_ATTRIBUTE))
                .thenReturn(scenes);
        when(sceneLogicService.findBaseScenes(DESCRIBE_API_NAME, EXTEND_ATTRIBUTE, user))
                .thenReturn(baseScenes);
        when(sceneLogicService.findSceneConfigList(user, DESCRIBE_API_NAME, scenes))
                .thenReturn(sceneConfigs);

        // Act
        FindSceneList.Result result = service.findScenes(arg, serviceContext);

        // Assert
        assertNotNull(result);
        
        // 验证调用
        verify(sceneLogicService).findScenes(DESCRIBE_API_NAME, user, EXTEND_ATTRIBUTE);
        verify(sceneLogicService).findBaseScenes(DESCRIBE_API_NAME, EXTEND_ATTRIBUTE, user);
        verify(sceneLogicService).findSceneConfigList(user, DESCRIBE_API_NAME, scenes);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findScenes方法 - 查找单个场景信息
     */
    @Test
    @DisplayName("测试findScenes方法 - 查找单个场景信息")
    void testFindScenesInfo_Success() {
        // Arrange
        FindSceneInfo.Arg arg = new FindSceneInfo.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setSceneApiName(SCENE_API_NAME);
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);
        arg.setAppId("app123");

        IScene mockScene = mock(IScene.class);
        when(sceneLogicService.findSceneByApiName(user, DESCRIBE_API_NAME, SCENE_API_NAME, EXTEND_ATTRIBUTE, "app123"))
                .thenReturn(mockScene);

        // Act
        FindSceneInfo.Result result = service.findScenes(arg, serviceContext);

        // Assert
        assertNotNull(result);
        
        // 验证调用
        verify(sceneLogicService).findSceneByApiName(user, DESCRIBE_API_NAME, SCENE_API_NAME, EXTEND_ATTRIBUTE, "app123");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createTenantScene方法 - 正常场景
     */
    @Test
    @DisplayName("测试createTenantScene方法 - 正常场景")
    void testCreateTenantScene_Success() {
        // Arrange
        ObjectTenantScene.Arg arg = new ObjectTenantScene.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);
        
        SceneDTO sceneDTO = new SceneDTO();
        sceneDTO.setApiName(SCENE_API_NAME);
        sceneDTO.setDisplayName("Test Scene");
        sceneDTO.setObjectDescribeApiName(DESCRIBE_API_NAME);
        sceneDTO.setExtendAttribute(EXTEND_ATTRIBUTE);
        arg.setScene(sceneDTO);

        ITenantScene mockTenantScene = mock(ITenantScene.class);
        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        
        when(sceneLogicService.createTenantScene(sceneDTO, DESCRIBE_API_NAME, EXTEND_ATTRIBUTE, user))
                .thenReturn(mockTenantScene);
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(mockDescribe);
        when(mockDescribe.getDisplayName()).thenReturn("Test Object");

        // Act
        ObjectTenantScene.Result result = service.createTenantScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockTenantScene, result.getScene());
        
        // 验证调用
        verify(sceneLogicService).createTenantScene(sceneDTO, DESCRIBE_API_NAME, EXTEND_ATTRIBUTE, user);
        verify(describeLogicService).findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
        verify(sceneLogicService).replaceOtherScenes(eq(mockDescribe), eq(sceneDTO), eq(EXTEND_ATTRIBUTE), isNull(), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createTenantScene方法 - 场景为null
     */
    @Test
    @DisplayName("测试createTenantScene方法 - 场景为null")
    void testCreateTenantScene_NullScene() {
        // Arrange
        ObjectTenantScene.Arg arg = new ObjectTenantScene.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setScene(null);

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            service.createTenantScene(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateTenantScene方法 - 租户场景更新
     */
    @Test
    @DisplayName("测试updateTenantScene方法 - 租户场景更新")
    void testUpdateTenantScene_TenantScene() {
        // Arrange
        ObjectTenantScene.Arg arg = new ObjectTenantScene.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);
        
        SceneDTO sceneDTO = new SceneDTO();
        sceneDTO.setApiName(SCENE_API_NAME);
        sceneDTO.setDisplayName("Updated Scene");
        sceneDTO.setType("tenant");
        sceneDTO.setObjectDescribeApiName(DESCRIBE_API_NAME);
        sceneDTO.setClearCustomConfig(false);
        arg.setScene(sceneDTO);

        ITenantScene mockTenantScene = mock(ITenantScene.class);
        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);

        when(sceneLogicService.updateTenantScene(any(ITenantScene.class), eq(DESCRIBE_API_NAME), eq(user)))
                .thenReturn(mockTenantScene);
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(mockDescribe);
        when(mockDescribe.getDisplayName()).thenReturn("Test Object");

        // Act
        ObjectTenantScene.Result result = service.updateTenantScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockTenantScene, result.getScene());
        
        // 验证调用
        verify(sceneLogicService).updateTenantScene(sceneDTO, DESCRIBE_API_NAME, user);
        verify(describeLogicService).findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateTenantScene方法 - 系统场景更新
     */
    @Test
    @DisplayName("测试updateTenantScene方法 - 系统场景更新")
    void testUpdateTenantScene_SystemScene() {
        // Arrange
        ObjectTenantScene.Arg arg = new ObjectTenantScene.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);
        
        SceneDTO sceneDTO = new SceneDTO();
        sceneDTO.setApiName(SCENE_API_NAME);
        sceneDTO.setDisplayName("Updated System Scene");
        sceneDTO.setType("default");
        sceneDTO.setObjectDescribeApiName(DESCRIBE_API_NAME);
        arg.setScene(sceneDTO);

        ISystemScene mockSystemScene = mock(ISystemScene.class);
        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);

        when(sceneLogicService.updateSystemScene(any(ISystemScene.class), eq(user)))
                .thenReturn(mockSystemScene);
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(mockDescribe);
        when(mockDescribe.getDisplayName()).thenReturn("Test Object");

        // Act
        ObjectTenantScene.Result result = service.updateTenantScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockSystemScene, result.getScene());
        
        // 验证调用
        verify(sceneLogicService).updateSystemScene(sceneDTO, user);
        verify(describeLogicService).findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updatedDefaultOrders方法
     */
    @Test
    @DisplayName("测试updatedDefaultOrders方法")
    void testUpdatedDefaultOrders_Success() {
        // Arrange
        DefaultSceneOrders.Arg arg = new DefaultSceneOrders.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setSceneApiName(SCENE_API_NAME);
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);
        OrderBy orderBy = new OrderBy("field1", true);
        OrderByExt orderByExt = OrderByExt.of(orderBy);
        arg.setOrders(orderByExt);

        ISystemScene mockSystemScene = mock(ISystemScene.class);
        when(sceneLogicService.updatedDefaultOrders(arg.getOrders(), DESCRIBE_API_NAME, SCENE_API_NAME, EXTEND_ATTRIBUTE, user))
                .thenReturn(mockSystemScene);

        // Act
        DefaultSceneOrders.Result result = service.updatedDefaultOrders(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockSystemScene, result.getScene());
        
        // 验证调用
        verify(sceneLogicService).updatedDefaultOrders(arg.getOrders(), DESCRIBE_API_NAME, SCENE_API_NAME, EXTEND_ATTRIBUTE, user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enableScene方法
     */
    @Test
    @DisplayName("测试enableScene方法")
    void testEnableScene_Success() {
        // Arrange
        ChangeSceneStatus.Arg arg = new ChangeSceneStatus.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setSceneApiName(SCENE_API_NAME);
        arg.setType("TENANT_SCENE");
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);

        // Act
        ChangeSceneStatus.Result result = service.enableScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证调用
        verify(sceneLogicService).enableScene(DESCRIBE_API_NAME, SCENE_API_NAME, "TENANT_SCENE", EXTEND_ATTRIBUTE, user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试disableScene方法
     */
    @Test
    @DisplayName("测试disableScene方法")
    void testDisableScene_Success() {
        // Arrange
        ChangeSceneStatus.Arg arg = new ChangeSceneStatus.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setSceneApiName(SCENE_API_NAME);
        arg.setType("TENANT_SCENE");
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);

        // Act
        ChangeSceneStatus.Result result = service.disableScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证调用
        verify(sceneLogicService).disableScene(DESCRIBE_API_NAME, SCENE_API_NAME, "TENANT_SCENE", EXTEND_ATTRIBUTE, user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteScene方法
     */
    @Test
    @DisplayName("测试deleteScene方法")
    void testDeleteScene_Success() {
        // Arrange
        ChangeSceneStatus.Arg arg = new ChangeSceneStatus.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setSceneApiName(SCENE_API_NAME);
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);

        // Act
        ChangeSceneStatus.Result result = service.deleteScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证调用
        verify(sceneLogicService).deleteTenantScene(DESCRIBE_API_NAME, SCENE_API_NAME, EXTEND_ATTRIBUTE, user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateTenantSceneCount方法
     */
    @Test
    @DisplayName("测试validateTenantSceneCount方法")
    void testValidateTenantSceneCount_Success() {
        // Arrange
        ValidateTenantSceneCount.Arg arg = new ValidateTenantSceneCount.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);

        // Act
        ValidateTenantSceneCount.Result result = service.validateTenantSceneCount(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证调用
        verify(sceneLogicService).validateTenantSceneSum(DESCRIBE_API_NAME, TENANT_ID, EXTEND_ATTRIBUTE);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateSceneName方法
     */
    @Test
    @DisplayName("测试validateSceneName方法")
    void testValidateSceneName_Success() {
        // Arrange
        ValidateSceneName.Arg arg = new ValidateSceneName.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setSceneApiName(SCENE_API_NAME);
        arg.setDisplayName("Test Scene Name");
        arg.setId("scene123");
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);

        SceneLogicServiceImpl.ValidateResult mockValidateResult = mock(SceneLogicServiceImpl.ValidateResult.class);
        when(mockValidateResult.isApiName()).thenReturn(false);
        when(mockValidateResult.isLabel()).thenReturn(false);

        when(sceneLogicService.validateApiNameAndLabel(DESCRIBE_API_NAME, SCENE_API_NAME, "Test Scene Name", "scene123", TENANT_ID, EXTEND_ATTRIBUTE))
                .thenReturn(mockValidateResult);

        // Act
        ValidateSceneName.Result result = service.validateSceneName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.getApiNameRepeat());
        assertFalse(result.getDisplayNameRepeat());

        // 验证调用
        verify(sceneLogicService).validateApiNameAndLabel(DESCRIBE_API_NAME, SCENE_API_NAME, "Test Scene Name", "scene123", TENANT_ID, EXTEND_ATTRIBUTE);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试initSystemScene方法
     */
    @Test
    @DisplayName("测试initSystemScene方法")
    void testInitSystemScene_Success() {
        // Arrange
        InitSystemScene.Arg arg = new InitSystemScene.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);
        arg.setOption("option1");
        arg.setObjectType("objectType1");
        arg.setSceneApiName(SCENE_API_NAME);
        arg.setTenantId(TENANT_ID);

        // Act
        InitSystemScene.Result result = service.initSystemScene(arg, serviceContext);

        // Assert
        assertNotNull(result);

        // 验证调用
        verify(sceneLogicService).initSystemScene(eq(DESCRIBE_API_NAME), eq(EXTEND_ATTRIBUTE), eq(user), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setDefaultScenePriority方法
     */
    @Test
    @DisplayName("测试setDefaultScenePriority方法")
    void testSetDefaultScenePriority_Success() {
        // Arrange
        DefaultScenePriority.Arg arg = new DefaultScenePriority.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setSceneApiNames(Arrays.asList("scene1", "scene2", "scene3"));
        arg.setExtendAttribute(EXTEND_ATTRIBUTE);

        // Act
        DefaultScenePriority.Result result = service.setDefaultScenePriority(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证调用
        verify(sceneLogicService).setUpDefaultScenePriority(DESCRIBE_API_NAME, Arrays.asList("scene1", "scene2", "scene3"), EXTEND_ATTRIBUTE, user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findSceneConfig方法
     */
    @Test
    @DisplayName("测试findSceneConfig方法")
    void testFindSceneConfig_Success() {
        // Arrange
        FindSceneConfig.Arg arg = new FindSceneConfig.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setSceneApiName(SCENE_API_NAME);

        IUdefConfig mockConfig = mock(IUdefConfig.class);
        when(sceneLogicService.findSceneConfig(DESCRIBE_API_NAME, SCENE_API_NAME, user))
                .thenReturn(mockConfig);

        // Act
        FindSceneConfig.Result result = service.findSceneConfig(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSceneConfig());

        // 验证调用
        verify(sceneLogicService).findSceneConfig(DESCRIBE_API_NAME, SCENE_API_NAME, user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试supportOrFilter方法
     */
    @Test
    @DisplayName("测试supportOrFilter方法")
    void testSupportOrFilter_Success() {
        // Arrange
        SupportOrFilter.Arg arg = new SupportOrFilter.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        when(metaDataService.isSupportOrFilter(TENANT_ID, DESCRIBE_API_NAME))
                .thenReturn(true);

        // Act
        SupportOrFilter.Result result = service.supportOrFilter(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSupportOrFilter());

        // 验证调用
        verify(metaDataService).isSupportOrFilter(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(sceneLogicService);
        assertNotNull(metaDataService);
        assertNotNull(describeLogicService);
    }
}
