package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.aggregate.GetAggregateValues;
import com.facishare.paas.appframework.metadata.AggregateRule;
import com.facishare.paas.appframework.metadata.AggregateValueLogicService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectAggregateService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectAggregateService单元测试")
class ObjectAggregateServiceTest {

    @Mock
    private AggregateValueLogicService aggregateValueLogicService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @InjectMocks
    private ObjectAggregateService objectAggregateService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String RULE_ID = "rule_123";
    private final String OBJECT_API_NAME = "test_object__c";
    private final String FIELD_API_NAME = "amount__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "aggregate", "getAggregateValues");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取聚合值成功的正常场景
     */
    @Test
    @DisplayName("测试获取聚合值成功")
    void testGetAggregateValuesSuccess() {
        // Arrange
        GetAggregateValues.Arg arg = new GetAggregateValues.Arg();
        arg.setAggregateRuleIds(Arrays.asList(RULE_ID));
        
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("dimension_field", "test_value");
        arg.setData(data);

        // Mock AggregateRule
        AggregateRule mockRule = mock(AggregateRule.class);
        when(mockRule.getId()).thenReturn(RULE_ID);
        when(mockRule.getAggregateObject()).thenReturn(OBJECT_API_NAME);

        when(mockRule.getAggregateWay()).thenReturn("sum");
        when(mockRule.getDateRangeWithTimeMillis()).thenReturn(Arrays.asList(1000L, 2000L));
        when(mockRule.parseDimension()).thenReturn(new String[]{"dimension_field"});


        // Mock IObjectDescribe
        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        


        // Mock service calls
        when(aggregateValueLogicService.findAggregateRuleByIds(TENANT_ID, Arrays.asList(RULE_ID)))
                .thenReturn(Arrays.asList(mockRule));
        
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(OBJECT_API_NAME, mockDescribe);
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), anySet()))
                .thenReturn(describeMap);

        BigDecimal aggregateValue = new BigDecimal("100.50");
        when(aggregateValueLogicService.getAggregateValue(eq(TENANT_ID), eq(RULE_ID), 
                eq("test_value"), eq(1000L), eq(2000L), eq("sum"), eq(2), eq(RoundingMode.HALF_UP)))
                .thenReturn(aggregateValue);

        // Act
        GetAggregateValues.Result result = objectAggregateService.getAggregateValues(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAggregateValues());
        assertEquals("100.50", result.getAggregateValues().get(RULE_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试聚合值为null时的处理（非最大最小值方式）
     */
    @Test
    @DisplayName("测试聚合值为null - 非最大最小值方式")
    void testGetAggregateValuesWithNullValueNonMaxMin() {
        // Arrange
        GetAggregateValues.Arg arg = new GetAggregateValues.Arg();
        arg.setAggregateRuleIds(Arrays.asList(RULE_ID));
        
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("dimension_field", "test_value");
        arg.setData(data);

        // Mock AggregateRule
        AggregateRule mockRule = mock(AggregateRule.class);
        when(mockRule.getId()).thenReturn(RULE_ID);
        when(mockRule.getAggregateObject()).thenReturn(OBJECT_API_NAME);
        when(mockRule.getAggregateField()).thenReturn(FIELD_API_NAME);
        when(mockRule.getAggregateWay()).thenReturn("sum");
        when(mockRule.getDateRangeWithTimeMillis()).thenReturn(Arrays.asList(1000L, 2000L));
        when(mockRule.parseDimension()).thenReturn(new String[]{"dimension_field"});
        when(mockRule.isMaxOrMinWay()).thenReturn(false);

        // Mock service calls
        when(aggregateValueLogicService.findAggregateRuleByIds(TENANT_ID, Arrays.asList(RULE_ID)))
                .thenReturn(Arrays.asList(mockRule));
        
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(OBJECT_API_NAME, mock(IObjectDescribe.class));
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), anySet()))
                .thenReturn(describeMap);

        // Return null aggregate value
        when(aggregateValueLogicService.getAggregateValue(eq(TENANT_ID), eq(RULE_ID), 
                eq("test_value"), eq(1000L), eq(2000L), eq("sum"), eq(2), eq(RoundingMode.HALF_UP)))
                .thenReturn(null);

        // Act
        GetAggregateValues.Result result = objectAggregateService.getAggregateValues(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAggregateValues());
        assertEquals("0", result.getAggregateValues().get(RULE_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试聚合值为null时的处理（最大最小值方式）
     */
    @Test
    @DisplayName("测试聚合值为null - 最大最小值方式")
    void testGetAggregateValuesWithNullValueMaxMin() {
        // Arrange
        GetAggregateValues.Arg arg = new GetAggregateValues.Arg();
        arg.setAggregateRuleIds(Arrays.asList(RULE_ID));
        
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("dimension_field", "test_value");
        arg.setData(data);

        // Mock AggregateRule
        AggregateRule mockRule = mock(AggregateRule.class);
        when(mockRule.getId()).thenReturn(RULE_ID);
        when(mockRule.getAggregateObject()).thenReturn(OBJECT_API_NAME);
        when(mockRule.getAggregateField()).thenReturn(FIELD_API_NAME);
        when(mockRule.getAggregateWay()).thenReturn("max");
        when(mockRule.getDateRangeWithTimeMillis()).thenReturn(Arrays.asList(1000L, 2000L));
        when(mockRule.parseDimension()).thenReturn(new String[]{"dimension_field"});
        when(mockRule.isMaxOrMinWay()).thenReturn(true);

        // Mock service calls
        when(aggregateValueLogicService.findAggregateRuleByIds(TENANT_ID, Arrays.asList(RULE_ID)))
                .thenReturn(Arrays.asList(mockRule));
        
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(OBJECT_API_NAME, mock(IObjectDescribe.class));
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), anySet()))
                .thenReturn(describeMap);

        // Return null aggregate value
        when(aggregateValueLogicService.getAggregateValue(eq(TENANT_ID), eq(RULE_ID), 
                eq("test_value"), eq(1000L), eq(2000L), eq("max"), eq(2), eq(RoundingMode.HALF_UP)))
                .thenReturn(null);

        // Act
        GetAggregateValues.Result result = objectAggregateService.getAggregateValues(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAggregateValues());
        assertNull(result.getAggregateValues().get(RULE_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空规则列表的边界条件
     */
    @Test
    @DisplayName("测试空规则列表")
    void testGetAggregateValuesWithEmptyRules() {
        // Arrange
        GetAggregateValues.Arg arg = new GetAggregateValues.Arg();
        arg.setAggregateRuleIds(Arrays.asList(RULE_ID));
        
        ObjectDataDocument data = new ObjectDataDocument();
        arg.setData(data);

        // Mock empty rule list
        when(aggregateValueLogicService.findAggregateRuleByIds(TENANT_ID, Arrays.asList(RULE_ID)))
                .thenReturn(Lists.newArrayList());
        
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), anySet()))
                .thenReturn(Maps.newHashMap());

        // Act
        GetAggregateValues.Result result = objectAggregateService.getAggregateValues(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAggregateValues());
        assertTrue(result.getAggregateValues().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象描述为null的边界条件
     */
    @Test
    @DisplayName("测试对象描述为null")
    void testGetAggregateValuesWithNullDescribe() {
        // Arrange
        GetAggregateValues.Arg arg = new GetAggregateValues.Arg();
        arg.setAggregateRuleIds(Arrays.asList(RULE_ID));

        ObjectDataDocument data = new ObjectDataDocument();
        data.put("dimension_field", "test_value");
        arg.setData(data);

        // Mock AggregateRule
        AggregateRule mockRule = mock(AggregateRule.class);
        when(mockRule.getId()).thenReturn(RULE_ID);
        when(mockRule.getAggregateObject()).thenReturn(OBJECT_API_NAME);
        when(mockRule.getDateRangeWithTimeMillis()).thenReturn(Arrays.asList(1000L, 2000L));
        when(mockRule.parseDimension()).thenReturn(new String[]{"dimension_field"});
        when(mockRule.getAggregateWay()).thenReturn("sum");

        // Mock service calls
        when(aggregateValueLogicService.findAggregateRuleByIds(TENANT_ID, Arrays.asList(RULE_ID)))
                .thenReturn(Arrays.asList(mockRule));

        // Return empty describe map (null describe)
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), anySet()))
                .thenReturn(Maps.newHashMap());

        // Act
        GetAggregateValues.Result result = objectAggregateService.getAggregateValues(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAggregateValues());
        assertFalse(result.getAggregateValues().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试聚合字段为空的边界条件
     */
    @Test
    @DisplayName("测试聚合字段为空")
    void testGetAggregateValuesWithEmptyAggregateField() {
        // Arrange
        GetAggregateValues.Arg arg = new GetAggregateValues.Arg();
        arg.setAggregateRuleIds(Arrays.asList(RULE_ID));

        ObjectDataDocument data = new ObjectDataDocument();
        data.put("dimension_field", "test_value");
        arg.setData(data);

        // Mock AggregateRule with empty aggregate field
        AggregateRule mockRule = mock(AggregateRule.class);
        when(mockRule.getId()).thenReturn(RULE_ID);
        when(mockRule.getAggregateObject()).thenReturn(OBJECT_API_NAME);
        when(mockRule.getDateRangeWithTimeMillis()).thenReturn(Arrays.asList(1000L, 2000L));
        when(mockRule.parseDimension()).thenReturn(new String[]{"dimension_field"});
        when(mockRule.getAggregateWay()).thenReturn("count");

        // Mock IObjectDescribe
        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);

        // Mock service calls
        when(aggregateValueLogicService.findAggregateRuleByIds(TENANT_ID, Arrays.asList(RULE_ID)))
                .thenReturn(Arrays.asList(mockRule));

        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(OBJECT_API_NAME, mockDescribe);
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), anySet()))
                .thenReturn(describeMap);

        // Act
        GetAggregateValues.Result result = objectAggregateService.getAggregateValues(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAggregateValues());
        assertFalse(result.getAggregateValues().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多维度字段解析（包含点分隔符）
     */
    @Test
    @DisplayName("测试多维度字段解析")
    void testGetAggregateValuesWithMultiDimensionField() {
        // Arrange
        GetAggregateValues.Arg arg = new GetAggregateValues.Arg();
        arg.setAggregateRuleIds(Arrays.asList(RULE_ID));

        ObjectDataDocument data = new ObjectDataDocument();
        data.put("lookup_field", "test_value");
        arg.setData(data);

        // Mock AggregateRule with multi-dimension field
        AggregateRule mockRule = mock(AggregateRule.class);
        when(mockRule.getId()).thenReturn(RULE_ID);
        when(mockRule.getAggregateObject()).thenReturn(OBJECT_API_NAME);
        when(mockRule.getDateRangeWithTimeMillis()).thenReturn(Arrays.asList(1000L, 2000L));
        when(mockRule.parseDimension()).thenReturn(new String[]{"parent_field", "lookup_field"});
        when(mockRule.getAggregateWay()).thenReturn("sum");

        // Mock service calls
        when(aggregateValueLogicService.findAggregateRuleByIds(TENANT_ID, Arrays.asList(RULE_ID)))
                .thenReturn(Arrays.asList(mockRule));

        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(OBJECT_API_NAME, mock(IObjectDescribe.class));
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), anySet()))
                .thenReturn(describeMap);

        // Act
        GetAggregateValues.Result result = objectAggregateService.getAggregateValues(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAggregateValues());
        assertFalse(result.getAggregateValues().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Arrange & Act & Assert
        assertNotNull(objectAggregateService);
        assertNotNull(aggregateValueLogicService);
        assertNotNull(describeLogicService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证Mock对象的交互行为
     */
    @Test
    @DisplayName("验证Mock对象交互行为")
    void testMockInteractions() {
        // Arrange
        GetAggregateValues.Arg arg = new GetAggregateValues.Arg();
        arg.setAggregateRuleIds(Arrays.asList(RULE_ID));

        ObjectDataDocument data = new ObjectDataDocument();
        data.put("dimension_field", "test_value");
        arg.setData(data);

        // Mock AggregateRule
        AggregateRule mockRule = mock(AggregateRule.class);
        when(mockRule.getId()).thenReturn(RULE_ID);
        when(mockRule.getAggregateObject()).thenReturn(OBJECT_API_NAME);
        when(mockRule.getAggregateWay()).thenReturn("sum");
        when(mockRule.getDateRangeWithTimeMillis()).thenReturn(Arrays.asList(1000L, 2000L));
        when(mockRule.parseDimension()).thenReturn(new String[]{"dimension_field"});

        when(aggregateValueLogicService.findAggregateRuleByIds(TENANT_ID, Arrays.asList(RULE_ID)))
                .thenReturn(Arrays.asList(mockRule));

        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), anySet()))
                .thenReturn(Maps.newHashMap());

        when(aggregateValueLogicService.getAggregateValue(anyString(), anyString(), anyString(), anyLong(), anyLong(), anyString(), anyInt(), any(RoundingMode.class)))
                .thenReturn(new BigDecimal("100"));

        // Act
        objectAggregateService.getAggregateValues(arg, serviceContext);

        // Assert - Verify interactions
        verify(aggregateValueLogicService).findAggregateRuleByIds(TENANT_ID, Arrays.asList(RULE_ID));
        verify(describeLogicService).findObjectsWithoutCopy(eq(TENANT_ID), anySet());
        verify(aggregateValueLogicService).getAggregateValue(eq(TENANT_ID), eq(RULE_ID),
                eq("test_value"), eq(1000L), eq(2000L), eq("sum"), eq(2), eq(RoundingMode.HALF_UP));

        // Verify rule method calls
        verify(mockRule, times(2)).getId(); // Called twice in the lambda
        verify(mockRule, times(2)).getAggregateObject(); // Called twice: once for stream, once for lambda
        verify(mockRule).getAggregateWay();
        verify(mockRule).getDateRangeWithTimeMillis();
        verify(mockRule).parseDimension();
    }
}
