package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.predef.service.dto.handler.EnableOrDisableHandler;
import com.facishare.paas.appframework.core.predef.service.dto.handler.UpsertHandlerDefinitionAndRuntimeConfig;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.metadata.handler.HandlerRuntimeConfigUniqueKey;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectHandlerService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectHandlerService单元测试")
class ObjectHandlerServiceTest {

    @Mock
    private HandlerLogicService handlerLogicService;
    
    @InjectMocks
    private ObjectHandlerService objectHandlerService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String UNIQUE_KEY = "testHandler";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "handler", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量更新处理器定义和运行时配置成功的场景
     */
    @Test
    @DisplayName("测试upsertHandlerDefinitionAndRuntimeConfig成功")
    void testUpsertHandlerDefinitionAndRuntimeConfigSuccess() {
        // Arrange
        UpsertHandlerDefinitionAndRuntimeConfig.Arg arg = new UpsertHandlerDefinitionAndRuntimeConfig.Arg();
        // 使用null来简化测试，避免复杂的类型构造
        arg.setDefinitions(null);
        arg.setRuntimeConfigs(null);

        // Act
        UpsertHandlerDefinitionAndRuntimeConfig.Result result = objectHandlerService
                .upsertHandlerDefinitionAndRuntimeConfig(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(handlerLogicService).batchUpsertHandlerDefinitionAndRuntimeConfig(
                eq(user), eq(null), eq(null));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用处理器成功的场景
     */
    @Test
    @DisplayName("测试enableHandler成功")
    void testEnableHandlerSuccess() {
        // Arrange
        EnableOrDisableHandler.Arg arg = new EnableOrDisableHandler.Arg();
        HandlerRuntimeConfigUniqueKey uniqueKey = mock(HandlerRuntimeConfigUniqueKey.class);
        arg.setUniqueKey(uniqueKey);

        // Act
        EnableOrDisableHandler.Result result = objectHandlerService.enableHandler(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(handlerLogicService).enableHandler(user, uniqueKey);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用处理器成功的场景
     */
    @Test
    @DisplayName("测试disableHandler成功")
    void testDisableHandlerSuccess() {
        // Arrange
        EnableOrDisableHandler.Arg arg = new EnableOrDisableHandler.Arg();
        HandlerRuntimeConfigUniqueKey uniqueKey = mock(HandlerRuntimeConfigUniqueKey.class);
        arg.setUniqueKey(uniqueKey);

        // Act
        EnableOrDisableHandler.Result result = objectHandlerService.disableHandler(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(handlerLogicService).disableHandler(user, uniqueKey);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量更新处理器定义和运行时配置时参数为空的场景
     */
    @Test
    @DisplayName("测试upsertHandlerDefinitionAndRuntimeConfig参数为空")
    void testUpsertHandlerDefinitionAndRuntimeConfigWithNullArgs() {
        // Arrange
        UpsertHandlerDefinitionAndRuntimeConfig.Arg arg = new UpsertHandlerDefinitionAndRuntimeConfig.Arg();
        arg.setDefinitions(null);
        arg.setRuntimeConfigs(null);

        // Act
        UpsertHandlerDefinitionAndRuntimeConfig.Result result = objectHandlerService
                .upsertHandlerDefinitionAndRuntimeConfig(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(handlerLogicService).batchUpsertHandlerDefinitionAndRuntimeConfig(
                eq(user), eq(null), eq(null));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用处理器时唯一键为null的场景
     */
    @Test
    @DisplayName("测试enableHandler唯一键为null")
    void testEnableHandlerWithNullUniqueKey() {
        // Arrange
        EnableOrDisableHandler.Arg arg = new EnableOrDisableHandler.Arg();
        arg.setUniqueKey(null);

        // Act
        EnableOrDisableHandler.Result result = objectHandlerService.enableHandler(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(handlerLogicService).enableHandler(user, null);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用处理器时唯一键为null的场景
     */
    @Test
    @DisplayName("测试disableHandler唯一键为null")
    void testDisableHandlerWithNullUniqueKey() {
        // Arrange
        EnableOrDisableHandler.Arg arg = new EnableOrDisableHandler.Arg();
        arg.setUniqueKey(null);

        // Act
        EnableOrDisableHandler.Result result = objectHandlerService.disableHandler(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(handlerLogicService).disableHandler(user, null);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectHandlerService);
        assertNotNull(handlerLogicService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("handler", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Assert
        assertNotNull(user);
        assertEquals(TENANT_ID, user.getTenantId());
        assertEquals(USER_ID, user.getUserId());
    }
}
