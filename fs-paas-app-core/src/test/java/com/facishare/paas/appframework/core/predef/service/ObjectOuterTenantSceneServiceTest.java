package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;

import com.facishare.paas.appframework.core.predef.service.dto.scene.*;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.SceneLogicService;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.ISystemScene;
import com.facishare.paas.appframework.metadata.dto.scene.ITenantScene;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectOuterTenantSceneService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectOuterTenantSceneService单元测试")
class ObjectOuterTenantSceneServiceTest {

    @Mock
    private SceneLogicService sceneLogicService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private ServiceContext serviceContext;

    @InjectMocks
    private ObjectOuterTenantSceneService service;

    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        when(serviceContext.getUser()).thenReturn(user);
        when(serviceContext.getTenantId()).thenReturn(TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findScenes方法查找场景列表
     */
    @Test
    @DisplayName("测试findScenes方法 - 查找场景列表")
    void testFindScenes_SceneList() {
        // Arrange
        FindSceneList.Arg arg = new FindSceneList.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        IScene mockScene = mock(IScene.class);
        when(mockScene.getApiName()).thenReturn("test_scene");
        when(mockScene.getDisplayName()).thenReturn("测试场景");
        
        List<IScene> mockScenes = Arrays.asList(mockScene);
        when(sceneLogicService.findScenes(eq("AccountObj"), any(User.class), eq("attr1"), any())).thenReturn(mockScenes);
        when(sceneLogicService.findBaseScenes(eq("AccountObj"), eq("attr1"), any(User.class))).thenReturn(mockScenes);
        when(sceneLogicService.findSceneConfigList(any(User.class), eq("AccountObj"), any())).thenReturn(Arrays.asList());

        // Act
        FindSceneList.Result result = service.findScenes(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getScenes());
        assertEquals(1, result.getScenes().size());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).findScenes(eq("AccountObj"), any(User.class), eq("attr1"), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findScenes方法查找单个场景
     */
    @Test
    @DisplayName("测试findScenes方法 - 查找单个场景")
    void testFindScenes_SingleScene() {
        // Arrange
        FindSceneInfo.Arg arg = new FindSceneInfo.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setSceneApiName("scene1");
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        IScene mockScene = mock(IScene.class);
        when(mockScene.getApiName()).thenReturn("scene1");
        when(mockScene.getDisplayName()).thenReturn("测试场景");
        
        when(sceneLogicService.findSceneByApiName(any(User.class), eq("AccountObj"), eq("scene1"), eq("attr1"), eq("app1"))).thenReturn(mockScene);

        // Act
        FindSceneInfo.Result result = service.findScenes(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getScene());
        assertEquals("scene1", result.getScene().getApiName());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).findSceneByApiName(any(User.class), eq("AccountObj"), eq("scene1"), eq("attr1"), eq("app1"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createTenantScene方法成功场景
     */
    @Test
    @DisplayName("测试createTenantScene方法 - 成功场景")
    void testCreateTenantScene_Success() {
        // Arrange
        SceneDTO sceneDTO = new SceneDTO();
        sceneDTO.setObjectDescribeApiName("AccountObj");
        sceneDTO.setDisplayName("测试场景");
        sceneDTO.setClearCustomConfig(true);
        
        ObjectTenantScene.Arg arg = new ObjectTenantScene.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setScene(sceneDTO);
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        ITenantScene mockTenantScene = mock(ITenantScene.class);
        when(mockTenantScene.getApiName()).thenReturn("tenant_scene");
        
        when(sceneLogicService.createTenantScene(eq(sceneDTO), eq("AccountObj"), eq("attr1"), any(User.class))).thenReturn(mockTenantScene);
        when(describeLogicService.findObjectWithoutCopyIfGray(anyString(), eq("AccountObj"))).thenReturn(mock(ObjectDescribe.class));

        // Act
        ObjectTenantScene.Result result = service.createTenantScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getScene());
        assertEquals("tenant_scene", result.getScene().getApiName());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).createTenantScene(eq(sceneDTO), eq("AccountObj"), eq("attr1"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createTenantScene方法场景为空时抛出异常
     */
    @Test
    @DisplayName("测试createTenantScene方法 - 场景为空时抛出异常")
    void testCreateTenantScene_NullScene() {
        // Arrange
        ObjectTenantScene.Arg arg = new ObjectTenantScene.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setScene(null);
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            service.createTenantScene(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateTenantScene方法租户场景
     */
    @Test
    @DisplayName("测试updateTenantScene方法 - 租户场景")
    void testUpdateTenantScene_TenantScene() {
        // Arrange
        SceneDTO sceneDTO = new SceneDTO();
        sceneDTO.setObjectDescribeApiName("AccountObj");
        sceneDTO.setDisplayName("测试场景");
        sceneDTO.setType("tenant");
        sceneDTO.setClearCustomConfig(true);
        
        ObjectTenantScene.Arg arg = new ObjectTenantScene.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setScene(sceneDTO);
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        ITenantScene mockTenantScene = mock(ITenantScene.class);
        when(mockTenantScene.getApiName()).thenReturn("tenant_scene");
        
        when(sceneLogicService.updateTenantScene(eq(sceneDTO), eq("AccountObj"), any(User.class))).thenReturn(mockTenantScene);
        when(describeLogicService.findObjectWithoutCopyIfGray(anyString(), eq("AccountObj"))).thenReturn(mock(ObjectDescribe.class));

        // Act
        ObjectTenantScene.Result result = service.updateTenantScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getScene());
        assertEquals("tenant_scene", result.getScene().getApiName());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).updateTenantScene(eq(sceneDTO), eq("AccountObj"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateTenantScene方法系统场景
     */
    @Test
    @DisplayName("测试updateTenantScene方法 - 系统场景")
    void testUpdateTenantScene_SystemScene() {
        // Arrange
        SceneDTO sceneDTO = new SceneDTO();
        sceneDTO.setObjectDescribeApiName("AccountObj");
        sceneDTO.setDisplayName("测试场景");
        sceneDTO.setApiName("test_scene");
        sceneDTO.setType("system");
        sceneDTO.setClearCustomConfig(true);
        
        ObjectTenantScene.Arg arg = new ObjectTenantScene.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setScene(sceneDTO);
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        ISystemScene mockSystemScene = mock(ISystemScene.class);
        when(mockSystemScene.getApiName()).thenReturn("test_scene");
        
        when(sceneLogicService.updateSystemScene(eq(sceneDTO), any(User.class))).thenReturn(mockSystemScene);
        when(describeLogicService.findObjectWithoutCopyIfGray(anyString(), eq("AccountObj"))).thenReturn(mock(ObjectDescribe.class));

        // Act
        ObjectTenantScene.Result result = service.updateTenantScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getScene());
        assertEquals("test_scene", result.getScene().getApiName());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).updateSystemScene(eq(sceneDTO), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enableScene方法
     */
    @Test
    @DisplayName("测试enableScene方法")
    void testEnableScene() {
        // Arrange
        ChangeSceneStatus.Arg arg = new ChangeSceneStatus.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setSceneApiName("scene1");
        arg.setType("tenant_scene");
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        // Act
        ChangeSceneStatus.Result result = service.enableScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).enableScene(eq("AccountObj"), eq("scene1"), eq("tenant_scene"), eq("attr1"), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试disableScene方法
     */
    @Test
    @DisplayName("测试disableScene方法")
    void testDisableScene() {
        // Arrange
        ChangeSceneStatus.Arg arg = new ChangeSceneStatus.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setSceneApiName("scene1");
        arg.setType("tenant_scene");
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        // Act
        ChangeSceneStatus.Result result = service.disableScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).disableScene(eq("AccountObj"), eq("scene1"), eq("tenant_scene"), eq("attr1"), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteScene方法
     */
    @Test
    @DisplayName("测试deleteScene方法")
    void testDeleteScene() {
        // Arrange
        ChangeSceneStatus.Arg arg = new ChangeSceneStatus.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setSceneApiName("scene1");
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        // Act
        ChangeSceneStatus.Result result = service.deleteScene(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).deleteTenantScene(eq("AccountObj"), eq("scene1"), eq("attr1"), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateTenantSceneCount方法
     */
    @Test
    @DisplayName("测试validateTenantSceneCount方法")
    void testValidateTenantSceneCount() {
        // Arrange
        ValidateTenantSceneCount.Arg arg = new ValidateTenantSceneCount.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setAppId("app1");

        // Act
        ValidateTenantSceneCount.Result result = service.validateTenantSceneCount(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).validateTenantSceneSum(eq("AccountObj"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateSceneName方法
     */
    @Test
    @DisplayName("测试validateSceneName方法")
    void testValidateSceneName() {
        // Arrange
        ValidateSceneName.Arg arg = new ValidateSceneName.Arg();
        arg.setSceneApiName("scene1");
        arg.setDisplayName("场景1");
        arg.setId("id1");
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        when(sceneLogicService.validateSceneApiNameRepeat(eq("scene1"), eq("场景1"), eq("id1"), eq("attr1"), any(User.class))).thenReturn(true);

        // Act
        ValidateSceneName.Result result = service.validateSceneName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getApiNameRepeat());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).validateSceneApiNameRepeat(eq("scene1"), eq("场景1"), eq("id1"), eq("attr1"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setDefaultScenePriority方法
     */
    @Test
    @DisplayName("测试setDefaultScenePriority方法")
    void testSetDefaultScenePriority() {
        // Arrange
        DefaultScenePriority.Arg arg = new DefaultScenePriority.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setSceneApiNames(Arrays.asList("scene1", "scene2"));
        arg.setExtendAttribute("attr1");
        arg.setAppId("app1");

        // Act
        DefaultScenePriority.Result result = service.setDefaultScenePriority(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(serviceContext).setAttribute("is_outer", true);
        verify(sceneLogicService).setUpDefaultScenePriority(eq("AccountObj"), eq(Arrays.asList("scene1", "scene2")), eq("attr1"), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(sceneLogicService);
        assertNotNull(describeLogicService);
    }
}
