package com.facishare.paas.appframework.core.predef.service;

import com.facishare.fsc.api.model.CreateFileShareIds;
import com.facishare.fsc.api.service.SharedFileService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.support.GDSHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FileLogicServiceTest {

    @Mock
    private SharedFileService sharedFileService;
    
    @Mock
    private GDSHandler gdsHandler;
    
    @InjectMocks
    private FileLogicService fileLogicService;
    
    private ServiceContext serviceContext;
    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User("test-tenant", "12345");
        RequestContext requestContext = RequestContext.builder()
                .tenantId("test-tenant")
                .user(testUser)
                .build();
        serviceContext = new ServiceContext(requestContext, "testService", "testMethod");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试npath2Token方法，验证正常用户的处理流程
     */
    @Test
    @DisplayName("测试npath2Token - 正常用户")
    void testNpath2Token_NormalUser() throws Exception {
        // Arrange
        CreateFileShareIds.Arg arg = new CreateFileShareIds.Arg();
        String expectedEA = "test-ea";
        CreateFileShareIds.Result expectedResult = new CreateFileShareIds.Result();
        
        when(gdsHandler.getEAByEI(eq("test-tenant"))).thenReturn(expectedEA);
        when(sharedFileService.createFileShareIds(any(CreateFileShareIds.Arg.class)))
                .thenReturn(expectedResult);

        // Act
        CreateFileShareIds.Result result = fileLogicService.npath2Token(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertSame(expectedResult, result);
        
        // 使用ArgumentCaptor验证参数设置
        ArgumentCaptor<CreateFileShareIds.Arg> argCaptor = ArgumentCaptor.forClass(CreateFileShareIds.Arg.class);
        verify(sharedFileService).createFileShareIds(argCaptor.capture());
        
        CreateFileShareIds.Arg capturedArg = argCaptor.getValue();
        assertEquals(12345, capturedArg.employeeId);
        assertEquals(expectedEA, capturedArg.ea);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试npath2Token方法，验证userId为空的情况
     */
    @Test
    @DisplayName("测试npath2Token - 空userId")
    void testNpath2Token_EmptyUserId() throws Exception {
        // Arrange
        User emptyUser = new User("test-tenant", "");
        RequestContext requestContext = RequestContext.builder()
                .tenantId("test-tenant")
                .user(emptyUser)
                .build();
        ServiceContext emptyUserContext = new ServiceContext(requestContext, "testService", "testMethod");
        
        CreateFileShareIds.Arg arg = new CreateFileShareIds.Arg();
        String expectedEA = "test-ea";
        CreateFileShareIds.Result expectedResult = new CreateFileShareIds.Result();
        
        when(gdsHandler.getEAByEI(eq("test-tenant"))).thenReturn(expectedEA);
        when(sharedFileService.createFileShareIds(any(CreateFileShareIds.Arg.class)))
                .thenReturn(expectedResult);

        // Act
        CreateFileShareIds.Result result = fileLogicService.npath2Token(arg, emptyUserContext);

        // Assert
        assertNotNull(result);
        
        // 验证当userId为空时，使用默认值"10000"
        ArgumentCaptor<CreateFileShareIds.Arg> argCaptor = ArgumentCaptor.forClass(CreateFileShareIds.Arg.class);
        verify(sharedFileService).createFileShareIds(argCaptor.capture());
        assertEquals(10000, argCaptor.getValue().employeeId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试npath2Token方法，验证超级管理员的情况
     */
    @Test
    @DisplayName("测试npath2Token - 超级管理员")
    void testNpath2Token_SuperAdmin() throws Exception {
        // Arrange
        User superAdminUser = mock(User.class);
        when(superAdminUser.getTenantId()).thenReturn("test-tenant");
        when(superAdminUser.getUserId()).thenReturn("admin-user");
        when(superAdminUser.isSupperAdmin()).thenReturn(true);
        
        RequestContext requestContext = RequestContext.builder()
                .tenantId("test-tenant")
                .user(superAdminUser)
                .build();
        ServiceContext adminContext = new ServiceContext(requestContext, "testService", "testMethod");
        
        CreateFileShareIds.Arg arg = new CreateFileShareIds.Arg();
        String expectedEA = "test-ea";
        CreateFileShareIds.Result expectedResult = new CreateFileShareIds.Result();
        
        when(gdsHandler.getEAByEI(eq("test-tenant"))).thenReturn(expectedEA);
        when(sharedFileService.createFileShareIds(any(CreateFileShareIds.Arg.class)))
                .thenReturn(expectedResult);

        // Act
        CreateFileShareIds.Result result = fileLogicService.npath2Token(arg, adminContext);

        // Assert
        assertNotNull(result);
        
        // 验证超级管理员使用默认值"10000"
        ArgumentCaptor<CreateFileShareIds.Arg> argCaptor = ArgumentCaptor.forClass(CreateFileShareIds.Arg.class);
        verify(sharedFileService).createFileShareIds(argCaptor.capture());
        assertEquals(10000, argCaptor.getValue().employeeId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试npath2Token方法，验证异常处理情况
     */
    @Test
    @DisplayName("测试npath2Token - 异常处理")
    void testNpath2Token_ExceptionHandling() throws Exception {
        // Arrange
        CreateFileShareIds.Arg arg = new CreateFileShareIds.Arg();
        String expectedEA = "test-ea";
        
        when(gdsHandler.getEAByEI(eq("test-tenant"))).thenReturn(expectedEA);
        when(sharedFileService.createFileShareIds(any(CreateFileShareIds.Arg.class)))
                .thenThrow(new RuntimeException("Service error"));

        // Act
        CreateFileShareIds.Result result = fileLogicService.npath2Token(arg, serviceContext);

        // Assert
        assertNotNull(result);
        // 验证异常被捕获并返回了新的Result对象
        verify(sharedFileService).createFileShareIds(any(CreateFileShareIds.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试npath2Token方法，验证null userId的情况
     */
    @Test
    @DisplayName("测试npath2Token - null userId")
    void testNpath2Token_NullUserId() throws Exception {
        // Arrange
        User nullUserIdUser = new User("test-tenant", null);
        RequestContext requestContext = RequestContext.builder()
                .tenantId("test-tenant")
                .user(nullUserIdUser)
                .build();
        ServiceContext nullUserContext = new ServiceContext(requestContext, "testService", "testMethod");
        
        CreateFileShareIds.Arg arg = new CreateFileShareIds.Arg();
        String expectedEA = "test-ea";
        CreateFileShareIds.Result expectedResult = new CreateFileShareIds.Result();
        
        when(gdsHandler.getEAByEI(eq("test-tenant"))).thenReturn(expectedEA);
        when(sharedFileService.createFileShareIds(any(CreateFileShareIds.Arg.class)))
                .thenReturn(expectedResult);

        // Act
        CreateFileShareIds.Result result = fileLogicService.npath2Token(arg, nullUserContext);

        // Assert
        assertNotNull(result);
        
        // 验证当userId为null时，使用默认值"10000"
        ArgumentCaptor<CreateFileShareIds.Arg> argCaptor = ArgumentCaptor.forClass(CreateFileShareIds.Arg.class);
        verify(sharedFileService).createFileShareIds(argCaptor.capture());
        assertEquals(10000, argCaptor.getValue().employeeId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数验证功能，确保employeeId和ea被正确设置
     */
    @Test
    @DisplayName("测试参数验证 - employeeId和ea设置")
    void testParameterValidation() throws Exception {
        // Arrange
        CreateFileShareIds.Arg arg = new CreateFileShareIds.Arg();
        String expectedEA = "enterprise-account-123";
        CreateFileShareIds.Result expectedResult = new CreateFileShareIds.Result();
        
        when(gdsHandler.getEAByEI(eq("test-tenant"))).thenReturn(expectedEA);
        when(sharedFileService.createFileShareIds(any(CreateFileShareIds.Arg.class)))
                .thenReturn(expectedResult);

        // Act
        fileLogicService.npath2Token(arg, serviceContext);

        // Assert
        ArgumentCaptor<CreateFileShareIds.Arg> argCaptor = ArgumentCaptor.forClass(CreateFileShareIds.Arg.class);
        verify(sharedFileService).createFileShareIds(argCaptor.capture());
        
        CreateFileShareIds.Arg capturedArg = argCaptor.getValue();
        assertNotNull(capturedArg);
        assertEquals(12345, capturedArg.employeeId);
        assertEquals(expectedEA, capturedArg.ea);
        assertSame(arg, capturedArg); // 验证是同一个对象被修改和传递
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多次调用以验证服务的幂等性
     */
    @Test
    @DisplayName("测试多次调用")
    void testMultipleCalls() throws Exception {
        // Arrange
        String expectedEA = "test-ea";
        CreateFileShareIds.Result expectedResult1 = new CreateFileShareIds.Result();
        CreateFileShareIds.Result expectedResult2 = new CreateFileShareIds.Result();
        
        when(gdsHandler.getEAByEI(eq("test-tenant"))).thenReturn(expectedEA);
        when(sharedFileService.createFileShareIds(any(CreateFileShareIds.Arg.class)))
                .thenReturn(expectedResult1)
                .thenReturn(expectedResult2);

        // Act
        CreateFileShareIds.Arg arg1 = new CreateFileShareIds.Arg();
        CreateFileShareIds.Result result1 = fileLogicService.npath2Token(arg1, serviceContext);
        
        CreateFileShareIds.Arg arg2 = new CreateFileShareIds.Arg();
        CreateFileShareIds.Result result2 = fileLogicService.npath2Token(arg2, serviceContext);

        // Assert
        assertSame(expectedResult1, result1);
        assertSame(expectedResult2, result2);
        verify(sharedFileService, times(2)).createFileShareIds(any(CreateFileShareIds.Arg.class));
        verify(gdsHandler, times(2)).getEAByEI(eq("test-tenant"));
    }
}