package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.predef.service.dto.gdpr.*;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.gdpr.GdprFormData;
import com.facishare.paas.appframework.metadata.gdpr.GdprService;
import com.facishare.paas.appframework.metadata.repository.model.GdprCompliance;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectGdprService单元测试类
 * 测试GDPR合规性服务的各种功能
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("ObjectGdprService单元测试")
public class ObjectGdprServiceTest extends BaseServiceTest {

    @Mock
    private GdprService gdprService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private MetaDataFindService metaDataFindService;

    @InjectMocks
    private ObjectGdprService objectGdprService;

    private static final String DESCRIBE_API_NAME = "TestObj__c";
    private static final String DATA_ID = "test_data_id";
    private static final String LEGAL_BASE = "agree";
    private static final String REMARK = "test remark";

    @Override
    protected String getServiceName() {
        return "gdpr";
    }

    @BeforeEach
    void setUp() {
        // 基础设置已在BaseServiceTest中完成
    }



    @Test
    @DisplayName("GenerateByAI - 测试openGdprCompliance方法成功场景")
    void testOpenGdprCompliance_Success() {
        // Arrange
        OpenGdprCompliance.Arg arg = new OpenGdprCompliance.Arg();
        arg.setApiNames(Lists.newArrayList(DESCRIBE_API_NAME));
        arg.setUnusableOperation(Lists.newArrayList("export"));
        arg.setForbidExport("true");
        arg.setPeriod(30);
        arg.setPeriodType("day");

        // Act
        OpenGdprCompliance.Result result = objectGdprService.openGdprCompliance(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(gdprService, times(1)).openGdprCompliance(eq(testUser), any(GdprCompliance.class));
    }

    @Test
    @DisplayName("GenerateByAI - 测试closeGdprCompliance方法成功场景")
    void testCloseGdprCompliance_Success() {
        // Arrange
        CloseGdprCompliance.Arg arg = new CloseGdprCompliance.Arg();

        // Act
        CloseGdprCompliance.Result result = objectGdprService.closeGdprCompliance(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(gdprService, times(1)).closeGdprCompliance(testUser);
    }

    @Test
    @DisplayName("GenerateByAI - 测试findGdprComplianceStatus方法成功场景")
    void testFindGdprComplianceStatus_Success() {
        // Arrange
        FindGdprComplianceStatus.Arg arg = new FindGdprComplianceStatus.Arg();
        arg.setApiName(DESCRIBE_API_NAME);

        when(gdprService.findGdprComplianceStatusByCache(testUser, DESCRIBE_API_NAME))
                .thenReturn(true);

        // Act
        FindGdprComplianceStatus.Result result = objectGdprService.findGdprComplianceStatus(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(gdprService, times(1)).findGdprComplianceStatusByCache(testUser, DESCRIBE_API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试findGdprComplianceData方法成功场景")
    void testFindGdprComplianceData_Success() {
        // Arrange
        FindGdprComplianceData.Arg arg = new FindGdprComplianceData.Arg();

        GdprCompliance mockCompliance = mock(GdprCompliance.class);
        when(mockCompliance.getUnusableOperation()).thenReturn(Lists.newArrayList("export"));
        when(gdprService.findGdprComplianceData(testUser))
                .thenReturn(Optional.of(mockCompliance));

        // Act
        FindGdprComplianceData.Result result = objectGdprService.findGdprComplianceData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        verify(gdprService, times(1)).findGdprComplianceData(testUser);
    }

    @Test
    @DisplayName("GenerateByAI - 测试findGdprPersonalField方法成功场景")
    void testFindGdprPersonalField_Success() {
        // Arrange
        FindGdprPersonalField.Arg arg = new FindGdprPersonalField.Arg();
        arg.setApiName(DESCRIBE_API_NAME);

        GdprCompliance mockCompliance = mock(GdprCompliance.class);
        when(mockCompliance.getApiName()).thenReturn(DESCRIBE_API_NAME);
        when(mockCompliance.getSensitiveFields()).thenReturn(Lists.newArrayList("field1"));
        when(gdprService.findGdprCompliance(testUser, DESCRIBE_API_NAME))
                .thenReturn(Lists.newArrayList(mockCompliance));

        // Act
        FindGdprPersonalField.Result result = objectGdprService.findGdprPersonalField(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(gdprService, times(1)).findGdprCompliance(testUser, DESCRIBE_API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试updateGdprPersonalField方法成功场景")
    void testUpdateGdprPersonalField_Success() {
        // Arrange
        UpdateGdprPersonalField.Arg arg = new UpdateGdprPersonalField.Arg();
        arg.setApiName(DESCRIBE_API_NAME);
        arg.setSensitiveFields(Lists.newArrayList("field1"));
        arg.setOrdinaryFields(Lists.newArrayList("field2"));

        // Act
        UpdateGdprPersonalField.Result result = objectGdprService.updateGdprPersonalField(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(gdprService, times(1)).updateGdprPersonalField(testUser, DESCRIBE_API_NAME,
                Lists.newArrayList("field1"), Lists.newArrayList("field2"));
    }

    @Test
    @DisplayName("GenerateByAI - 测试openGdprLegalBase方法成功场景")
    void testOpenGdprLegalBase_Success() {
        // Arrange
        OpenGdprLegalBase.Arg arg = new OpenGdprLegalBase.Arg();
        arg.setApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);

        // Act
        OpenGdprLegalBase.Result result = objectGdprService.openGdprLegalBase(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(gdprService, times(1)).openGdprLegalBase(testUser, DESCRIBE_API_NAME, DATA_ID);
    }

    @Test
    @DisplayName("GenerateByAI - 测试closeGdprLegalBase方法成功场景")
    void testCloseGdprLegalBase_Success() {
        // Arrange
        CloseGdprLegalBase.Arg arg = new CloseGdprLegalBase.Arg();
        arg.setApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);

        // Act
        CloseGdprLegalBase.Result result = objectGdprService.closeGdprLegalBase(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(gdprService, times(1)).closeGdprLegalBase(testUser, DESCRIBE_API_NAME, DATA_ID);
    }

    @Test
    @DisplayName("GenerateByAI - 测试updateGdprLegalBase方法成功场景")
    void testUpdateGdprLegalBase_Success() {
        // Arrange
        UpdateGdprLegalBase.Arg arg = new UpdateGdprLegalBase.Arg();
        arg.setApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setLegalBase(LEGAL_BASE);

        // Act
        UpdateGdprLegalBase.Result result = objectGdprService.updateGdprLegalBase(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(gdprService, times(1)).updateGdprLegalBase(testUser, DESCRIBE_API_NAME, DATA_ID, LEGAL_BASE, null);
    }

    @Test
    @DisplayName("GenerateByAI - 测试submitGdprFormData方法成功场景")
    void testSubmitGdprFormData_Success() {
        // Arrange
        SubmitGdprFormData.Arg arg = new SubmitGdprFormData.Arg();
        arg.setId(DATA_ID);
        arg.setRemark(REMARK);
        arg.setAgreeFirstOption(Lists.newArrayList("option1"));

        // Act
        SubmitGdprFormData.Result result = objectGdprService.submitGdprFormData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(gdprService, times(1)).submitGdprFormData(testUser, DATA_ID,
                Lists.newArrayList("option1"), REMARK);
    }

    @Test
    @DisplayName("GenerateByAI - 测试findGdprFormData方法成功场景")
    void testFindGdprFormData_Success() {
        // Arrange
        FindGdprFormData.Arg arg = new FindGdprFormData.Arg();

        GdprFormData mockFormData = mock(GdprFormData.class);
        when(mockFormData.getEnterpriseName()).thenReturn("Test Enterprise");
        when(mockFormData.getLang()).thenReturn("en");
        when(gdprService.findGdprFormData(testUser)).thenReturn(mockFormData);

        // Act
        FindGdprFormData.Result result = objectGdprService.findGdprFormData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals("Test Enterprise", result.getEnterpriseName());
        assertEquals("en", result.getLang());
        verify(gdprService, times(1)).findGdprFormData(testUser);
    }

    @Test
    @DisplayName("GenerateByAI - 测试saveGdprFormLang方法成功场景")
    void testSaveGdprFormLang_Success() {
        // Arrange
        SaveGdprFormLang.Arg arg = new SaveGdprFormLang.Arg();
        arg.setLang("en");

        // Act
        SaveGdprFormLang.Result result = objectGdprService.saveGdprFormLang(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(gdprService, times(1)).saveGdprFormLang(testUser, "en");
    }

    @Test
    @DisplayName("GenerateByAI - 测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectGdprService);
        assertNotNull(gdprService);
        assertNotNull(describeLogicService);
        assertNotNull(metaDataFindService);
    }
}
