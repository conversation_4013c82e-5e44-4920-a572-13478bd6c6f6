package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.multiCurrency.*;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.MultiCurrencyLogicService;
import com.facishare.paas.appframework.metadata.MtCurrency;
import com.facishare.paas.appframework.metadata.repository.model.MtCurrencyExchange;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectMultiCurrencyService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectMultiCurrencyService单元测试")
class ObjectMultiCurrencyServiceTest {

    @Mock
    private MultiCurrencyLogicService multiCurrencyLogicService;
    
    @InjectMocks
    private ObjectMultiCurrencyService service;
    
    private User testUser;
    private ServiceContext serviceContext;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        testUser = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(testUser)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "multi_currency", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试openMultiCurrency方法，正常开启多货币
     */
    @Test
    @DisplayName("正常场景 - 测试openMultiCurrency方法")
    void testOpenMultiCurrency_NormalCase() {
        // 准备测试数据
        OpenMultiCurrency.Arg arg = new OpenMultiCurrency.Arg();
        arg.setFunctionalCurrency("CNY");
        
        // 配置Mock行为
        doNothing().when(multiCurrencyLogicService).openMultiCurrency(anyString(), any(User.class));
        
        // 执行被测试方法
        OpenMultiCurrency.Result result = service.openMultiCurrency(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).openMultiCurrency(eq("CNY"), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试multiCurrencyStatus方法，正常查询多货币状态
     */
    @Test
    @DisplayName("正常场景 - 测试multiCurrencyStatus方法")
    void testMultiCurrencyStatus_NormalCase() {
        // 配置Mock行为
        when(multiCurrencyLogicService.findMultiCurrencyStatus(any(User.class))).thenReturn(1);
        
        // 执行被测试方法
        FindMultiCurrencyStatus.Result result = service.multiCurrencyStatus(serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getStatus());
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).findMultiCurrencyStatus(eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试multiCurrencyStatus方法，用户为null的场景
     */
    @Test
    @DisplayName("异常场景 - 测试multiCurrencyStatus方法用户为null")
    void testMultiCurrencyStatus_NullUser() {
        // 准备测试数据
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(null)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        ServiceContext nullUserContext = new ServiceContext(requestContext, "multi_currency", "test");

        // 执行被测试方法并验证异常
        // 修复：用户为null时应该抛出PermissionError异常
        assertThrows(PermissionError.class, () -> {
            service.multiCurrencyStatus(nullUserContext);
        });

        // 验证Mock交互
        verify(multiCurrencyLogicService, never()).findMultiCurrencyStatus(any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试refreshCurrencyOptions方法，正常刷新货币选项
     */
    @Test
    @DisplayName("正常场景 - 测试refreshCurrencyOptions方法")
    void testRefreshCurrencyOptions_NormalCase() {
        // 准备测试数据
        RefreshCurrencyOptions.Arg arg = new RefreshCurrencyOptions.Arg();
        arg.setTenantIds(Lists.newArrayList("74255", "74256"));
        
        // 配置Mock行为
        doNothing().when(multiCurrencyLogicService).bulkRefreshCurrencyOptions(anyList());
        
        // 执行被测试方法
        RefreshCurrencyOptions.Result result = service.refreshCurrencyOptions(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).bulkRefreshCurrencyOptions(eq(Lists.newArrayList("74255", "74256")));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchModifyRate方法，正常批量编辑汇率
     */
    @Test
    @DisplayName("正常场景 - 测试batchModifyRate方法")
    void testBatchModifyRate_NormalCase() {
        // 准备测试数据
        BatchModifyRate.Arg arg = new BatchModifyRate.Arg();
        arg.setExchangeRateList(Lists.newArrayList());
        
        // 配置Mock行为
        doNothing().when(multiCurrencyLogicService).batchModifyRate(anyList(), any(User.class));
        
        // 执行被测试方法
        BatchModifyRate.Result result = service.batchModifyRate(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).batchModifyRate(anyList(), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryRate方法，正常分页查询汇率
     */
    @Test
    @DisplayName("正常场景 - 测试queryRate方法")
    void testQueryRate_NormalCase() {
        // 准备测试数据
        QueryRate.Arg arg = new QueryRate.Arg();
        arg.setCurrencyCode("USD");
        arg.setPageSize(10);
        arg.setPageNumber(1);
        
        // 配置Mock行为 - 修复：返回有效的QueryResult而不是null
        @SuppressWarnings("unchecked")
        com.facishare.paas.metadata.api.QueryResult mockQueryResult = mock(com.facishare.paas.metadata.api.QueryResult.class);
        when(mockQueryResult.getTotalNumber()).thenReturn(5);
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList());

        when(multiCurrencyLogicService.queryRate(anyString(), any(), any(), anyInt(), anyInt(), any(User.class)))
                .thenReturn(mockQueryResult);
        
        // 执行被测试方法
        QueryRate.Result result = service.queryRate(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getPageNumber());
        assertEquals(Integer.valueOf(10), result.getPageSize());
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).queryRate(eq("USD"), any(), any(), eq(10), eq(1), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(multiCurrencyLogicService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findCurrencyByCode方法，正常根据货币代码查询货币信息
     */
    @Test
    @DisplayName("正常场景 - 测试findCurrencyByCode方法")
    void testFindCurrencyByCode_NormalCase() {
        // 准备测试数据
        FindCurrencyByCode.Arg arg = new FindCurrencyByCode.Arg();
        arg.setCurrencyCode("USD");

        // Mock返回的货币信息
        MtCurrency mockCurrency = MtCurrency.builder()
                .currencyCode("USD")
                .currencyLabel("美元")
                .exchangeRate("6.8")
                .build();

        // 配置Mock行为
        when(multiCurrencyLogicService.findCurrencyByCode(anyString(), any(User.class)))
                .thenReturn(mockCurrency);

        // 执行被测试方法
        FindCurrencyByCode.Result result = service.findCurrencyByCode(arg, serviceContext);

        // 验证结果
        assertNotNull(result);
        assertEquals("USD", result.getCurrencyCode());
        assertEquals("美元", result.getCurrencyLabel());
        assertEquals("6.8", result.getExchangeRate());

        // 验证Mock交互
        verify(multiCurrencyLogicService).findCurrencyByCode(eq("USD"), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findExchangeRateByCurrency方法，正常查询汇率
     */
    @Test
    @DisplayName("正常场景 - 测试findExchangeRateByCurrency方法")
    void testFindExchangeRateByCurrency_NormalCase() {
        // 准备测试数据
        FindExchangeRateByCurrency.Arg arg = new FindExchangeRateByCurrency.Arg();
        arg.setFromCurrencyCode("USD");
        arg.setToCurrencyCode("CNY");

        // Mock返回的汇率信息
        MtCurrencyExchange mockExchange = new MtCurrencyExchange();
        mockExchange.setExchangeRate("6.8");

        // 配置Mock行为
        when(multiCurrencyLogicService.findCurrencyExchange(any(User.class), anyString(), anyString()))
                .thenReturn(mockExchange);

        // 执行被测试方法
        FindExchangeRateByCurrency.Result result = service.findExchangeRateByCurrency(arg, serviceContext);

        // 验证结果
        assertNotNull(result);
        assertEquals("6.8", result.getExchangeRate());

        // 验证Mock交互
        verify(multiCurrencyLogicService).findCurrencyExchange(eq(testUser), eq("USD"), eq("CNY"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findExchangeRateByCurrency方法，汇率信息为null的场景
     */
    @Test
    @DisplayName("边界场景 - 测试findExchangeRateByCurrency方法汇率为null")
    void testFindExchangeRateByCurrency_NullExchange() {
        // 准备测试数据
        FindExchangeRateByCurrency.Arg arg = new FindExchangeRateByCurrency.Arg();
        arg.setFromCurrencyCode("EUR");
        arg.setToCurrencyCode("CNY");

        // 配置Mock行为 - 返回null
        when(multiCurrencyLogicService.findCurrencyExchange(any(User.class), anyString(), anyString()))
                .thenReturn(null);

        // 执行被测试方法
        FindExchangeRateByCurrency.Result result = service.findExchangeRateByCurrency(arg, serviceContext);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getExchangeRate());

        // 验证Mock交互
        verify(multiCurrencyLogicService).findCurrencyExchange(eq(testUser), eq("EUR"), eq("CNY"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试openMultiCurrency方法，不同货币代码的场景
     */
    @Test
    @DisplayName("正常场景 - 测试openMultiCurrency方法使用不同货币")
    void testOpenMultiCurrency_DifferentCurrency() {
        // 准备测试数据
        OpenMultiCurrency.Arg arg = new OpenMultiCurrency.Arg();
        arg.setFunctionalCurrency("USD");

        // 配置Mock行为
        doNothing().when(multiCurrencyLogicService).openMultiCurrency(anyString(), any(User.class));

        // 执行被测试方法
        OpenMultiCurrency.Result result = service.openMultiCurrency(arg, serviceContext);

        // 验证结果
        assertNotNull(result);

        // 验证Mock交互
        verify(multiCurrencyLogicService).openMultiCurrency(eq("USD"), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试multiCurrencyStatus方法，返回不同状态值的场景
     */
    @Test
    @DisplayName("正常场景 - 测试multiCurrencyStatus方法返回不同状态")
    void testMultiCurrencyStatus_DifferentStatus() {
        // 配置Mock行为 - 返回状态0（未开启）
        when(multiCurrencyLogicService.findMultiCurrencyStatus(any(User.class))).thenReturn(0);

        // 执行被测试方法
        FindMultiCurrencyStatus.Result result = service.multiCurrencyStatus(serviceContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(0), result.getStatus());

        // 验证Mock交互
        verify(multiCurrencyLogicService).findMultiCurrencyStatus(eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试refreshCurrencyOptions方法，空租户列表的场景
     */
    @Test
    @DisplayName("边界场景 - 测试refreshCurrencyOptions方法空租户列表")
    void testRefreshCurrencyOptions_EmptyTenantList() {
        // 准备测试数据
        RefreshCurrencyOptions.Arg arg = new RefreshCurrencyOptions.Arg();
        arg.setTenantIds(Lists.newArrayList());

        // 配置Mock行为
        doNothing().when(multiCurrencyLogicService).bulkRefreshCurrencyOptions(anyList());

        // 执行被测试方法
        RefreshCurrencyOptions.Result result = service.refreshCurrencyOptions(arg, serviceContext);

        // 验证结果
        assertNotNull(result);

        // 验证Mock交互
        verify(multiCurrencyLogicService).bulkRefreshCurrencyOptions(eq(Lists.newArrayList()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryRate方法，不同分页参数的场景
     */
    @Test
    @DisplayName("正常场景 - 测试queryRate方法不同分页参数")
    void testQueryRate_DifferentPagination() {
        // 准备测试数据
        QueryRate.Arg arg = new QueryRate.Arg();
        arg.setCurrencyCode("EUR");
        arg.setPageSize(20);
        arg.setPageNumber(2);
        arg.setStartTime(1640995200000L); // 2022-01-01
        arg.setEndTime(1672531199000L);   // 2022-12-31

        // 配置Mock行为
        com.facishare.paas.metadata.api.QueryResult mockQueryResult = mock(com.facishare.paas.metadata.api.QueryResult.class);
        when(mockQueryResult.getTotalNumber()).thenReturn(35);
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList());

        when(multiCurrencyLogicService.queryRate(anyString(), any(), any(), anyInt(), anyInt(), any(User.class)))
                .thenReturn(mockQueryResult);

        // 执行被测试方法
        QueryRate.Result result = service.queryRate(arg, serviceContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(2), result.getPageNumber());
        assertEquals(Integer.valueOf(20), result.getPageSize());
        assertEquals(Integer.valueOf(35), result.getTotalCount());
        assertEquals(Integer.valueOf(2), result.getPageCount()); // 35/20 = 2页

        // 验证Mock交互
        verify(multiCurrencyLogicService).queryRate(eq("EUR"), eq(1640995200000L), eq(1672531199000L), eq(20), eq(2), eq(testUser));
    }
}
