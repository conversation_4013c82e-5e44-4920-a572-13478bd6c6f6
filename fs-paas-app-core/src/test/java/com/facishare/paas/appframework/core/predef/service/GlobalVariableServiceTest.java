package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.globalvariable.*;
import com.facishare.paas.appframework.metadata.GlobalVarServiceImpl;
import com.facishare.paas.appframework.metadata.dto.GlobalVariableResult;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;
import com.facishare.paas.metadata.impl.describe.GlobalVariableDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GlobalVariableService单元测试类
 * 测试全局变量服务的核心业务方法
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("GlobalVariableService单元测试")
class GlobalVariableServiceTest {

    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String API_NAME = "testVariable";
    private static final String JSON_DATA = "{\"apiName\":\"testVariable\",\"label\":\"测试变量\"}";

    @Mock
    private GlobalVarServiceImpl globalVarService;
    
    @InjectMocks
    private GlobalVariableService globalVariableService;
    
    private ServiceContext serviceContext;
    private User user;

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        serviceContext = new ServiceContext(requestContext, "global_variable", "test");
    }

    @AfterEach
    void tearDown() {
        RequestContextManager.removeContext();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建全局变量成功的场景
     */
    @Test
    @DisplayName("测试创建全局变量成功")
    void testCreateGlobalVariableSuccess() {
        // Arrange
        CreateGlobalVariable.Arg arg = new CreateGlobalVariable.Arg();
        arg.setJson_data(JSON_DATA);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        
        when(globalVarService.create(any(IGlobalVariableDescribe.class)))
                .thenReturn(globalVariableResult);

        // Act
        CreateGlobalVariable.Result result = globalVariableService.createGlobalVarialbe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).create(any(IGlobalVariableDescribe.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建全局变量失败的场景
     */
    @Test
    @DisplayName("测试创建全局变量失败")
    void testCreateGlobalVariableFailed() {
        // Arrange
        CreateGlobalVariable.Arg arg = new CreateGlobalVariable.Arg();
        arg.setJson_data(JSON_DATA);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(false);
        
        when(globalVarService.create(any(IGlobalVariableDescribe.class)))
                .thenReturn(globalVariableResult);

        // Act
        CreateGlobalVariable.Result result = globalVariableService.createGlobalVarialbe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(globalVarService).create(any(IGlobalVariableDescribe.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新全局变量成功的场景
     */
    @Test
    @DisplayName("测试更新全局变量成功")
    void testUpdateGlobalVariableSuccess() {
        // Arrange
        UpdateGlobalVariable.Arg arg = new UpdateGlobalVariable.Arg();
        arg.setJson_data(JSON_DATA);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        
        when(globalVarService.update(any(IGlobalVariableDescribe.class)))
                .thenReturn(globalVariableResult);

        // Act
        UpdateGlobalVariable.Result result = globalVariableService.updateGlobalVariable(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).update(any(IGlobalVariableDescribe.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除全局变量成功的场景
     */
    @Test
    @DisplayName("测试删除全局变量成功")
    void testDeleteGlobalVariableSuccess() {
        // Arrange
        DeleteGlobalVariable.Arg arg = new DeleteGlobalVariable.Arg();
        arg.setApiName(API_NAME);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        
        when(globalVarService.delete(eq(API_NAME), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        DeleteGlobalVariable.Result result = globalVariableService.deleteGloableVarialbe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).delete(eq(API_NAME), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量列表成功的场景
     */
    @Test
    @DisplayName("测试获取全局变量列表成功")
    void testGetGlobalVariableListSuccess() {
        // Arrange
        GetGlobalVariableList.Arg arg = new GetGlobalVariableList.Arg();
        arg.setLabel("测试");
        arg.setRealTimeTrans(true);
        
        List<IGlobalVariableDescribe> variableList = Lists.newArrayList();
        IGlobalVariableDescribe variable = mock(IGlobalVariableDescribe.class);
        variableList.add(variable);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        globalVariableResult.setGlobalVariableList(variableList);
        
        when(globalVarService.findGlobalVariableList(eq("测试"), eq(true), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        GetGlobalVariableList.Result result = globalVariableService.getGlobalVariableList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getGlobalVariableList());
        assertEquals(1, result.getGlobalVariableList().size());
        verify(globalVarService).findGlobalVariableList(eq("测试"), eq(true), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量详情成功的场景
     */
    @Test
    @DisplayName("测试获取全局变量详情成功")
    void testGetGlobalVariableDetailSuccess() {
        // Arrange
        GetGloableVariableDetail.Arg arg = new GetGloableVariableDetail.Arg();
        arg.setApiName(API_NAME);
        arg.setRealTimeTrans(true);

        GlobalVariableDescribe variable = new GlobalVariableDescribe();
        variable.setApiName(API_NAME);
        variable.setLabel("测试变量");

        when(globalVarService.findGlobalVariableInfo(eq(API_NAME), eq(true), isNull(), eq(TENANT_ID)))
                .thenReturn(variable);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetail(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).findGlobalVariableInfo(eq(API_NAME), eq(true), isNull(), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量详情指定语言成功的场景
     */
    @Test
    @DisplayName("测试获取全局变量详情指定语言成功")
    void testGetGlobalVariableDetailAssignLangSuccess() {
        // Arrange
        GetGlobalVariableDetailAssignLang.Arg arg = new GetGlobalVariableDetailAssignLang.Arg();
        arg.setApiName(API_NAME);
        arg.setRealTimeTrans(true);
        arg.setLang("zh_CN");

        GlobalVariableDescribe variable = new GlobalVariableDescribe();
        variable.setApiName(API_NAME);
        variable.setLabel("测试变量");

        when(globalVarService.findGlobalVariableInfo(eq(API_NAME), eq(true), eq("zh_CN"), eq(TENANT_ID)))
                .thenReturn(variable);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetailAssignLang(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).findGlobalVariableInfo(eq(API_NAME), eq(true), eq("zh_CN"), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量详情返回null的场景
     */
    @Test
    @DisplayName("测试获取全局变量详情返回null")
    void testGetGlobalVariableDetailReturnsNull() {
        // Arrange
        GetGloableVariableDetail.Arg arg = new GetGloableVariableDetail.Arg();
        arg.setApiName(API_NAME);
        arg.setRealTimeTrans(true);
        
        when(globalVarService.findGlobalVariableInfo(eq(API_NAME), eq(true), isNull(), eq(TENANT_ID)))
                .thenReturn(null);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetail(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).findGlobalVariableInfo(eq(API_NAME), eq(true), isNull(), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量列表返回空列表的场景
     */
    @Test
    @DisplayName("测试获取全局变量列表返回空列表")
    void testGetGlobalVariableListReturnsEmptyList() {
        // Arrange
        GetGlobalVariableList.Arg arg = new GetGlobalVariableList.Arg();
        arg.setLabel("");
        arg.setRealTimeTrans(false);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        globalVariableResult.setGlobalVariableList(Lists.newArrayList());
        
        when(globalVarService.findGlobalVariableList(eq(""), eq(false), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        GetGlobalVariableList.Result result = globalVariableService.getGlobalVariableList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getGlobalVariableList());
        assertTrue(result.getGlobalVariableList().isEmpty());
        verify(globalVarService).findGlobalVariableList(eq(""), eq(false), eq(TENANT_ID));
    }

    /**
     * 测试创建全局变量参数为空的场景
     */
    @Test
    @DisplayName("测试创建全局变量参数为空")
    void testCreateGlobalVariableWithNullData() {
        // Arrange
        CreateGlobalVariable.Arg arg = new CreateGlobalVariable.Arg();
        arg.setJson_data(null);

        // Act & Assert - 期望抛出异常，因为json_data为null
        assertThrows(NullPointerException.class, () -> {
            globalVariableService.createGlobalVarialbe(arg, serviceContext);
        });
    }

    // ==================== 更新全局变量的边界条件测试 ====================

    /**
     * 测试更新全局变量失败的场景
     */
    @Test
    @DisplayName("测试更新全局变量失败")
    void testUpdateGlobalVariableFailed() {
        // Arrange
        UpdateGlobalVariable.Arg arg = new UpdateGlobalVariable.Arg();
        arg.setJson_data(JSON_DATA);

        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(false);

        when(globalVarService.update(any(IGlobalVariableDescribe.class)))
                .thenReturn(globalVariableResult);

        // Act
        UpdateGlobalVariable.Result result = globalVariableService.updateGlobalVariable(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(globalVarService).update(any(IGlobalVariableDescribe.class));
    }

    /**
     * 测试更新全局变量参数为空的场景
     */
    @Test
    @DisplayName("测试更新全局变量参数为空")
    void testUpdateGlobalVariableWithNullData() {
        // Arrange
        UpdateGlobalVariable.Arg arg = new UpdateGlobalVariable.Arg();
        arg.setJson_data(null);

        // Act & Assert - 期望抛出异常，因为json_data为null
        assertThrows(NullPointerException.class, () -> {
            globalVariableService.updateGlobalVariable(arg, serviceContext);
        });
    }

    // ==================== 删除全局变量的边界条件测试 ====================

    /**
     * 测试删除全局变量失败的场景
     */
    @Test
    @DisplayName("测试删除全局变量失败")
    void testDeleteGlobalVariableFailed() {
        // Arrange
        DeleteGlobalVariable.Arg arg = new DeleteGlobalVariable.Arg();
        arg.setApiName(API_NAME);

        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(false);

        when(globalVarService.delete(eq(API_NAME), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        DeleteGlobalVariable.Result result = globalVariableService.deleteGloableVarialbe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(globalVarService).delete(eq(API_NAME), eq(TENANT_ID));
    }

    /**
     * 测试删除全局变量参数为空的场景
     */
    @Test
    @DisplayName("测试删除全局变量参数为空")
    void testDeleteGlobalVariableWithNullApiName() {
        // Arrange
        DeleteGlobalVariable.Arg arg = new DeleteGlobalVariable.Arg();
        arg.setApiName(null);

        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(false);

        when(globalVarService.delete(eq(null), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        DeleteGlobalVariable.Result result = globalVariableService.deleteGloableVarialbe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(globalVarService).delete(eq(null), eq(TENANT_ID));
    }

    // ==================== 获取全局变量列表的边界条件测试 ====================

    /**
     * 测试获取全局变量列表失败的场景
     */
    @Test
    @DisplayName("测试获取全局变量列表失败")
    void testGetGlobalVariableListFailed() {
        // Arrange
        GetGlobalVariableList.Arg arg = new GetGlobalVariableList.Arg();
        arg.setLabel("测试");
        arg.setRealTimeTrans(true);

        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(false);
        globalVariableResult.setGlobalVariableList(null);

        when(globalVarService.findGlobalVariableList(eq("测试"), eq(true), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        GetGlobalVariableList.Result result = globalVariableService.getGlobalVariableList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNull(result.getGlobalVariableList());
        verify(globalVarService).findGlobalVariableList(eq("测试"), eq(true), eq(TENANT_ID));
    }

    /**
     * 测试获取全局变量列表参数为null的场景
     */
    @Test
    @DisplayName("测试获取全局变量列表参数为null")
    void testGetGlobalVariableListWithNullParams() {
        // Arrange
        GetGlobalVariableList.Arg arg = new GetGlobalVariableList.Arg();
        arg.setLabel(null);
        arg.setRealTimeTrans(null);

        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        globalVariableResult.setGlobalVariableList(Lists.newArrayList());

        when(globalVarService.findGlobalVariableList(eq(null), eq(null), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        GetGlobalVariableList.Result result = globalVariableService.getGlobalVariableList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getGlobalVariableList());
        assertTrue(result.getGlobalVariableList().isEmpty());
        verify(globalVarService).findGlobalVariableList(eq(null), eq(null), eq(TENANT_ID));
    }

    // ==================== 获取全局变量详情的边界条件测试 ====================

    /**
     * 测试获取全局变量详情参数为null的场景
     */
    @Test
    @DisplayName("测试获取全局变量详情参数为null")
    void testGetGlobalVariableDetailWithNullParams() {
        // Arrange
        GetGloableVariableDetail.Arg arg = new GetGloableVariableDetail.Arg();
        arg.setApiName(null);
        arg.setRealTimeTrans(null);

        when(globalVarService.findGlobalVariableInfo(eq(null), eq(null), isNull(), eq(TENANT_ID)))
                .thenReturn(null);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetail(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getGlobal_Variable());
        verify(globalVarService).findGlobalVariableInfo(eq(null), eq(null), isNull(), eq(TENANT_ID));
    }

    /**
     * 测试获取全局变量详情指定语言参数为null的场景
     */
    @Test
    @DisplayName("测试获取全局变量详情指定语言参数为null")
    void testGetGlobalVariableDetailAssignLangWithNullParams() {
        // Arrange
        GetGlobalVariableDetailAssignLang.Arg arg = new GetGlobalVariableDetailAssignLang.Arg();
        arg.setApiName(null);
        arg.setRealTimeTrans(null);
        arg.setLang(null);

        when(globalVarService.findGlobalVariableInfo(eq(null), eq(null), eq(null), eq(TENANT_ID)))
                .thenReturn(null);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetailAssignLang(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getGlobal_Variable());
        verify(globalVarService).findGlobalVariableInfo(eq(null), eq(null), eq(null), eq(TENANT_ID));
    }

    // ==================== 方法调用关系测试 ====================

    /**
     * 测试 getGlobalVariableDetail 方法内部调用 getGlobalVariableDetailAssignLang 的逻辑
     */
    @Test
    @DisplayName("测试getGlobalVariableDetail内部调用逻辑")
    void testGetGlobalVariableDetailInternalCall() {
        // Arrange
        GetGloableVariableDetail.Arg arg = new GetGloableVariableDetail.Arg();
        arg.setApiName(API_NAME);
        arg.setRealTimeTrans(false);

        GlobalVariableDescribe variable = new GlobalVariableDescribe();
        variable.setApiName(API_NAME);
        variable.setLabel("测试变量");

        // 验证内部调用时传递的参数
        when(globalVarService.findGlobalVariableInfo(eq(API_NAME), eq(false), isNull(), eq(TENANT_ID)))
                .thenReturn(variable);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetail(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        // 验证内部调用了正确的方法，且参数传递正确
        verify(globalVarService).findGlobalVariableInfo(eq(API_NAME), eq(false), isNull(), eq(TENANT_ID));
    }

    /**
     * 测试 getGlobalVariableDetailAssignLang 方法返回有数据的场景
     */
    @Test
    @DisplayName("测试获取全局变量详情指定语言返回有数据")
    void testGetGlobalVariableDetailAssignLangWithData() {
        // Arrange
        GetGlobalVariableDetailAssignLang.Arg arg = GetGlobalVariableDetailAssignLang.Arg.builder()
                .apiName(API_NAME)
                .realTimeTrans(true)
                .lang("en_US")
                .build();

        GlobalVariableDescribe variable = new GlobalVariableDescribe();
        variable.setApiName(API_NAME);
        variable.setLabel("Test Variable");

        when(globalVarService.findGlobalVariableInfo(eq(API_NAME), eq(true), eq("en_US"), eq(TENANT_ID)))
                .thenReturn(variable);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetailAssignLang(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getGlobal_Variable());
        verify(globalVarService).findGlobalVariableInfo(eq(API_NAME), eq(true), eq("en_US"), eq(TENANT_ID));
    }

    // ==================== 基础功能测试 ====================

    /**
     * 测试服务实例化和依赖注入是否正确
     */
    @Test
    @DisplayName("测试服务实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(globalVariableService);
        assertNotNull(globalVarService);
    }

    /**
     * 测试 ServiceContext 构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("global_variable", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }

    /**
     * 测试常量定义是否正确
     */
    @Test
    @DisplayName("测试常量定义正确")
    void testConstantsDefinition() {
        // Assert
        assertEquals("74255", TENANT_ID);
        assertEquals("1000", USER_ID);
        assertEquals("testVariable", API_NAME);
        assertTrue(JSON_DATA.contains("testVariable"));
        assertTrue(JSON_DATA.contains("测试变量"));
    }
}
