package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.button.GetSpecialButtons;
import com.facishare.paas.appframework.metadata.button.SpecialButtonManager;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectSpecialButtonProviderService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectSpecialButtonProviderService单元测试")
class ObjectSpecialButtonProviderServiceTest {

    @Mock
    private SpecialButtonManager providerManager;
    
    @Mock
    private SpecialButtonProvider specialButtonProvider;
    
    @InjectMocks
    private ObjectSpecialButtonProviderService objectSpecialButtonProviderService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "SpecialButtonProvider", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取特殊按钮成功的场景
     */
    @Test
    @DisplayName("测试getSpecialButtons成功")
    void testGetSpecialButtonsSuccess() {
        // Arrange
        GetSpecialButtons.Arg arg = new GetSpecialButtons.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // 使用真实的Button对象而不是Mock，避免ClassCastException
        com.facishare.paas.metadata.impl.ui.layout.Button button1 = new com.facishare.paas.metadata.impl.ui.layout.Button();
        button1.setName("button1");
        button1.setLabel("Button 1");
        button1.setAction("action1");
        button1.setActionType("default");
        com.facishare.paas.metadata.impl.ui.layout.Button button2 = new com.facishare.paas.metadata.impl.ui.layout.Button();
        button2.setName("button2");
        button2.setLabel("Button 2");
        button2.setAction("action2");
        button2.setActionType("default");
        List<IButton> mockButtons = Arrays.asList(button1, button2);

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(specialButtonProvider);
        when(specialButtonProvider.getSpecialButtons()).thenReturn(mockButtons);

        // Act
        GetSpecialButtons.Result result = objectSpecialButtonProviderService.getSpecialButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(specialButtonProvider).getSpecialButtons();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取特殊按钮时返回空列表的场景
     */
    @Test
    @DisplayName("测试getSpecialButtons返回空列表")
    void testGetSpecialButtonsReturnsEmptyList() {
        // Arrange
        GetSpecialButtons.Arg arg = new GetSpecialButtons.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(specialButtonProvider);
        when(specialButtonProvider.getSpecialButtons()).thenReturn(Arrays.asList());

        // Act
        GetSpecialButtons.Result result = objectSpecialButtonProviderService.getSpecialButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(specialButtonProvider).getSpecialButtons();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取特殊按钮时返回null的场景
     */
    @Test
    @DisplayName("测试getSpecialButtons返回null")
    void testGetSpecialButtonsReturnsNull() {
        // Arrange
        GetSpecialButtons.Arg arg = new GetSpecialButtons.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(specialButtonProvider);
        when(specialButtonProvider.getSpecialButtons()).thenReturn(null);

        // Act
        GetSpecialButtons.Result result = objectSpecialButtonProviderService.getSpecialButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(specialButtonProvider).getSpecialButtons();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取特殊按钮时Provider为null的场景
     */
    @Test
    @DisplayName("测试getSpecialButtons时Provider为null")
    void testGetSpecialButtonsWithNullProvider() {
        // Arrange
        GetSpecialButtons.Arg arg = new GetSpecialButtons.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(null);

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            objectSpecialButtonProviderService.getSpecialButtons(arg, serviceContext);
        });

        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取特殊按钮时参数为空的场景
     */
    @Test
    @DisplayName("测试getSpecialButtons参数为空")
    void testGetSpecialButtonsWithEmptyDescribeApiName() {
        // Arrange
        GetSpecialButtons.Arg arg = new GetSpecialButtons.Arg();
        arg.setDescribeApiName("");

        when(providerManager.getLocalProvider("")).thenReturn(specialButtonProvider);
        when(specialButtonProvider.getSpecialButtons()).thenReturn(Arrays.asList());

        // Act
        GetSpecialButtons.Result result = objectSpecialButtonProviderService.getSpecialButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(providerManager).getLocalProvider("");
        verify(specialButtonProvider).getSpecialButtons();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取特殊按钮时参数为null的场景
     */
    @Test
    @DisplayName("测试getSpecialButtons参数为null")
    void testGetSpecialButtonsWithNullDescribeApiName() {
        // Arrange
        GetSpecialButtons.Arg arg = new GetSpecialButtons.Arg();
        arg.setDescribeApiName(null);

        when(providerManager.getLocalProvider(null)).thenReturn(specialButtonProvider);
        when(specialButtonProvider.getSpecialButtons()).thenReturn(Arrays.asList());

        // Act
        GetSpecialButtons.Result result = objectSpecialButtonProviderService.getSpecialButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(providerManager).getLocalProvider(null);
        verify(specialButtonProvider).getSpecialButtons();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取特殊按钮时包含多种按钮类型的场景
     */
    @Test
    @DisplayName("测试getSpecialButtons包含多种按钮类型")
    void testGetSpecialButtonsWithMultipleButtonTypes() {
        // Arrange
        GetSpecialButtons.Arg arg = new GetSpecialButtons.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // 使用真实的Button对象而不是Mock，避免ClassCastException
        com.facishare.paas.metadata.impl.ui.layout.Button editButton = new com.facishare.paas.metadata.impl.ui.layout.Button();
        editButton.setName("edit");
        editButton.setLabel("Edit Button");
        editButton.setAction("edit");
        editButton.setActionType("default");

        com.facishare.paas.metadata.impl.ui.layout.Button deleteButton = new com.facishare.paas.metadata.impl.ui.layout.Button();
        deleteButton.setName("delete");
        deleteButton.setLabel("Delete Button");
        deleteButton.setAction("delete");
        deleteButton.setActionType("default");

        com.facishare.paas.metadata.impl.ui.layout.Button customButton = new com.facishare.paas.metadata.impl.ui.layout.Button();
        customButton.setName("custom");
        customButton.setLabel("Custom Button");
        customButton.setAction("custom");
        customButton.setActionType("default");

        List<IButton> mockButtons = Arrays.asList(editButton, deleteButton, customButton);

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(specialButtonProvider);
        when(specialButtonProvider.getSpecialButtons()).thenReturn(mockButtons);

        // Act
        GetSpecialButtons.Result result = objectSpecialButtonProviderService.getSpecialButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(specialButtonProvider).getSpecialButtons();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectSpecialButtonProviderService);
        assertNotNull(providerManager);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("SpecialButtonProvider", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Assert
        assertNotNull(user);
        assertEquals(TENANT_ID, user.getTenantId());
        assertEquals(USER_ID, user.getUserId());
    }
}
