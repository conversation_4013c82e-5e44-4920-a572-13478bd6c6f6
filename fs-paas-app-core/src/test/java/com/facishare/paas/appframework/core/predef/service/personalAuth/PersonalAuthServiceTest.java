package com.facishare.paas.appframework.core.predef.service.personalAuth;

import com.beust.jcommander.internal.Lists;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.predef.service.BaseServiceTest;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginAppType;
import com.facishare.paas.appframework.core.predef.service.dto.personalAuth.*;
import com.facishare.paas.appframework.metadata.FileStoreService;
import com.facishare.paas.appframework.metadata.onlinedoc.PackagePluginLogicService;
import com.facishare.paas.appframework.metadata.personalAuth.PersonalAuthLogicService;
import com.facishare.paas.appframework.metadata.personalAuth.model.ActionCallback;
import com.facishare.paas.appframework.metadata.personalAuth.model.GetPersonalAuthUrl;
import com.facishare.paas.appframework.metadata.repository.model.PackagePluginEntity;
import com.facishare.paas.appframework.metadata.repository.model.PersonalAuthEntity;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * PersonalAuthService单元测试类
 * 测试个人授权服务的所有@ServiceMethod方法
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("PersonalAuthService单元测试")
public class PersonalAuthServiceTest extends BaseServiceTest {

    @Mock
    private PersonalAuthLogicService personalAuthLogicService;

    @Mock
    private PackagePluginLogicService packagePluginLogicService;

    @Mock
    private FileStoreService fileStoreService;

    @InjectMocks
    private PersonalAuthService personalAuthService;

    private static final String PLUGIN_API_NAME = "test_plugin";
    private static final String APP_TYPE = PackagePluginAppType.ONLINE_DOC.getType(); // 使用正确的枚举值
    private static final String AUTH_CODE = "test_auth_code";
    private static final String AUTH_URL = "https://auth.example.com";
    private static final String ICON_URL = "https://icon.example.com/icon.png";
    private static final String PLUGIN_NAME = "Test Plugin";
    private static final Long CREATE_TIME = 1624512345L;
    private static final Long EXPIRED_TIME = System.currentTimeMillis() + 3600000L; // 1小时后过期
    private static final Long EXPIRED_TIME_SOON = System.currentTimeMillis() + 10000L; // 10秒后过期

    @Override
    protected String getServiceName() {
        return "personalAuth";
    }

    @BeforeEach
    void setUp() {
        // 基础设置已在BaseServiceTest中完成
    }

    @Test
    @DisplayName("GenerateByAI - 测试getAllAuth成功场景")
    void testGetAllAuth_Success() {
        // Arrange
        GetAllAuthVo.Arg arg = new GetAllAuthVo.Arg();
        arg.setPageNumber(0);
        arg.setPageSize(10);

        // Mock配置数据
        Map<String, Object> pluginConfig = new HashMap<String, Object>();
        pluginConfig.put("package_app_types", Lists.newArrayList(APP_TYPE));

        // Mock插件实体
        PackagePluginEntity pluginEntity = new PackagePluginEntity();
        pluginEntity.setPluginApiName(PLUGIN_API_NAME);
        pluginEntity.setName(PLUGIN_NAME);
        pluginEntity.setIcon("icon.png");
        pluginEntity.setIconSign("sign123");
        pluginEntity.setAppType(APP_TYPE);
        pluginEntity.setDefineType("system");
        pluginEntity.setExtraInfo(Maps.newHashMap());

        // Mock授权实体
        PersonalAuthEntity authEntity = new PersonalAuthEntity();
        authEntity.setPluginApiName(PLUGIN_API_NAME);
        authEntity.setAppType(APP_TYPE);
        authEntity.setCreateTime(CREATE_TIME);
        authEntity.setExpiredTime(EXPIRED_TIME);

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(AppFrameworkConfig::getPluginConfig).thenReturn(pluginConfig);

            when(packagePluginLogicService.getPluginsByAppTypes(testUser, Lists.newArrayList(APP_TYPE)))
                    .thenReturn(Lists.newArrayList(pluginEntity));
            when(personalAuthLogicService.getAllAuth(testUser, 0, 11))
                    .thenReturn(Lists.newArrayList(authEntity));
            when(packagePluginLogicService.getPluginsByApiNames(testUser, Lists.newArrayList(PLUGIN_API_NAME)))
                    .thenReturn(Lists.newArrayList(pluginEntity));
            when(fileStoreService.generateCpathAccessUrl(testUser, "icon.png", "sign123"))
                    .thenReturn(ICON_URL);

            // Act
            GetAllAuthVo.Result result = personalAuthService.getAllAuth(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertFalse(result.isHasMore());
            assertNotNull(result.getAuthList());
            assertEquals(1, result.getAuthList().size());

            GetAllAuthVo.AuthItem authItem = result.getAuthList().get(0);
            assertEquals(APP_TYPE, authItem.getAppType());
            assertEquals(PLUGIN_API_NAME, authItem.getPluginApiName());
            assertEquals(CREATE_TIME, authItem.getCreateTime());
            assertEquals(EXPIRED_TIME, authItem.getExpiredTime());
            assertEquals("icon.png", authItem.getIcon());
            assertEquals(ICON_URL, authItem.getIconUrl());

            assertNotNull(result.getPluginList());
            assertTrue(result.getPluginList().isEmpty()); // 因为插件已授权，所以未授权列表为空

            verify(personalAuthLogicService, times(1)).getAllAuth(testUser, 0, 11);
            verify(packagePluginLogicService, times(1)).getPluginsByAppTypes(testUser, Lists.newArrayList(APP_TYPE));
            verify(packagePluginLogicService, times(1)).getPluginsByApiNames(testUser, Lists.newArrayList(PLUGIN_API_NAME));
        }
    }

    @Test
    @DisplayName("GenerateByAI - 测试getAllAuth空结果场景")
    void testGetAllAuth_EmptyResult() {
        // Arrange
        GetAllAuthVo.Arg arg = new GetAllAuthVo.Arg();
        arg.setPageNumber(0);
        arg.setPageSize(10);

        // Mock空配置
        Map<String, Object> pluginConfig = new HashMap<String, Object>();
        pluginConfig.put("package_app_types", Lists.newArrayList());

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(AppFrameworkConfig::getPluginConfig).thenReturn(pluginConfig);

            when(personalAuthLogicService.getAllAuth(testUser, 0, 11))
                    .thenReturn(Lists.newArrayList());

            // Act
            GetAllAuthVo.Result result = personalAuthService.getAllAuth(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertFalse(result.isHasMore());
            assertNotNull(result.getAuthList());
            assertTrue(result.getAuthList().isEmpty());
            assertNotNull(result.getPluginList());
            assertTrue(result.getPluginList().isEmpty());

            verify(personalAuthLogicService, times(1)).getAllAuth(testUser, 0, 11);
            verify(packagePluginLogicService, never()).getPluginsByAppTypes(any(), any());
        }
    }

    @Test
    @DisplayName("GenerateByAI - 测试checkAuth有权限场景")
    void testCheckAuth_HasAuth() {
        // Arrange
        CheckAuthVo.Arg arg = new CheckAuthVo.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);

        // Mock有效的授权实体
        PersonalAuthEntity authEntity = new PersonalAuthEntity();
        authEntity.setPluginApiName(PLUGIN_API_NAME);
        authEntity.setAppType(APP_TYPE);
        authEntity.setExpiredTime(EXPIRED_TIME); // 远期过期时间

        when(personalAuthLogicService.getCurrentPersonalAuth(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME))
                .thenReturn(authEntity);

        // Act
        CheckAuthVo.Result result = personalAuthService.checkAuth(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isHasAuth());
        assertNull(result.getAuthUrl());

        verify(personalAuthLogicService, times(1)).getCurrentPersonalAuth(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME);
        verify(personalAuthLogicService, never()).getAuthUrl(any(), any(), any());
    }

    @Test
    @DisplayName("GenerateByAI - 测试checkAuth无权限场景")
    void testCheckAuth_NoAuth() {
        // Arrange
        CheckAuthVo.Arg arg = new CheckAuthVo.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);

        // Mock授权URL结果
        GetPersonalAuthUrl.Result urlResult = new GetPersonalAuthUrl.Result();
        urlResult.setAuthUrl(AUTH_URL);

        when(personalAuthLogicService.getCurrentPersonalAuth(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME))
                .thenReturn(null); // 无授权记录
        when(personalAuthLogicService.getAuthUrl(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME))
                .thenReturn(urlResult);

        // Act
        CheckAuthVo.Result result = personalAuthService.checkAuth(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isHasAuth());
        assertEquals(AUTH_URL, result.getAuthUrl());

        verify(personalAuthLogicService, times(1)).getCurrentPersonalAuth(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME);
        verify(personalAuthLogicService, times(1)).getAuthUrl(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试checkAuth权限即将过期场景")
    void testCheckAuth_AuthExpiringSoon() {
        // Arrange
        CheckAuthVo.Arg arg = new CheckAuthVo.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);

        // Mock即将过期的授权实体
        PersonalAuthEntity authEntity = new PersonalAuthEntity();
        authEntity.setPluginApiName(PLUGIN_API_NAME);
        authEntity.setAppType(APP_TYPE);
        authEntity.setExpiredTime(EXPIRED_TIME_SOON); // 即将过期

        // Mock授权URL结果
        GetPersonalAuthUrl.Result urlResult = new GetPersonalAuthUrl.Result();
        urlResult.setAuthUrl(AUTH_URL);

        when(personalAuthLogicService.getCurrentPersonalAuth(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME))
                .thenReturn(authEntity);
        when(personalAuthLogicService.getAuthUrl(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME))
                .thenReturn(urlResult);

        // Act
        CheckAuthVo.Result result = personalAuthService.checkAuth(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isHasAuth());
        assertEquals(AUTH_URL, result.getAuthUrl());

        verify(personalAuthLogicService, times(1)).getCurrentPersonalAuth(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME);
        verify(personalAuthLogicService, times(1)).getAuthUrl(
                testUser, PackagePluginAppType.ONLINE_DOC.getType(), PLUGIN_API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试addAuth成功场景")
    void testAddAuth_Success() {
        // Arrange
        AddAuthVo.Arg arg = new AddAuthVo.Arg();
        arg.setAppType(APP_TYPE);
        arg.setPluginApiName(PLUGIN_API_NAME);
        arg.setExpiredTime(EXPIRED_TIME);

        Map<String, Object> runtimeData = new HashMap<String, Object>();
        runtimeData.put("token", "test_token");
        arg.setRuntimeData(runtimeData);

        // Mock返回的授权实体
        PersonalAuthEntity authEntity = new PersonalAuthEntity();
        authEntity.setPluginApiName(PLUGIN_API_NAME);
        authEntity.setAppType(APP_TYPE);

        when(personalAuthLogicService.addAuth(testUser, APP_TYPE, PLUGIN_API_NAME, runtimeData, EXPIRED_TIME))
                .thenReturn(authEntity);

        // Act
        AddAuthVo.Result result = personalAuthService.addAuth(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());

        verify(personalAuthLogicService, times(1)).addAuth(testUser, APP_TYPE, PLUGIN_API_NAME, runtimeData, EXPIRED_TIME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试deleteAuth成功场景")
    void testDeleteAuth_Success() {
        // Arrange
        DeleteAuthVo.Arg arg = new DeleteAuthVo.Arg();
        arg.setAppType(APP_TYPE);
        arg.setPluginApiName(PLUGIN_API_NAME);

        // Mock返回的授权实体
        PersonalAuthEntity authEntity = new PersonalAuthEntity();
        authEntity.setPluginApiName(PLUGIN_API_NAME);
        authEntity.setAppType(APP_TYPE);

        when(personalAuthLogicService.deleteAuth(testUser, APP_TYPE, PLUGIN_API_NAME))
                .thenReturn(authEntity);

        // Act
        DeleteAuthVo.Result result = personalAuthService.deleteAuth(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());

        verify(personalAuthLogicService, times(1)).deleteAuth(testUser, APP_TYPE, PLUGIN_API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试getAuthUrl成功场景")
    void testGetAuthUrl_Success() {
        // Arrange
        GetPersonalAuthUrlVo.Arg arg = new GetPersonalAuthUrlVo.Arg();
        arg.setAppType(APP_TYPE);
        arg.setPluginApiName(PLUGIN_API_NAME);

        // Mock授权URL结果
        GetPersonalAuthUrl.Result urlResult = new GetPersonalAuthUrl.Result();
        urlResult.setAuthUrl(AUTH_URL);

        when(personalAuthLogicService.getAuthUrl(testUser, APP_TYPE, PLUGIN_API_NAME))
                .thenReturn(urlResult);

        // Act
        GetPersonalAuthUrlVo.Result result = personalAuthService.getAuthUrl(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(AUTH_URL, result.getAuthUrl());

        verify(personalAuthLogicService, times(1)).getAuthUrl(testUser, APP_TYPE, PLUGIN_API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试actionCallback成功场景")
    void testActionCallback_Success() {
        // Arrange
        ActionCallbackVo.Arg arg = new ActionCallbackVo.Arg();
        arg.setAppType(APP_TYPE);
        arg.setPluginApiName(PLUGIN_API_NAME);
        arg.setCode(AUTH_CODE);

        // Mock回调结果
        ActionCallback.Result actionResult = new ActionCallback.Result();
        actionResult.setErrorCode(0);
        actionResult.setErrorMessage("");

        when(personalAuthLogicService.actionCallback(testUser, APP_TYPE, PLUGIN_API_NAME, AUTH_CODE))
                .thenReturn(actionResult);

        // Act
        ActionCallbackVo.Result result = personalAuthService.actionCallback(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getErrorCode());
        assertEquals("", result.getErrorMessage());

        verify(personalAuthLogicService, times(1)).actionCallback(testUser, APP_TYPE, PLUGIN_API_NAME, AUTH_CODE);
    }

    @Test
    @DisplayName("GenerateByAI - 测试actionCallback失败场景")
    void testActionCallback_Failure() {
        // Arrange
        ActionCallbackVo.Arg arg = new ActionCallbackVo.Arg();
        arg.setAppType(APP_TYPE);
        arg.setPluginApiName(PLUGIN_API_NAME);
        arg.setCode(AUTH_CODE);

        // Mock回调失败结果
        ActionCallback.Result actionResult = new ActionCallback.Result();
        actionResult.setErrorCode(1001);
        actionResult.setErrorMessage("授权失败");

        when(personalAuthLogicService.actionCallback(testUser, APP_TYPE, PLUGIN_API_NAME, AUTH_CODE))
                .thenReturn(actionResult);

        // Act
        ActionCallbackVo.Result result = personalAuthService.actionCallback(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(1001, result.getErrorCode());
        assertEquals("授权失败", result.getErrorMessage());

        verify(personalAuthLogicService, times(1)).actionCallback(testUser, APP_TYPE, PLUGIN_API_NAME, AUTH_CODE);
    }
}
