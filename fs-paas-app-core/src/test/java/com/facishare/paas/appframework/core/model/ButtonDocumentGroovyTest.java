package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.UdefButton;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ButtonDocumentGroovyTest {

    @BeforeAll
    static void setupSpec() throws Exception {
        // 创建 mock 实例
        I18nClient i18nClient = mock(I18nClient.class);
        I18nServiceImpl i18nServiceImpl = mock(I18nServiceImpl.class);

        // 给 mock 设置返回值
        when(i18nClient.getAllLanguage()).thenReturn(java.util.Collections.emptyList());

        // 设置内部字段 impl
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl);

        // 设置 SINGLETON
        Whitebox.setInternalState(I18nClient.class, "SINGLETON", i18nClient);
    }

    @Test
    void testOfMethodWithNullButton() {
        // When: 使用null作为button参数
        ButtonDocument result = ButtonDocument.of(null, true, false);

        // Then: 验证结果为null
        assertNull(result);
    }

    @Test
    void testOfMethodWithEmptyApiName() {
        // Given: 准备一个apiName为空的button
        UdefButton button = new UdefButton();
        button.setApiName("");

        // Mock ObjectAction
        try (MockedStatic<ObjectAction> mockedObjectAction = mockStatic(ObjectAction.class)) {
            mockedObjectAction.when(() -> ObjectAction.getActionCodeByButtonApiName("")).thenReturn("");

            // When: 调用被测试方法
            ButtonDocument result = ButtonDocument.of(button, true, false);

            // Then: 验证结果
            assertNotNull(result);
            assertEquals("", result.get("api_name"));
            assertEquals("", result.get("action"));
        }
    }

    @Test
    void testOfMethodWithIncludeActionCodeFalse() {
        // Given: 准备一个UdefButton对象
        UdefButton button = new UdefButton();
        button.setApiName("test_button");

        // When: 调用of方法，includeActionCode为false
        ButtonDocument result = ButtonDocument.of(button, false, false);

        // Then: 验证结果不包含actionCode
        assertNotNull(result);
        assertEquals("test_button", result.get("api_name"));
        assertFalse(result.containsKey("action"));
    }
}
