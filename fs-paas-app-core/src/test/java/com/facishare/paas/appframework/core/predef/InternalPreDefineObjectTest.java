package com.facishare.paas.appframework.core.predef;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InternalPreDefineObjectTest {

  @BeforeEach
  void setUp() {
    // 测试前的准备工作
  }

  @Test
  void testGetApiName() {
    // Given & When & Then
    assertEquals("AsyncTaskMonitorObj", InternalPreDefineObject.AsyncTaskMonitorObj.getApiName());
  }

  @Test
  void testGetPackageName() {
    // Given & When
    String packageName = InternalPreDefineObject.AsyncTaskMonitorObj.getPackageName();

    // Then
    assertEquals("com.facishare.paas.appframework.core.predef", packageName);
  }

  @Test
  void testGetDefaultActionClassInfo() {
    // Given
    String actionCode = "Add";

    // When
    ActionClassInfo actionClassInfo = InternalPreDefineObject.AsyncTaskMonitorObj.getDefaultActionClassInfo(actionCode);

    // Then
    assertNotNull(actionClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.action.AsyncTaskMonitorObjAddAction", 
                 actionClassInfo.getClassName());
  }

  @Test
  void testGetControllerClassInfo() {
    // Given
    String methodName = "List";

    // When
    ControllerClassInfo controllerClassInfo = InternalPreDefineObject.AsyncTaskMonitorObj.getControllerClassInfo(methodName);

    // Then
    assertNotNull(controllerClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.controller.AsyncTaskMonitorObjListController", 
                 controllerClassInfo.getClassName());
  }

  @Test
  void testInit() {
    // Given
    try (MockedStatic<PreDefineObjectRegistry> mockedRegistry = mockStatic(PreDefineObjectRegistry.class)) {
      
      // When
      InternalPreDefineObject.init();

      // Then
      mockedRegistry.verify(() -> PreDefineObjectRegistry.register(InternalPreDefineObject.AsyncTaskMonitorObj));
    }
  }

  @Test
  void testEnumValues() {
    // Given & When
    InternalPreDefineObject[] values = InternalPreDefineObject.values();

    // Then
    assertEquals(1, values.length);
    assertEquals(InternalPreDefineObject.AsyncTaskMonitorObj, values[0]);
  }

  @Test
  void testValueOf() {
    // Given & When
    InternalPreDefineObject object = InternalPreDefineObject.valueOf("AsyncTaskMonitorObj");

    // Then
    assertEquals(InternalPreDefineObject.AsyncTaskMonitorObj, object);
  }

  @Test
  void testValueOfWithInvalidName() {
    // Given & When & Then
    assertThrows(IllegalArgumentException.class, () -> {
      InternalPreDefineObject.valueOf("InvalidName");
    });
  }

  @Test
  void testGetDefaultActionClassInfoWithDifferentActionCodes() {
    // Test with different action codes
    String[] actionCodes = {"Add", "Edit", "Delete", "View", "List"};
    
    for (String actionCode : actionCodes) {
      // When
      ActionClassInfo actionClassInfo = InternalPreDefineObject.AsyncTaskMonitorObj.getDefaultActionClassInfo(actionCode);
      
      // Then
      assertNotNull(actionClassInfo);
      String expectedClassName = "com.facishare.paas.appframework.core.predef.action.AsyncTaskMonitorObj" + actionCode + "Action";
      assertEquals(expectedClassName, actionClassInfo.getClassName());
    }
  }

  @Test
  void testGetControllerClassInfoWithDifferentMethodNames() {
    // Test with different method names
    String[] methodNames = {"List", "Add", "Edit", "Delete", "View"};
    
    for (String methodName : methodNames) {
      // When
      ControllerClassInfo controllerClassInfo = InternalPreDefineObject.AsyncTaskMonitorObj.getControllerClassInfo(methodName);
      
      // Then
      assertNotNull(controllerClassInfo);
      String expectedClassName = "com.facishare.paas.appframework.core.predef.controller.AsyncTaskMonitorObj" + methodName + "Controller";
      assertEquals(expectedClassName, controllerClassInfo.getClassName());
    }
  }

  @Test
  void testGetDefaultActionClassInfoWithNullActionCode() {
    // Given
    String actionCode = null;

    // When
    ActionClassInfo actionClassInfo = InternalPreDefineObject.AsyncTaskMonitorObj.getDefaultActionClassInfo(actionCode);

    // Then
    assertNotNull(actionClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.action.AsyncTaskMonitorObjnullAction", 
                 actionClassInfo.getClassName());
  }

  @Test
  void testGetControllerClassInfoWithNullMethodName() {
    // Given
    String methodName = null;

    // When
    ControllerClassInfo controllerClassInfo = InternalPreDefineObject.AsyncTaskMonitorObj.getControllerClassInfo(methodName);

    // Then
    assertNotNull(controllerClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.controller.AsyncTaskMonitorObjnullController", 
                 controllerClassInfo.getClassName());
  }

  @Test
  void testGetDefaultActionClassInfoWithEmptyActionCode() {
    // Given
    String actionCode = "";

    // When
    ActionClassInfo actionClassInfo = InternalPreDefineObject.AsyncTaskMonitorObj.getDefaultActionClassInfo(actionCode);

    // Then
    assertNotNull(actionClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.action.AsyncTaskMonitorObjAction", 
                 actionClassInfo.getClassName());
  }

  @Test
  void testGetControllerClassInfoWithEmptyMethodName() {
    // Given
    String methodName = "";

    // When
    ControllerClassInfo controllerClassInfo = InternalPreDefineObject.AsyncTaskMonitorObj.getControllerClassInfo(methodName);

    // Then
    assertNotNull(controllerClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.controller.AsyncTaskMonitorObjController", 
                 controllerClassInfo.getClassName());
  }
}
