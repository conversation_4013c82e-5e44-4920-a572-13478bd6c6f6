package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.UIEventDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.uievent.CreateUIEvent;
import com.facishare.paas.appframework.core.predef.service.dto.uievent.DeleteById;
import com.facishare.paas.appframework.core.predef.service.dto.uievent.FindByLayout;
import com.facishare.paas.appframework.core.predef.service.dto.uievent.FindByObject;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.UIEventLogicService;
import com.facishare.paas.metadata.api.IUIEvent;
import com.facishare.paas.metadata.impl.UIEvent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectUIEventService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectUIEventService单元测试")
class ObjectUIEventServiceTest {

    @Mock
    private LayoutLogicService layoutLogicService;
    
    @Mock
    private UIEventLogicService eventLogicService;
    
    @InjectMocks
    private ObjectUIEventService service;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String LAYOUT_API_NAME = "TestLayout";
    private final String DESCRIBE_API_NAME = "TestObj__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        serviceContext = mock(ServiceContext.class);
        when(serviceContext.getUser()).thenReturn(user);
        when(serviceContext.getTenantId()).thenReturn(TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createUIEvent方法 - 正常场景
     */
    @Test
    @DisplayName("测试createUIEvent方法 - 正常场景")
    void testCreateUIEvent_Success() {
        // Arrange
        CreateUIEvent.Arg arg = new CreateUIEvent.Arg();
        arg.setLayoutApiName(LAYOUT_API_NAME);
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        
        Map<String, Object> newEventMap = new HashMap<>();
        newEventMap.put("id", "event1");
        newEventMap.put("type", "click");
        UIEventDocument newEvent = UIEventDocument.of(newEventMap);
        arg.setEvents(Arrays.asList(newEvent));

        // Mock layout
        ILayout mockLayout = mock(ILayout.class);
        Map<String, Object> existingEvent = new HashMap<>();
        existingEvent.put("id", "event0");
        existingEvent.put("type", "load");
        List<Map<String, Object>> existingEvents = new ArrayList<>(Arrays.asList(existingEvent));
        when(mockLayout.getEvents()).thenReturn(existingEvents);
        
        ILayout updatedLayout = mock(ILayout.class);
        when(updatedLayout.getEvents()).thenReturn(Arrays.asList(existingEvent, newEvent));
        
        when(layoutLogicService.findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(mockLayout);
        when(layoutLogicService.updateLayout(eq(user), any(ILayout.class)))
                .thenReturn(updatedLayout);

        // Act
        CreateUIEvent.Result result = service.createUIEvent(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getEvents());
        assertEquals(2, result.getEvents().size());
        
        // 验证调用
        verify(layoutLogicService).findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME);
        verify(layoutLogicService).updateLayout(eq(user), any(ILayout.class));
        verify(mockLayout).setEvents(anyList());
        verify(mockLayout).set("is_deal_ui", true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试createUIEvent方法 - 空事件列表
     */
    @Test
    @DisplayName("测试createUIEvent方法 - 空事件列表")
    void testCreateUIEvent_EmptyEvents() {
        // Arrange
        CreateUIEvent.Arg arg = new CreateUIEvent.Arg();
        arg.setLayoutApiName(LAYOUT_API_NAME);
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setEvents(Collections.emptyList());

        // Mock layout
        ILayout mockLayout = mock(ILayout.class);
        List<Map<String, Object>> emptyEvents = new ArrayList<>();
        when(mockLayout.getEvents()).thenReturn(emptyEvents);
        
        ILayout updatedLayout = mock(ILayout.class);
        when(updatedLayout.getEvents()).thenReturn(Collections.emptyList());
        
        when(layoutLogicService.findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(mockLayout);
        when(layoutLogicService.updateLayout(eq(user), any(ILayout.class)))
                .thenReturn(updatedLayout);

        // Act
        CreateUIEvent.Result result = service.createUIEvent(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getEvents());
        assertTrue(result.getEvents().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findByLayout方法 - 正常场景
     */
    @Test
    @DisplayName("测试findByLayout方法 - 正常场景")
    void testFindByLayout_Success() {
        // Arrange
        FindByLayout.Arg arg = new FindByLayout.Arg();
        arg.setLayoutApiName(LAYOUT_API_NAME);
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // Mock layout
        ILayout mockLayout = mock(ILayout.class);
        Map<String, Object> event1 = new HashMap<>();
        event1.put("id", "event1");
        event1.put("type", "click");
        
        Map<String, Object> event2 = new HashMap<>();
        event2.put("id", "event2");
        event2.put("type", "load");
        
        when(mockLayout.getEvents()).thenReturn(Arrays.asList(event1, event2));
        when(layoutLogicService.findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(mockLayout);

        // Act
        FindByLayout.Result result = service.findByLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getEvents());
        assertEquals(2, result.getEvents().size());
        
        // 验证调用
        verify(layoutLogicService).findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findByLayout方法 - 空事件列表
     */
    @Test
    @DisplayName("测试findByLayout方法 - 空事件列表")
    void testFindByLayout_EmptyEvents() {
        // Arrange
        FindByLayout.Arg arg = new FindByLayout.Arg();
        arg.setLayoutApiName(LAYOUT_API_NAME);
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // Mock layout
        ILayout mockLayout = mock(ILayout.class);
        when(mockLayout.getEvents()).thenReturn(Collections.emptyList());
        when(layoutLogicService.findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(mockLayout);

        // Act
        FindByLayout.Result result = service.findByLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getEvents());
        assertTrue(result.getEvents().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteById方法 - 正常场景
     */
    @Test
    @DisplayName("测试deleteById方法 - 正常场景")
    void testDeleteById_Success() {
        // Arrange
        DeleteById.Arg arg = new DeleteById.Arg();
        arg.setLayoutApiName(LAYOUT_API_NAME);
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setEventIdList(new HashSet<>(Arrays.asList("event1")));

        // Mock layout
        ILayout mockLayout = mock(ILayout.class);
        Map<String, Object> event1 = new HashMap<>();
        event1.put(IUIEvent.ID, "event1");
        event1.put("type", "click");
        
        Map<String, Object> event2 = new HashMap<>();
        event2.put(IUIEvent.ID, "event2");
        event2.put("type", "load");
        
        List<Map<String, Object>> events = new ArrayList<>(Arrays.asList(event1, event2));
        when(mockLayout.getEvents()).thenReturn(events);
        when(layoutLogicService.findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(mockLayout);

        // Act
        DeleteById.Result result = service.findByLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证调用
        verify(layoutLogicService).findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME);
        verify(layoutLogicService).updateLayout(user, mockLayout);
        verify(mockLayout).setEvents(anyList());
        verify(mockLayout).set("is_deal_ui", true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteById方法 - 空ID列表
     */
    @Test
    @DisplayName("测试deleteById方法 - 空ID列表")
    void testDeleteById_EmptyIdList() {
        // Arrange
        DeleteById.Arg arg = new DeleteById.Arg();
        arg.setLayoutApiName(LAYOUT_API_NAME);
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setEventIdList(Collections.emptySet());

        // Mock layout
        ILayout mockLayout = mock(ILayout.class);
        Map<String, Object> event1 = new HashMap<>();
        event1.put(IUIEvent.ID, "event1");
        event1.put("type", "click");
        
        List<Map<String, Object>> events = new ArrayList<>(Arrays.asList(event1));
        when(mockLayout.getEvents()).thenReturn(events);
        when(layoutLogicService.findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(mockLayout);

        // Act
        DeleteById.Result result = service.findByLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证调用
        verify(layoutLogicService).findLayoutByApiName(user, LAYOUT_API_NAME, DESCRIBE_API_NAME);
        verify(layoutLogicService).updateLayout(user, mockLayout);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findByObject方法 - 正常场景
     */
    @Test
    @DisplayName("测试findByObject方法 - 正常场景")
    void testFindByObject_Success() {
        // Arrange
        FindByObject.Arg arg = new FindByObject.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        // Mock UI events
        IUIEvent event1 = new UIEvent();
        event1.setId("event1");
        event1.setType(1);
        
        IUIEvent event2 = new UIEvent();
        event2.setId("event2");
        event2.setType(2);
        
        when(eventLogicService.findEventByObject(DESCRIBE_API_NAME, TENANT_ID))
                .thenReturn(Arrays.asList(event1, event2));

        // Act
        FindByObject.Result result = service.findByObject(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getEvents());
        assertEquals(2, result.getEvents().size());
        
        // 验证调用
        verify(eventLogicService).findEventByObject(DESCRIBE_API_NAME, TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findByObject方法 - 空事件列表
     */
    @Test
    @DisplayName("测试findByObject方法 - 空事件列表")
    void testFindByObject_EmptyEvents() {
        // Arrange
        FindByObject.Arg arg = new FindByObject.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        when(eventLogicService.findEventByObject(DESCRIBE_API_NAME, TENANT_ID))
                .thenReturn(Collections.emptyList());

        // Act
        FindByObject.Result result = service.findByObject(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getEvents());
        assertTrue(result.getEvents().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(layoutLogicService);
        assertNotNull(eventLogicService);
    }
}
