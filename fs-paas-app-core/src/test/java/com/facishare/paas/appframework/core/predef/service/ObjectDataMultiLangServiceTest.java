package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.predef.service.dto.dataMultiLang.*;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.DataLanguageService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectDataMultiLangService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectDataMultiLangService单元测试")
class ObjectDataMultiLangServiceTest {

    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private DataLanguageService dataLanguageService;
    
    @InjectMocks
    private ObjectDataMultiLangService objectDataMultiLangService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "dataMultiLang", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建多语言字段成功的场景
     */
    @Test
    @DisplayName("测试create成功")
    void testCreateSuccess() {
        // Arrange
        ChangeMultiLanguageField.Arg arg = new ChangeMultiLanguageField.Arg();

        ChangeMultiLanguageField.MultiLanguageField field1 = new ChangeMultiLanguageField.MultiLanguageField();
        field1.setDescribeApiName("TestObj__c");
        field1.setEnableField(Arrays.asList("name"));

        ChangeMultiLanguageField.MultiLanguageField field2 = new ChangeMultiLanguageField.MultiLanguageField();
        field2.setDescribeApiName("TestObj__c");
        field2.setEnableField(Arrays.asList("description"));

        List<ChangeMultiLanguageField.MultiLanguageField> enableMultiLanguageInfoList =
                Arrays.asList(field1, field2);
        arg.setEnableMultiLanguageInfo(enableMultiLanguageInfoList);

        // Mock dependencies
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(new HashMap<>());

        // Act
        ChangeMultiLanguageField.Result result = objectDataMultiLangService.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建多语言字段时参数为空的场景
     */
    @Test
    @DisplayName("测试create参数为空")
    void testCreateWithEmptyParams() {
        // Arrange
        ChangeMultiLanguageField.Arg arg = new ChangeMultiLanguageField.Arg();
        arg.setEnableMultiLanguageInfo(Arrays.asList());

        // Act & Assert
        assertThrows(Exception.class, () -> {
            objectDataMultiLangService.create(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建多语言字段时参数为null的场景
     */
    @Test
    @DisplayName("测试create参数为null")
    void testCreateWithNullParams() {
        // Arrange
        ChangeMultiLanguageField.Arg arg = new ChangeMultiLanguageField.Arg();
        arg.setEnableMultiLanguageInfo(null);

        // Act & Assert
        assertThrows(Exception.class, () -> {
            objectDataMultiLangService.create(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支持的对象成功的场景
     */
    @Test
    @DisplayName("测试getSupportObject成功")
    void testGetSupportObjectSuccess() {
        // Arrange
        GetSupportObject.Arg arg = new GetSupportObject.Arg();
        arg.setIncludeEnable(true);

        // Act
        GetSupportObject.Result result = objectDataMultiLangService.getSupportObject(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDescribeList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支持的对象时不包含启用的场景
     */
    @Test
    @DisplayName("测试getSupportObject不包含启用")
    void testGetSupportObjectWithoutIncludeEnable() {
        // Arrange
        GetSupportObject.Arg arg = new GetSupportObject.Arg();
        arg.setIncludeEnable(false);

        // Act
        GetSupportObject.Result result = objectDataMultiLangService.getSupportObject(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDescribeList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多语言字段的批量处理
     */
    @Test
    @DisplayName("测试多语言字段批量处理")
    void testMultiLanguageFieldBatchProcessing() {
        // Arrange
        ChangeMultiLanguageField.Arg arg = new ChangeMultiLanguageField.Arg();
        
        ChangeMultiLanguageField.MultiLanguageField[] fields = new ChangeMultiLanguageField.MultiLanguageField[5];
        for (int i = 0; i < 5; i++) {
            fields[i] = new ChangeMultiLanguageField.MultiLanguageField();
            fields[i].setDescribeApiName("BatchObj" + i + "__c");
            fields[i].setEnableField(Arrays.asList("field" + i));
        }

        arg.setEnableMultiLanguageInfo(Arrays.asList(fields));

        // Mock dependencies
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(new HashMap<>());

        // Act
        ChangeMultiLanguageField.Result result = objectDataMultiLangService.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多语言字段的类型验证
     */
    @Test
    @DisplayName("测试多语言字段类型验证")
    void testMultiLanguageFieldTypeValidation() {
        // Arrange
        ChangeMultiLanguageField.Arg arg = new ChangeMultiLanguageField.Arg();
        
        ChangeMultiLanguageField.MultiLanguageField textField = new ChangeMultiLanguageField.MultiLanguageField();
        textField.setDescribeApiName("TestObj__c");
        textField.setEnableField(Arrays.asList("textField"));

        ChangeMultiLanguageField.MultiLanguageField numberField = new ChangeMultiLanguageField.MultiLanguageField();
        numberField.setDescribeApiName("TestObj__c");
        numberField.setEnableField(Arrays.asList("numberField"));
        
        arg.setEnableMultiLanguageInfo(Arrays.asList(textField, numberField));

        // Mock dependencies
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(new HashMap<>());

        // Act
        ChangeMultiLanguageField.Result result = objectDataMultiLangService.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多语言字段的权限验证
     */
    @Test
    @DisplayName("测试多语言字段权限验证")
    void testMultiLanguageFieldPermissionValidation() {
        // Arrange
        ChangeMultiLanguageField.Arg arg = new ChangeMultiLanguageField.Arg();
        
        ChangeMultiLanguageField.MultiLanguageField restrictedField = new ChangeMultiLanguageField.MultiLanguageField();
        restrictedField.setDescribeApiName("RestrictedObj__c");
        restrictedField.setEnableField(Arrays.asList("restrictedField"));
        
        arg.setEnableMultiLanguageInfo(Arrays.asList(restrictedField));

        // Mock dependencies
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(new HashMap<>());

        // Act
        ChangeMultiLanguageField.Result result = objectDataMultiLangService.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多语言字段的国际化支持
     */
    @Test
    @DisplayName("测试多语言字段国际化支持")
    void testMultiLanguageFieldInternationalizationSupport() {
        // Arrange
        ChangeMultiLanguageField.Arg arg = new ChangeMultiLanguageField.Arg();
        
        ChangeMultiLanguageField.MultiLanguageField i18nField = new ChangeMultiLanguageField.MultiLanguageField();
        i18nField.setDescribeApiName("I18nObj__c");
        i18nField.setEnableField(Arrays.asList("i18nField"));
        
        arg.setEnableMultiLanguageInfo(Arrays.asList(i18nField));

        // Mock dependencies
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(new HashMap<>());

        // Act
        ChangeMultiLanguageField.Result result = objectDataMultiLangService.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多语言字段的配置验证
     */
    @Test
    @DisplayName("测试多语言字段配置验证")
    void testMultiLanguageFieldConfigurationValidation() {
        // Arrange
        GetSupportObject.Arg arg = new GetSupportObject.Arg();
        arg.setIncludeEnable(true);

        // Act
        GetSupportObject.Result result = objectDataMultiLangService.getSupportObject(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDescribeList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectDataMultiLangService);
        assertNotNull(describeLogicService);
        assertNotNull(dataLanguageService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("dataMultiLang", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Assert
        assertNotNull(user);
        assertEquals(TENANT_ID, user.getTenantId());
        assertEquals(USER_ID, user.getUserId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSupportFieldByObject方法成功的场景
     */
    @Test
    @DisplayName("测试getSupportFieldByObject成功")
    void testGetSupportFieldByObjectSuccess() {
        // Arrange
        GetSupportFieldByObject.Arg arg = new GetSupportFieldByObject.Arg();
        arg.setDescribeApiName("TestObj__c");

        // Mock ObjectDescribe
        Map<String, Object> describeData = Maps.newHashMap();
        describeData.put("api_name", "TestObj__c");
        describeData.put("display_name", "Test Object");
        ObjectDescribe mockDescribe = new ObjectDescribe(describeData);

        when(describeLogicService.findObjectWithoutCopy(eq(TENANT_ID), eq("TestObj__c")))
                .thenReturn(mockDescribe);

        // Act
        GetSupportFieldByObject.Result result = objectDataMultiLangService.getSupportFieldByObject(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMultiLangInfo());
        assertEquals("TestObj__c", result.getMultiLangInfo().getDescribeApiName());
        assertEquals("Test Object", result.getMultiLangInfo().getDisplayName());
        verify(describeLogicService).findObjectWithoutCopy(eq(TENANT_ID), eq("TestObj__c"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSupportFieldByObject方法参数为空的场景
     */
    @Test
    @DisplayName("测试getSupportFieldByObject参数为空")
    void testGetSupportFieldByObjectWithEmptyParam() {
        // Arrange
        GetSupportFieldByObject.Arg arg = new GetSupportFieldByObject.Arg();
        arg.setDescribeApiName("");

        // Act & Assert
        assertThrows(Exception.class, () -> {
            objectDataMultiLangService.getSupportFieldByObject(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSupportFieldByObject方法对象不存在的场景
     */
    @Test
    @DisplayName("测试getSupportFieldByObject对象不存在")
    void testGetSupportFieldByObjectWithNonExistentObject() {
        // Arrange
        GetSupportFieldByObject.Arg arg = new GetSupportFieldByObject.Arg();
        arg.setDescribeApiName("NonExistentObj__c");

        when(describeLogicService.findObjectWithoutCopy(eq(TENANT_ID), eq("NonExistentObj__c")))
                .thenReturn(null);

        // Act & Assert
        assertThrows(Exception.class, () -> {
            objectDataMultiLangService.getSupportFieldByObject(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getEnableMultiLanguageField方法成功的场景
     */
    @Test
    @DisplayName("测试getEnableMultiLanguageField成功")
    void testGetEnableMultiLanguageFieldSuccess() {
        // Arrange
        GetEnableMultiLanguageField.Arg arg = new GetEnableMultiLanguageField.Arg();
        arg.setDescribeApiName("TestObj__c");

        // Mock ObjectDescribe
        Map<String, Object> describeData = Maps.newHashMap();
        describeData.put("api_name", "TestObj__c");
        describeData.put("display_name", "Test Object");
        ObjectDescribe mockDescribe = new ObjectDescribe(describeData);

        when(describeLogicService.findObject(eq(TENANT_ID), eq("TestObj__c")))
                .thenReturn(mockDescribe);

        // Act
        GetEnableMultiLanguageField.Result result = objectDataMultiLangService.getEnableMultiLanguageField(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMultiLanguageInfo());
        verify(describeLogicService).findObject(eq(TENANT_ID), eq("TestObj__c"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getEnableMultiLanguageField方法无参数的场景
     */
    @Test
    @DisplayName("测试getEnableMultiLanguageField无参数")
    void testGetEnableMultiLanguageFieldWithoutParam() {
        // Arrange
        GetEnableMultiLanguageField.Arg arg = new GetEnableMultiLanguageField.Arg();
        arg.setDescribeApiName(null);

        // Mock DataLanguageService
        when(dataLanguageService.getDataLanguageInfoByTenant(any(ServiceContext.class)))
                .thenReturn(Lists.newArrayList());

        // Act
        GetEnableMultiLanguageField.Result result = objectDataMultiLangService.getEnableMultiLanguageField(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMultiLanguageInfo());
        verify(dataLanguageService).getDataLanguageInfoByTenant(any(ServiceContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试update方法成功的场景
     */
    @Test
    @DisplayName("测试update成功")
    void testUpdateSuccess() {
        // Arrange
        ChangeMultiLanguageField.Arg arg = new ChangeMultiLanguageField.Arg();

        ChangeMultiLanguageField.MultiLanguageField field1 = new ChangeMultiLanguageField.MultiLanguageField();
        field1.setDescribeApiName("TestObj__c");
        field1.setEnableField(Arrays.asList("name"));

        arg.setEnableMultiLanguageInfo(Arrays.asList(field1));

        // Mock dependencies
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any())).thenReturn(new HashMap<>());

        // Act
        ChangeMultiLanguageField.Result result = objectDataMultiLangService.update(arg, serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试update方法参数为空的场景
     */
    @Test
    @DisplayName("测试update参数为空")
    void testUpdateWithEmptyParams() {
        // Arrange
        ChangeMultiLanguageField.Arg arg = new ChangeMultiLanguageField.Arg();
        arg.setEnableMultiLanguageInfo(Arrays.asList());

        // Act & Assert
        assertThrows(Exception.class, () -> {
            objectDataMultiLangService.update(arg, serviceContext);
        });
    }
}
