package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.layoutrule.*;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.LayoutRuleLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectLayoutRuleService单元测试类
 * 测试布局规则服务的所有@ServiceMethod方法
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("ObjectLayoutRuleService单元测试")
public class ObjectLayoutRuleServiceTest extends BaseServiceTest {

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private LayoutRuleLogicService layoutRuleLogicService;

    @Mock
    private LayoutLogicService layoutLogicService;

    @InjectMocks
    private ObjectLayoutRuleService objectLayoutRuleService;

    private LayoutRuleDocument testLayoutRuleDocument;
    private LayoutRuleInfo testLayoutRuleInfo;

    @Override
    protected String getServiceName() {
        return "layout_rule";
    }

    @BeforeEach
    void setUp() {
        // 创建测试用的LayoutRuleDocument
        Map<String, Object> ruleMap = new HashMap<String, Object>();
        ruleMap.put("api_name", "test_layout_rule");
        ruleMap.put("label", "测试布局规则");
        ruleMap.put("describe_api_name", "test_object");
        ruleMap.put("layout_api_name", "test_layout");
        ruleMap.put("status", 1);
        testLayoutRuleDocument = new LayoutRuleDocument(ruleMap);

        // 创建测试用的LayoutRuleInfo
        testLayoutRuleInfo = new LayoutRuleInfo(ruleMap);
    }

    @Test
    @DisplayName("GenerateByAI - 测试创建布局规则成功场景")
    void testCreateLayoutRule_Success() {
        // Arrange
        CreateLayoutRule.Arg arg = new CreateLayoutRule.Arg();
        arg.setLayoutRule(testLayoutRuleDocument);

        // Act
        CreateLayoutRule.Result result = objectLayoutRuleService.createLayoutRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(layoutRuleLogicService, times(1)).createLayoutRule(eq(testUser), any(LayoutRuleInfo.class));
    }

    @Test
    @DisplayName("GenerateByAI - 测试创建布局规则时抛出异常")
    void testCreateLayoutRule_ThrowsException() {
        // Arrange
        CreateLayoutRule.Arg arg = new CreateLayoutRule.Arg();
        arg.setLayoutRule(testLayoutRuleDocument);

        doThrow(new RuntimeException("创建失败")).when(layoutRuleLogicService)
                .createLayoutRule(eq(testUser), any(LayoutRuleInfo.class));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            objectLayoutRuleService.createLayoutRule(arg, serviceContext);
        });
        verify(layoutRuleLogicService, times(1)).createLayoutRule(eq(testUser), any(LayoutRuleInfo.class));
    }

    @Test
    @DisplayName("GenerateByAI - 测试更新布局规则成功场景")
    void testUpdateLayoutRule_Success() {
        // Arrange
        UpdateLayoutRule.Arg arg = new UpdateLayoutRule.Arg();
        arg.setLayoutRule(testLayoutRuleDocument);

        // Act
        UpdateLayoutRule.Result result = objectLayoutRuleService.updateLayoutRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(layoutRuleLogicService, times(1)).updateLayoutRule(eq(testUser), any(LayoutRuleInfo.class));
    }

    @Test
    @DisplayName("GenerateByAI - 测试更新布局规则时抛出异常")
    void testUpdateLayoutRule_ThrowsException() {
        // Arrange
        UpdateLayoutRule.Arg arg = new UpdateLayoutRule.Arg();
        arg.setLayoutRule(testLayoutRuleDocument);

        doThrow(new RuntimeException("更新失败")).when(layoutRuleLogicService)
                .updateLayoutRule(eq(testUser), any(LayoutRuleInfo.class));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            objectLayoutRuleService.updateLayoutRule(arg, serviceContext);
        });
        verify(layoutRuleLogicService, times(1)).updateLayoutRule(eq(testUser), any(LayoutRuleInfo.class));
    }

    @Test
    @DisplayName("GenerateByAI - 测试启用布局规则成功场景")
    void testEnableLayoutRule_Success() {
        // Arrange
        EnableLayoutRule.Arg arg = new EnableLayoutRule.Arg();
        arg.setLayoutRuleApiName("test_layout_rule");
        arg.setDescribeApiName("test_object");

        // Act
        EnableLayoutRule.Result result = objectLayoutRuleService.enableLayoutRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(layoutRuleLogicService, times(1)).enableLayoutRule(testUser, "test_layout_rule", "test_object");
    }

    @Test
    @DisplayName("GenerateByAI - 测试禁用布局规则成功场景")
    void testDisableLayoutRule_Success() {
        // Arrange
        DisableLayoutRule.Arg arg = new DisableLayoutRule.Arg();
        arg.setLayoutRuleApiName("test_layout_rule");

        // Act
        DisableLayoutRule.Result result = objectLayoutRuleService.disableLayoutRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(layoutRuleLogicService, times(1)).disableLayoutRule(testUser, "test_layout_rule");
    }

    @Test
    @DisplayName("GenerateByAI - 测试删除布局规则成功场景")
    void testDeleteLayoutRule_Success() {
        // Arrange
        DeleteLayoutRule.Arg arg = new DeleteLayoutRule.Arg();
        arg.setLayoutRuleApiName("test_layout_rule");

        // Act
        DeleteLayoutRule.Result result = objectLayoutRuleService.deleteLayoutRule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(layoutRuleLogicService, times(1)).deleteLayoutRule(testUser, "test_layout_rule");
    }

    @Test
    @DisplayName("GenerateByAI - 测试删除布局规则时抛出异常")
    void testDeleteLayoutRule_ThrowsException() {
        // Arrange
        DeleteLayoutRule.Arg arg = new DeleteLayoutRule.Arg();
        arg.setLayoutRuleApiName("test_layout_rule");

        doThrow(new RuntimeException("删除失败")).when(layoutRuleLogicService)
                .deleteLayoutRule(testUser, "test_layout_rule");

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            objectLayoutRuleService.deleteLayoutRule(arg, serviceContext);
        });
        verify(layoutRuleLogicService, times(1)).deleteLayoutRule(testUser, "test_layout_rule");
    }

    @Test
    @DisplayName("GenerateByAI - 测试查找布局规则信息成功场景")
    void testFindLayoutRuleInfo_Success() {
        // Arrange
        FindLayoutRuleInfo.Arg arg = new FindLayoutRuleInfo.Arg();
        arg.setLayoutRuleApiName("test_layout_rule");

        when(layoutRuleLogicService.findLayoutRuleByApiName(testUser, "test_layout_rule"))
                .thenReturn(testLayoutRuleInfo);

        // Act
        FindLayoutRuleInfo.Result result = objectLayoutRuleService.findLayoutRuleInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getLayoutRule());
        verify(layoutRuleLogicService, times(1)).findLayoutRuleByApiName(testUser, "test_layout_rule");
    }

    @Test
    @DisplayName("GenerateByAI - 测试查找规则相关信息成功场景（包含对象描述和布局）")
    void testFindRuleRelatedInfo_Success() {
        // Arrange
        FindRuleRelatedInfo.Arg arg = new FindRuleRelatedInfo.Arg();
        arg.setDescribeApiName("test_object");
        arg.setIncludeDescribe(true);
        arg.setIncludeLayout(true);
        arg.setNeedLayoutLang(false);

        IObjectDescribe mockDescribe = TestDataFactory.createObjectDescribe("test_object");
        List<ILayout> mockLayoutList = Arrays.asList();
        List<LayoutRuleInfo> mockLayoutRuleList = Arrays.asList(testLayoutRuleInfo);
        ManageGroup mockManageGroup = new ManageGroup(true, ManageGroupType.LAYOUT, "test_object", Collections.emptySet());

        when(serviceFacade.findObject(TENANT_ID, "test_object")).thenReturn(mockDescribe);
        when(serviceFacade.getLayoutLogicService()).thenReturn(layoutLogicService);
        when(layoutLogicService.findByTypesIncludeFlowLayout(any(), eq("test_object"), any(), eq(false)))
                .thenReturn(mockLayoutList);
        when(layoutRuleLogicService.findLayoutRuleByDescribe(testUser, "test_object"))
                .thenReturn(mockLayoutRuleList);
        when(layoutLogicService.isEditLayoutEnable(TENANT_ID, "test_object", false))
                .thenReturn(false);
        when(layoutLogicService.queryLayoutManageGroup(testUser, "test_object", null))
                .thenReturn(mockManageGroup);

        // Act
        FindRuleRelatedInfo.Result result = objectLayoutRuleService.findRuleRelatedInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDescribe());
        assertNotNull(result.getLayoutList());
        assertNotNull(result.getLayoutRuleList());
        assertNotNull(result.getLayoutManageGroup());
        verify(serviceFacade, times(1)).findObject(TENANT_ID, "test_object");
        verify(layoutRuleLogicService, times(1)).findLayoutRuleByDescribe(testUser, "test_object");
    }

    @Test
    @DisplayName("GenerateByAI - 测试查找规则相关信息成功场景（不包含对象描述和布局）")
    void testFindRuleRelatedInfo_WithoutDescribeAndLayout() {
        // Arrange
        FindRuleRelatedInfo.Arg arg = new FindRuleRelatedInfo.Arg();
        arg.setDescribeApiName("test_object");
        arg.setIncludeDescribe(false);
        arg.setIncludeLayout(false);

        List<LayoutRuleInfo> mockLayoutRuleList = Arrays.asList(testLayoutRuleInfo);
        ManageGroup mockManageGroup = new ManageGroup(true, ManageGroupType.LAYOUT, "test_object", Collections.emptySet());

        when(serviceFacade.getLayoutLogicService()).thenReturn(layoutLogicService);
        when(layoutRuleLogicService.findLayoutRuleByDescribe(testUser, "test_object"))
                .thenReturn(mockLayoutRuleList);
        when(layoutLogicService.isEditLayoutEnable(TENANT_ID, "test_object", false))
                .thenReturn(false);
        when(layoutLogicService.queryLayoutManageGroup(testUser, "test_object", null))
                .thenReturn(mockManageGroup);

        // Act
        FindRuleRelatedInfo.Result result = objectLayoutRuleService.findRuleRelatedInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getDescribe());
        assertNotNull(result.getLayoutList());
        assertTrue(result.getLayoutList().isEmpty());
        assertNotNull(result.getLayoutRuleList());
        assertNotNull(result.getLayoutManageGroup());
        verify(serviceFacade, never()).findObject(anyString(), anyString());
        verify(layoutRuleLogicService, times(1)).findLayoutRuleByDescribe(testUser, "test_object");
    }

    @Test
    @DisplayName("GenerateByAI - 测试查找规则相关信息时布局规则列表为空")
    void testFindRuleRelatedInfo_EmptyLayoutRuleList() {
        // Arrange
        FindRuleRelatedInfo.Arg arg = new FindRuleRelatedInfo.Arg();
        arg.setDescribeApiName("test_object");
        arg.setIncludeDescribe(false);
        arg.setIncludeLayout(false);

        List<LayoutRuleInfo> emptyLayoutRuleList = Arrays.asList();
        ManageGroup mockManageGroup = new ManageGroup(true, ManageGroupType.LAYOUT, "test_object", Collections.emptySet());

        when(serviceFacade.getLayoutLogicService()).thenReturn(layoutLogicService);
        when(layoutRuleLogicService.findLayoutRuleByDescribe(testUser, "test_object"))
                .thenReturn(emptyLayoutRuleList);
        when(layoutLogicService.queryLayoutManageGroup(testUser, "test_object", null))
                .thenReturn(mockManageGroup);

        // Act
        FindRuleRelatedInfo.Result result = objectLayoutRuleService.findRuleRelatedInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getLayoutRuleList());
        assertTrue(result.getLayoutRuleList().isEmpty());
        verify(layoutRuleLogicService, times(1)).findLayoutRuleByDescribe(testUser, "test_object");
    }
}
