package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.layout.UpdateComponentConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectComponentService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectComponentService单元测试")
class ObjectComponentServiceTest {

    @Mock
    private ConfigService configService;
    
    @InjectMocks
    private ObjectComponentService objectComponentService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OBJECT_API_NAME = "test_object__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "component", "test_method");
    }    /**
     * GenerateByAI
     * 测试内容描述：测试更新组件配置成功的正常场景
     */
    @Test
    @DisplayName("测试更新组件配置成功")
    void testUpdateComponentConfigSuccess() {
        // Arrange
        UpdateComponentConfig.Arg arg = new UpdateComponentConfig.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setReset(false);
        
        Map<String, Object> component1 = new HashMap<>();
        component1.put("api_name", "comp1");
        component1.put("config", "value1");
        Map<String, Object> component2 = new HashMap<>();
        component2.put("api_name", "comp2");
        component2.put("config", "value2");
        arg.setComponents(Arrays.asList(component1, component2));

        when(configService.findUserConfig(eq(user), anyString())).thenReturn(null);
        doNothing().when(configService).createUserConfig(eq(user), anyString(), anyString(), eq(ConfigValueType.JSON));

        // Act
        UpdateComponentConfig.Result result = objectComponentService.updateComponentConfig(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(configService).findUserConfig(eq(user), anyString());
        verify(configService).createUserConfig(eq(user), anyString(), anyString(), eq(ConfigValueType.JSON));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重置组件配置成功的正常场景
     */
    @Test
    @DisplayName("测试重置组件配置成功")
    void testUpdateComponentConfigResetSuccess() {
        // Arrange
        UpdateComponentConfig.Arg arg = new UpdateComponentConfig.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setReset(true);

        doNothing().when(configService).deleteUserConfig(eq(user), anyString());

        // Act
        UpdateComponentConfig.Result result = objectComponentService.updateComponentConfig(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(configService).deleteUserConfig(eq(user), anyString());
        verify(configService, never()).findUserConfig(any(), any());
        verify(configService, never()).createUserConfig(any(), any(), any(), any());
        verify(configService, never()).updateUserConfig(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新组件配置空对象API名称抛出异常
     */
    @Test
    @DisplayName("测试更新组件配置空对象API名称抛出异常")
    void testUpdateComponentConfigEmptyObjectApiNameThrowsException() {
        // Arrange
        UpdateComponentConfig.Arg arg = new UpdateComponentConfig.Arg();
        arg.setObjectApiName("");
        arg.setReset(false);

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            objectComponentService.updateComponentConfig(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新组件配置null对象API名称抛出异常
     */
    @Test
    @DisplayName("测试更新组件配置null对象API名称抛出异常")
    void testUpdateComponentConfigNullObjectApiNameThrowsException() {
        // Arrange
        UpdateComponentConfig.Arg arg = new UpdateComponentConfig.Arg();
        arg.setObjectApiName(null);
        arg.setReset(false);

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            objectComponentService.updateComponentConfig(arg, serviceContext);
        });
    }
}