package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.data.*;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.NameCache;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectDataQueryService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectDataQueryService单元测试")
class ObjectDataQueryServiceTest {

    @Mock
    private MetaDataService metaDataService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private ExpressionService expressionService;
    
    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private InfraServiceFacade infraServiceFacade;
    
    @Mock
    private PublicObjectService publicObjectService;

    @InjectMocks
    private ObjectDataQueryService objectDataQueryService;

    private ServiceContext serviceContext;
    private User user;

    private static final String TENANT_ID = "78057";
    private static final String USER_ID = "1000";
    private static final String OBJECT_API_NAME = "TestObj";
    private static final String FIELD_API_NAME = "test_field__c";
    private static final String DATA_ID = "data123";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .user(user)
                .tenantId(TENANT_ID)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "objects", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据条件查询成功的场景
     */
    @Test
    @DisplayName("测试根据条件查询成功")
    void testQueryByCriteriaSuccess() {
        // Arrange
        QueryByCriteria.Arg arg = new QueryByCriteria.Arg();
        arg.setObjectAPIName(OBJECT_API_NAME);
        
        QueryByCriteria.QueryCriteria queryCriteria = new QueryByCriteria.QueryCriteria();
        queryCriteria.setOffset(0);
        queryCriteria.setLimit(10);
        queryCriteria.setCriteriaList(Lists.newArrayList());
        arg.setQueryCriteria(queryCriteria);

        IObjectDescribe mockDescribe = createMockObjectDescribe();
        QueryResult<IObjectData> mockQueryResult = createMockQueryResult();
        
        when(describeLogicService.findObject(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                .thenReturn(mockDescribe);
        when(metaDataService.findBySearchQuery(eq(user), eq(mockDescribe), eq(OBJECT_API_NAME), any()))
                .thenReturn(mockQueryResult);

        // Act
        QueryByCriteria.Result result = objectDataQueryService.queryByCriteria(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDataList());
        assertEquals(1, result.getDataList().size());
        verify(describeLogicService).findObject(eq(TENANT_ID), eq(OBJECT_API_NAME));
        verify(metaDataService).findBySearchQuery(eq(user), eq(mockDescribe), eq(OBJECT_API_NAME), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找记录名称成功的场景
     */
    @Test
    @DisplayName("测试查找记录名称成功")
    void testFindRecordNameSuccess() {
        // Arrange
        FindRecordName.Arg arg = new FindRecordName.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setIdList(Lists.newArrayList(DATA_ID));

        List<INameCache> mockNameList = createMockNameCacheList();
        
        when(metaDataService.findRecordName(any(), eq(OBJECT_API_NAME), eq(arg.getIdList())))
                .thenReturn(mockNameList);

        // Act
        FindRecordName.Result result = objectDataQueryService.findRecordName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getObjectData());
        assertEquals(1, result.getObjectData().size());
        verify(metaDataService).findRecordName(any(), eq(OBJECT_API_NAME), eq(arg.getIdList()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找记录名称时ID列表为空的场景
     */
    @Test
    @DisplayName("测试查找记录名称时ID列表为空")
    void testFindRecordNameEmptyIdList() {
        // Arrange
        FindRecordName.Arg arg = new FindRecordName.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setIdList(Lists.newArrayList());

        // Act
        FindRecordName.Result result = objectDataQueryService.findRecordName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getObjectData());
        verify(metaDataService, never()).findRecordName(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据过滤器查询成功的场景
     */
    @Test
    @DisplayName("测试根据过滤器查询成功")
    void testQueryByFilterSuccess() {
        // Arrange
        QueryByFilter.Arg arg = new QueryByFilter.Arg();
        arg.setObjectAPIName(OBJECT_API_NAME);
        
        Map<String, Object> queryData = Maps.newHashMap();
        queryData.put("limit", 10);
        queryData.put("offset", 0);
        SearchTemplateQueryDocument query = SearchTemplateQueryDocument.of(queryData);
        arg.setQuery(query);

        QueryResult<IObjectData> mockQueryResult = createMockQueryResult();
        
        when(metaDataService.findByQueryWithContext(any(), eq(OBJECT_API_NAME), any()))
                .thenReturn(mockQueryResult);

        // Act
        QueryByFilter.Result result = objectDataQueryService.queryByFilter(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDataList());
        assertEquals(1, result.getDataList().size());
        assertEquals(1, result.getTotalCount());
        verify(metaDataService).findByQueryWithContext(any(), eq(OBJECT_API_NAME), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找数据成功的场景
     */
    @Test
    @DisplayName("测试根据ID查找数据成功")
    void testFindDataByIdSuccess() {
        // Arrange
        FindDataById.Arg arg = new FindDataById.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);

        IObjectDescribe mockDescribe = createMockObjectDescribe();
        IObjectData mockObjectData = createMockObjectData();
        
        when(describeLogicService.findObject(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                .thenReturn(mockDescribe);
        when(metaDataService.findObjectData(eq(user), eq(DATA_ID), eq(mockDescribe)))
                .thenReturn(mockObjectData);

        // Act
        FindDataById.Result result = objectDataQueryService.findDataById(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getObjectData());
        verify(describeLogicService).findObject(eq(TENANT_ID), eq(OBJECT_API_NAME));
        verify(metaDataService).findObjectData(eq(user), eq(DATA_ID), eq(mockDescribe));
        verify(metaDataService).doDataPrivilegeCheck(eq(user), anyList(), eq(mockDescribe), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找数据时无权限的场景
     */
    @Test
    @DisplayName("测试根据ID查找数据时无权限")
    void testFindDataByIdNoPrivilege() {
        // Arrange
        FindDataById.Arg arg = new FindDataById.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);

        IObjectDescribe mockDescribe = createMockObjectDescribe();
        IObjectData mockObjectData = createMockObjectData();
        
        when(describeLogicService.findObject(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                .thenReturn(mockDescribe);
        when(metaDataService.findObjectData(eq(user), eq(DATA_ID), eq(mockDescribe)))
                .thenReturn(mockObjectData);
        doThrow(new ValidateException("无数据权限"))
                .when(metaDataService).doDataPrivilegeCheck(eq(user), anyList(), eq(mockDescribe), anyString());

        // Act
        FindDataById.Result result = objectDataQueryService.findDataById(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getObjectData());
        verify(metaDataService).doDataPrivilegeCheck(eq(user), anyList(), eq(mockDescribe), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称查找ID成功的场景
     */
    @Test
    @DisplayName("测试根据名称查找ID成功")
    void testFindIdByNameSuccess() {
        // Arrange
        FindIdByName.Arg arg = new FindIdByName.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setNameList(Lists.newArrayList("测试对象"));

        Map<String, String> mockMap = Maps.newHashMap();
        mockMap.put("测试对象", DATA_ID);
        
        when(metaDataService.findObjectIdByName(eq(user), eq(OBJECT_API_NAME), eq(arg.getNameList())))
                .thenReturn(mockMap);

        // Act
        FindIdByName.Result result = objectDataQueryService.findIdByName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getNameIdMap());
        assertEquals(DATA_ID, result.getNameIdMap().get("测试对象"));
        verify(metaDataService).findObjectIdByName(eq(user), eq(OBJECT_API_NAME), eq(arg.getNameList()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称查找ID时参数为空的场景
     */
    @Test
    @DisplayName("测试根据名称查找ID时参数为空")
    void testFindIdByNameEmptyParams() {
        // Arrange
        FindIdByName.Arg arg = new FindIdByName.Arg();
        arg.setObjectApiName("");
        arg.setNameList(Lists.newArrayList());

        // Act
        FindIdByName.Result result = objectDataQueryService.findIdByName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getNameIdMap());
        assertTrue(result.getNameIdMap().isEmpty());
        verify(metaDataService, never()).findObjectIdByName(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找名称成功的场景
     */
    @Test
    @DisplayName("测试根据ID查找名称成功")
    void testFindNameByIdSuccess() {
        // Arrange
        FindNameById.Arg arg = new FindNameById.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setIdList(Lists.newArrayList(DATA_ID));

        Map<String, String> mockMap = Maps.newHashMap();
        mockMap.put(DATA_ID, "测试对象");
        
        when(metaDataService.findNameByIds(eq(user), eq(OBJECT_API_NAME), eq(arg.getIdList())))
                .thenReturn(mockMap);

        // Act
        FindNameById.Result result = objectDataQueryService.findNameById(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId2NameMap());
        assertEquals("测试对象", result.getId2NameMap().get(DATA_ID));
        verify(metaDataService).findNameByIds(eq(user), eq(OBJECT_API_NAME), eq(arg.getIdList()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找引用数据成功的场景
     */
    @Test
    @DisplayName("测试查找引用数据成功")
    void testFindReferencedDataSuccess() {
        // Arrange
        FindReferencedData.Arg arg = new FindReferencedData.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setIdList(Lists.newArrayList(DATA_ID));

        List<String> mockList = Lists.newArrayList("RefObj");
        Map<String, IObjectDescribe> mockDescribeMap = Maps.newHashMap();
        IObjectDescribe refDescribe = new ObjectDescribe();
        refDescribe.setApiName("RefObj");
        refDescribe.setDisplayName("引用对象");
        refDescribe.setTenantId(TENANT_ID);
        mockDescribeMap.put("RefObj", refDescribe);
        
        when(metaDataService.findReferencedDataList(eq(user), eq(OBJECT_API_NAME), eq(arg.getIdList())))
                .thenReturn(mockList);
        when(describeLogicService.findObjects(eq(TENANT_ID), eq(mockList)))
                .thenReturn(mockDescribeMap);

        // Act
        FindReferencedData.Result result = objectDataQueryService.findReferencedData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getReferencedDataList());
        assertEquals(1, result.getReferencedDataList().size());
        assertEquals("RefObj", result.getReferencedDataList().get(0).getApiName());
        assertEquals("引用对象", result.getReferencedDataList().get(0).getLabel());
        verify(metaDataService).findReferencedDataList(eq(user), eq(OBJECT_API_NAME), eq(arg.getIdList()));
        verify(describeLogicService).findObjects(eq(TENANT_ID), eq(mockList));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试校验函数成功的场景
     */
    @Test
    @DisplayName("测试校验函数成功")
    void testCheckFunctionCountLimitSuccess() {
        // Arrange
        ValidateFunction.Arg arg = new ValidateFunction.Arg();
        arg.setButtonApiName("test_button__c");
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setObjectDataId(DATA_ID);
        arg.setObjectData(ObjectDataDocument.of(createMockObjectData()));
        arg.setDetails(Maps.newHashMap());
        arg.setArgs(Maps.newHashMap());

        ButtonExecutor.Result mockResult = ButtonExecutor.Result.builder()
                .hasReturnValue(true)
                .returnValue("success")
                .build();
        
        when(infraServiceFacade.triggerValidationFunction(eq(user), any(ButtonExecutor.Arg.class)))
                .thenReturn(mockResult);

        // Act
        ValidateFunction.Result result = objectDataQueryService.checkFunctionCountLimit(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getHasReturnValue());
        assertEquals("success", result.getReturnValue());
        verify(infraServiceFacade).triggerValidationFunction(eq(user), any(ButtonExecutor.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试判断数据是否存在成功的场景
     */
    @Test
    @DisplayName("测试判断数据是否存在成功")
    void testIsExistDataSuccess() {
        // Arrange
        IsExistData.Arg arg = new IsExistData.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        
        when(metaDataService.existData(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                .thenReturn(true);

        // Act
        IsExistData.Result result = objectDataQueryService.isExistData(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertTrue(result.isExistData());
        verify(metaDataService).existData(eq(TENANT_ID), eq(OBJECT_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试选项值被数据使用检查成功的场景
     */
    @Test
    @DisplayName("测试选项值被数据使用检查成功")
    void testOptionUsedByDataSuccess() {
        // Arrange
        OptionUsedByData.Arg arg = new OptionUsedByData.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setFieldApiName(FIELD_API_NAME);
        arg.setValues(Sets.newHashSet("value1", "value2"));

        // Act
        OptionUsedByData.Result result = objectDataQueryService.optionUsedByData(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertFalse(result.isUsed());
        verify(metaDataService).checkOptionUsedByData(eq(user), eq(OBJECT_API_NAME), eq(FIELD_API_NAME), eq(arg.getValues()));
        verify(metaDataService).checkOptionUsedByReference(eq(user), eq(OBJECT_API_NAME), eq(FIELD_API_NAME), eq(arg.getValues()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试支持外部团队查询成功的场景
     */
    @Test
    @DisplayName("测试支持外部团队查询成功")
    void testSupportOutTeamQuerySuccess() {
        // 使用MockedStatic来模拟AppFrameworkConfig
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // Arrange
            SupportOutTeamQuery.Arg arg = new SupportOutTeamQuery.Arg();
            
            Map<String, Boolean> moduleMap = Maps.newHashMap();
            moduleMap.put(LicenseConstants.ModuleCode.INTERCONNECT_APP_BASIC_APP, true);
            
            mockedConfig.when(() -> AppFrameworkConfig.isShowingOutTeamMemberGray(eq(TENANT_ID)))
                    .thenReturn(true);
            when(serviceFacade.existModule(eq(TENANT_ID), any(Set.class)))
                    .thenReturn(moduleMap);

            // Act
            SupportOutTeamQuery.Result result = objectDataQueryService.supportOutTeamQuery(serviceContext, arg);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSupport());
            mockedConfig.verify(() -> AppFrameworkConfig.isShowingOutTeamMemberGray(eq(TENANT_ID)));
            verify(serviceFacade).existModule(eq(TENANT_ID), any(Set.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试外部用户不支持外部团队查询的场景
     */
    @Test
    @DisplayName("测试外部用户不支持外部团队查询")
    void testSupportOutTeamQueryOutUser() {
        // Arrange
        User outUser = mock(User.class);
        when(outUser.isOutUser()).thenReturn(true);
        
        RequestContext outUserContext = RequestContext.builder()
                .user(outUser)
                .tenantId(TENANT_ID)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        ServiceContext outUserServiceContext = new ServiceContext(outUserContext, "objects", "test");
        
        SupportOutTeamQuery.Arg arg = new SupportOutTeamQuery.Arg();

        // Act
        SupportOutTeamQuery.Result result = objectDataQueryService.supportOutTeamQuery(outUserServiceContext, arg);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSupport());
        verify(outUser).isOutUser();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试转换公共数据成功的场景
     */
    @Test
    @DisplayName("测试转换公共数据成功")
    void testConvertPublicDataSuccess() {
        // Arrange
        PublicObject.DataArg arg = new PublicObject.DataArg();
        Map<String, List<String>> dataMap = Maps.newHashMap();
        dataMap.put(OBJECT_API_NAME, Lists.newArrayList(DATA_ID));
        arg.setDataMap(dataMap);

        // Act
        PublicObject.DataConvertResult result = objectDataQueryService.convertPublicData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(publicObjectService).convertPublicData(eq(user), eq(dataMap));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试转换公共数据时参数为空的场景
     */
    @Test
    @DisplayName("测试转换公共数据时参数为空")
    void testConvertPublicDataEmptyArg() {
        // Arrange
        PublicObject.DataArg arg = null;

        // Act
        PublicObject.DataConvertResult result = objectDataQueryService.convertPublicData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(publicObjectService, never()).convertPublicData(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建搜索查询成功的场景
     */
    @Test
    @DisplayName("测试构建搜索查询成功")
    void testBuildSearchQuerySuccess() {
        // Arrange
        BuildSearchQuery.Arg arg = new BuildSearchQuery.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setTemplateId("template123");
        arg.setSearchTemplateType("CUSTOM");
        arg.setSearchQueryInfo("{}");

        IObjectDescribe mockDescribe = createMockObjectDescribe();
        Query mockQuery = mock(Query.class);
        ISearchTemplateQuery mockSearchTemplateQuery = mock(ISearchTemplateQuery.class);
        
        when(describeLogicService.findObjectWithoutCopy(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                .thenReturn(mockDescribe);
        when(serviceFacade.findSearchQuery(eq(user), eq(mockDescribe), eq("{}"), any(SearchQueryContext.class)))
                .thenReturn(mockQuery);
        when(mockQuery.toSearchTemplateQuery()).thenReturn(mockSearchTemplateQuery);
        when(mockSearchTemplateQuery.toJsonString()).thenReturn("{\"query\":\"test\"}");

        // Act
        BuildSearchQuery.Result result = objectDataQueryService.buildSearchQuery(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals("{\"query\":\"test\"}", result.getSearchTemplateQuery());
        verify(describeLogicService).findObjectWithoutCopy(eq(TENANT_ID), eq(OBJECT_API_NAME));
        verify(serviceFacade).findSearchQuery(eq(user), eq(mockDescribe), eq("{}"), any(SearchQueryContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试转换私有数据成功的场景
     */
    @Test
    @DisplayName("测试转换私有数据成功")
    void testConvertPrivateDataSuccess() {
        // Arrange
        PublicObject.DataArg arg = new PublicObject.DataArg();
        Map<String, List<String>> dataMap = Maps.newHashMap();
        dataMap.put(OBJECT_API_NAME, Lists.newArrayList(DATA_ID));
        arg.setDataMap(dataMap);

        // Act
        PublicObject.DataConvertResult result = objectDataQueryService.convertPrivateData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证Mock交互
        verify(publicObjectService).convertPrivateData(user, dataMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试转换公共对象描述成功的场景
     */
    @Test
    @DisplayName("测试转换公共对象描述成功")
    void testConvertPublicDescribeSuccess() {
        // Arrange
        PublicObject.DescribeArg arg = new PublicObject.DescribeArg();
        arg.setObjectApiNames(Lists.newArrayList(OBJECT_API_NAME));

        // Act
        PublicObject.DataConvertResult result = objectDataQueryService.convertPublicDescribe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证Mock交互
        verify(publicObjectService).convertPublicDescribe(user, Lists.newArrayList(OBJECT_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用公共对象描述成功的场景
     */
    @Test
    @DisplayName("测试启用公共对象描述成功")
    void testEnablePublicDescribeSuccess() {
        // Arrange
        PublicObject.DescribeArg arg = new PublicObject.DescribeArg();
        arg.setObjectApiNames(Lists.newArrayList(OBJECT_API_NAME));
        arg.setUpstreamTenantId("upstream123");

        // Act
        PublicObject.DataConvertResult result = objectDataQueryService.enablePublicDescribe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证Mock交互
        verify(publicObjectService).enablePublicDescribe(user, "upstream123", Lists.newArrayList(OBJECT_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用公共对象描述成功的场景
     */
    @Test
    @DisplayName("测试禁用公共对象描述成功")
    void testDisablePublicDescribeSuccess() {
        // Arrange
        PublicObject.DescribeArg arg = new PublicObject.DescribeArg();
        arg.setObjectApiNames(Lists.newArrayList(OBJECT_API_NAME));
        arg.setUpstreamTenantId("upstream123");

        // Act
        PublicObject.DataConvertResult result = objectDataQueryService.disablePublicDescribe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证Mock交互
        verify(publicObjectService).disablePublicDescribe(user, Lists.newArrayList(OBJECT_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找公共对象描述API名称成功的场景
     */
    @Test
    @DisplayName("测试查找公共对象描述API名称成功")
    void testFindPublicDescribeApiNamesSuccess() {
        // Arrange
        PublicObject.DescribeArg arg = new PublicObject.DescribeArg();
        arg.setObjectApiNames(Lists.newArrayList(OBJECT_API_NAME));

        List<String> mockResult = Lists.newArrayList(OBJECT_API_NAME);
        when(publicObjectService.findPublicDescribeApiNames(user, Lists.newArrayList(OBJECT_API_NAME)))
                .thenReturn(mockResult);

        // Act
        PublicObject.DescribeResult result = objectDataQueryService.findPublicDescribeApiNames(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockResult, result.getObjectApiNames());

        // 验证Mock交互
        verify(publicObjectService).findPublicDescribeApiNames(user, Lists.newArrayList(OBJECT_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找公共对象数据成功的场景
     */
    @Test
    @DisplayName("测试根据ID查找公共对象数据成功")
    void testFindPublicObjectDataByIdsSuccess() {
        // Arrange
        PublicObject.DataArg arg = new PublicObject.DataArg();
        Map<String, List<String>> dataMap = Maps.newHashMap();
        dataMap.put(OBJECT_API_NAME, Lists.newArrayList(DATA_ID));
        arg.setDataMap(dataMap);

        Map<String, List<String>> mockResult = Maps.newHashMap();
        mockResult.put(OBJECT_API_NAME, Lists.newArrayList(DATA_ID));
        when(publicObjectService.findPublicObjectDataByIds(user, dataMap))
                .thenReturn(mockResult);

        // Act
        PublicObject.DataResult result = objectDataQueryService.findPublicObjectDataByIds(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockResult, result.getDataMap());

        // 验证Mock交互
        verify(publicObjectService).findPublicObjectDataByIds(user, dataMap);
    }

    // Helper methods for creating test data
    private IObjectDescribe createMockObjectDescribe() {
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName(OBJECT_API_NAME);
        describe.setDisplayName("测试对象");
        describe.setTenantId(TENANT_ID);
        
        Map<String, Object> fieldConfig = Maps.newHashMap();
        fieldConfig.put("api_name", FIELD_API_NAME);
        fieldConfig.put("type", IFieldType.TEXT);
        IFieldDescribe field = FieldDescribeFactory.newInstance(fieldConfig);
        describe.setFieldDescribes(Lists.newArrayList(field));
        
        return describe;
    }

    private IObjectData createMockObjectData() {
        IObjectData objectData = new ObjectData();
        objectData.setId(DATA_ID);
        objectData.setName("测试数据");
        ((ObjectData) objectData).set("object_describe_api_name", OBJECT_API_NAME);
        return objectData;
    }

    private QueryResult<IObjectData> createMockQueryResult() {
        QueryResult<IObjectData> queryResult = mock(QueryResult.class);
        List<IObjectData> dataList = Lists.newArrayList(createMockObjectData());
        lenient().when(queryResult.getData()).thenReturn(dataList);
        lenient().when(queryResult.getTotalNumber()).thenReturn(1);
        return queryResult;
    }

    private List<INameCache> createMockNameCacheList() {
        NameCache nameCache = mock(NameCache.class);
        Map<String, Object> containerDocument = Maps.newHashMap();
        containerDocument.put("_id", DATA_ID);
        containerDocument.put("name", "测试数据");
        containerDocument.put("object_describe_api_name", OBJECT_API_NAME);
        
        when(nameCache.getContainerDocument()).thenReturn(containerDocument);
        return Lists.newArrayList(nameCache);
    }
}