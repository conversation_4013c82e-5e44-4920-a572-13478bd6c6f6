package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.control.level.FindControlLevel;
import com.facishare.paas.appframework.metadata.config.ControlLevelResourceType;
import com.facishare.paas.appframework.metadata.config.ObjectControlLevelLogicService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectControlLevelService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectControlLevelService单元测试")
class ObjectControlLevelServiceTest {

    @Mock
    private ObjectControlLevelLogicService objectControlLevelLogicService;
    
    @InjectMocks
    private ObjectControlLevelService objectControlLevelService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "control_level", "test_method");
    }    /**
     * GenerateByAI
     * 测试内容描述：测试查找控制级别成功的正常场景
     */
    @Test
    @DisplayName("测试查找控制级别成功")
    void testFindControlLevelSuccess() {
        // Arrange
        FindControlLevel.Arg arg = new FindControlLevel.Arg();
        arg.setResourceType("DESCRIBE");
        arg.setParentFieldValue("parent1");
        arg.setPrimaryKey("key1");

        ObjectControlLevelLogicService.ObjectControlLevelInfo info1 =
                ObjectControlLevelLogicService.ObjectControlLevelInfo.of("sourceType1", "key1", "parent1", "level1");

        ObjectControlLevelLogicService.ObjectControlLevelInfo info2 =
                ObjectControlLevelLogicService.ObjectControlLevelInfo.of("sourceType2", "key2", "parent1", "level2");
        
        List<ObjectControlLevelLogicService.ObjectControlLevelInfo> mockInfos = new ArrayList<>(Arrays.asList(info1, info2));

        try (MockedStatic<ControlLevelResourceType> mockedStatic = mockStatic(ControlLevelResourceType.class)) {
            mockedStatic.when(() -> ControlLevelResourceType.getByResourceType("DESCRIBE"))
                    .thenReturn(Optional.of(ControlLevelResourceType.DESCRIBE));

            when(objectControlLevelLogicService.queryControlLevel(eq(user), eq("parent1"), eq(ControlLevelResourceType.DESCRIBE)))
                    .thenReturn(mockInfos);

            // Act
            FindControlLevel.Result result = objectControlLevelService.findControlLevel(serviceContext, arg);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getControlLevelInfos());
            assertEquals(1, result.getControlLevelInfos().size());
            assertEquals("key1", result.getControlLevelInfos().get(0).getPrimaryKey());
            verify(objectControlLevelLogicService).queryControlLevel(eq(user), eq("parent1"), eq(ControlLevelResourceType.DESCRIBE));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找控制级别无效资源类型场景
     */
    @Test
    @DisplayName("测试查找控制级别无效资源类型")
    void testFindControlLevelInvalidResourceType() {
        // Arrange
        FindControlLevel.Arg arg = new FindControlLevel.Arg();
        arg.setResourceType("INVALID");

        try (MockedStatic<ControlLevelResourceType> mockedStatic = mockStatic(ControlLevelResourceType.class)) {
            mockedStatic.when(() -> ControlLevelResourceType.getByResourceType("INVALID"))
                    .thenReturn(Optional.empty());

            // Act
            FindControlLevel.Result result = objectControlLevelService.findControlLevel(serviceContext, arg);

            // Assert
            assertNotNull(result);
            assertNull(result.getControlLevelInfos());
            verify(objectControlLevelLogicService, never()).queryControlLevel(any(), any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找控制级别无主键过滤场景
     */
    @Test
    @DisplayName("测试查找控制级别无主键过滤")
    void testFindControlLevelNoPrimaryKeyFilter() {
        // Arrange
        FindControlLevel.Arg arg = new FindControlLevel.Arg();
        arg.setResourceType("DESCRIBE");
        arg.setParentFieldValue("parent1");
        // 不设置primaryKey

        ObjectControlLevelLogicService.ObjectControlLevelInfo info1 =
                ObjectControlLevelLogicService.ObjectControlLevelInfo.of("sourceType1", "key1", "parent1", "level1");

        ObjectControlLevelLogicService.ObjectControlLevelInfo info2 =
                ObjectControlLevelLogicService.ObjectControlLevelInfo.of("sourceType2", "key2", "parent1", "level2");

        List<ObjectControlLevelLogicService.ObjectControlLevelInfo> mockInfos = new ArrayList<>(Arrays.asList(info1, info2));

        try (MockedStatic<ControlLevelResourceType> mockedStatic = mockStatic(ControlLevelResourceType.class)) {
            mockedStatic.when(() -> ControlLevelResourceType.getByResourceType("DESCRIBE"))
                    .thenReturn(Optional.of(ControlLevelResourceType.DESCRIBE));

            when(objectControlLevelLogicService.queryControlLevel(eq(user), eq("parent1"), eq(ControlLevelResourceType.DESCRIBE)))
                    .thenReturn(mockInfos);

            // Act
            FindControlLevel.Result result = objectControlLevelService.findControlLevel(serviceContext, arg);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getControlLevelInfos());
            assertEquals(2, result.getControlLevelInfos().size());
        }
    }
}