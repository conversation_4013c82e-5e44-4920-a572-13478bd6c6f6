package com.facishare.paas.appframework.core.predef.service;


import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISearchQuery;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ObjectRESTDriverTestServiceTest {

  private static final String TENANT_ID = "74255";
  private static final String USER_ID = "1000";

  @Mock
  private IObjectDataService dataService;

  @InjectMocks
  private ObjectRESTDriverTestService objectRESTDriverTestService;

  private ServiceContext serviceContext;
  private User user;

  @BeforeEach
  void setUp() {
    user = new User(TENANT_ID, USER_ID);
    RequestContext requestContext = RequestContext.builder()
        .tenantId(TENANT_ID)
        .user(user)
        .requestSource(RequestContext.RequestSource.CEP)
        .build();
    RequestContextManager.setContext(requestContext);
    serviceContext = new ServiceContext(requestContext, "object_data_rest", "findbysearchquery");
  }

  @AfterEach
  void tearDown() {
    RequestContextManager.removeContext();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试通过搜索查询查找数据的基本功能
   */
  @Test
  @DisplayName("测试通过搜索查询查找数据的基本功能")
  void testFindbysearchqueryBasic() throws MetadataServiceException {
    // Arrange
    Map<String, Object> searchQueryMap = Maps.newHashMap();
    searchQueryMap.put("objectApiName", "AccountObj");
    searchQueryMap.put("pageSize", 10);
    searchQueryMap.put("pageIndex", 1);
    searchQueryMap.put("limit", 10);
    searchQueryMap.put("offset", 0);

    Map<String, Object> objectDataMap = Maps.newHashMap();
    objectDataMap.put("id", "1");
    objectDataMap.put("name", "Test Account");
    ObjectData objectData = new ObjectData(objectDataMap);

    List<IObjectData> dataList = Lists.newArrayList(objectData);
    QueryResult<IObjectData> queryResult = new QueryResult<IObjectData>();
    queryResult.setData(dataList);
    queryResult.setTotalNumber(1);

    when(dataService.findBySearchQuery(any(ISearchQuery.class), anyString(), anyString(), any(ActionContext.class)))
        .thenReturn(queryResult);

    // Act
    Object result = objectRESTDriverTestService.findbysearchquery(searchQueryMap, serviceContext);

    // Then
    assertNotNull(result);
    assertTrue(result instanceof String);
    String jsonResult = (String) result;
    assertTrue(jsonResult.contains("Test Account"));
    verify(dataService).findBySearchQuery(any(ISearchQuery.class), anyString(), anyString(), any(ActionContext.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试异常处理
   */
  @Test
  @DisplayName("测试异常处理")
  void testFindbysearchqueryException() throws MetadataServiceException {
    // Arrange
    Map<String, Object> searchQueryMap = Maps.newHashMap();
    searchQueryMap.put("objectApiName", "AccountObj");
    searchQueryMap.put("pageSize", 10);
    searchQueryMap.put("pageIndex", 1);
    searchQueryMap.put("limit", 10);
    searchQueryMap.put("offset", 0);

    when(dataService.findBySearchQuery(any(ISearchQuery.class), anyString(), anyString(), any(ActionContext.class)))
        .thenThrow(new RuntimeException("Test exception"));

    // Act & Then
    assertThrows(RuntimeException.class, () -> {
      objectRESTDriverTestService.findbysearchquery(searchQueryMap, serviceContext);
    });
  }
}
