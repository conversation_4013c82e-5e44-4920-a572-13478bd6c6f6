package com.facishare.paas.appframework.core.predef.service;


import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.service.dto.datasync.SyncField;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.mtresource.IMtResourceService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectDataSyncService单元测试类
 * 测试数据同步服务的所有@ServiceMethod方法
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("ObjectDataSyncService单元测试")
class ObjectDataSyncServiceJunitTest extends BaseServiceTest {

    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private LicenseService licenseService;
    
    @Mock
    private IMtResourceService mtResourceService;
    
    @Mock
    private ManageGroupService manageGroupService;
    
    @InjectMocks
    private ObjectDataSyncService objectDataSyncService;

    private static final String DESCRIBE_API_NAME = "TestObj__c";

    @Override
    protected String getServiceName() {
        return "data_sync";
    }

    @BeforeEach
    void setUp() {
        // 基础设置已在BaseServiceTest中完成
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本的Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectDataSyncService);
        assertNotNull(describeLogicService);
        assertNotNull(licenseService);
        assertNotNull(mtResourceService);
        assertNotNull(manageGroupService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试SyncField.Arg的基本构造
     */
    @Test
    @DisplayName("测试SyncField.Arg构造")
    void testSyncFieldArgConstruction() {
        // Arrange & Act
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet("target1", "target2"));
        arg.setFieldApiNames(Sets.newHashSet("field1", "field2"));
        arg.setLockDownstram(false);

        // Assert
        assertNotNull(arg);
        assertEquals(DESCRIBE_API_NAME, arg.getDescribeApiName());
        assertEquals(2, arg.getTenantIds().size());
        assertEquals(2, arg.getFieldApiNames().size());
        assertFalse(arg.isLockDownstram());
        assertTrue(arg.getTenantIds().contains("target1"));
        assertTrue(arg.getTenantIds().contains("target2"));
        assertTrue(arg.getFieldApiNames().contains("field1"));
        assertTrue(arg.getFieldApiNames().contains("field2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试SyncField.Result的基本构造
     */
    @Test
    @DisplayName("测试SyncField.Result构造")
    void testSyncFieldResultConstruction() {
        // Arrange & Act
        SyncField.Result result = SyncField.Result.builder()
                .errorInfo(null)
                .build();

        // Assert
        assertNotNull(result);
        assertNull(result.getErrorInfo());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext的正确构造
     */
    @Test
    @DisplayName("测试ServiceContext构造")
    void testServiceContextConstruction() {
        // Assert
        assertNotNull(serviceContext);
        assertNotNull(serviceContext.getRequestContext());
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(testUser, serviceContext.getRequestContext().getUser());
        assertEquals("data_sync", serviceContext.getServiceName());
    }

    @Test
    @DisplayName("GenerateByAI - 测试syncField空租户列表场景")
    void testSyncField_EmptyTenantIds() {
        // Arrange
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet()); // 空租户列表
        arg.setFieldApiNames(Sets.newHashSet("field1", "field2"));
        arg.setLockDownstram(false);

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(() -> AppFrameworkConfig.isSyncFieldGrayEi(TENANT_ID)).thenReturn(true);

            // Act
            SyncField.Result result = objectDataSyncService.syncField(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNull(result.getErrorInfo());
        }
    }

    @Test
    @DisplayName("GenerateByAI - 测试syncField空字段列表场景")
    void testSyncField_EmptyFieldApiNames() {
        // Arrange
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet("target1", "target2"));
        arg.setFieldApiNames(Sets.newHashSet()); // 空字段列表
        arg.setLockDownstram(false);

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(() -> AppFrameworkConfig.isSyncFieldGrayEi(TENANT_ID)).thenReturn(true);

            // Act
            SyncField.Result result = objectDataSyncService.syncField(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNull(result.getErrorInfo());
        }
    }

    @Test
    @DisplayName("GenerateByAI - 测试syncField灰度功能未开启异常")
    void testSyncField_GrayNotEnabled_ThrowsException() {
        // Arrange
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet("target1"));
        arg.setFieldApiNames(Sets.newHashSet("field1"));
        arg.setLockDownstram(false);

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(() -> AppFrameworkConfig.isSyncFieldGrayEi(TENANT_ID)).thenReturn(false);

            // Act & Assert
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                objectDataSyncService.syncField(arg, serviceContext);
            });
            assertEquals("unSupport tenant!", exception.getMessage());
        }
    }

    @Test
    @DisplayName("GenerateByAI - 测试syncField成功场景")
    void testSyncField_Success() {
        // Arrange
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet("target1"));
        arg.setFieldApiNames(Sets.newHashSet("field1"));
        arg.setLockDownstram(false);

        // Mock对象描述和字段描述
        IObjectDescribe mockSourceDescribe = TestDataFactory.createObjectDescribe(DESCRIBE_API_NAME);
        IFieldDescribe mockField = TestDataFactory.createTextFieldDescribe("field1");
        IObjectDescribe mockTargetDescribe = TestDataFactory.createObjectDescribe(DESCRIBE_API_NAME);

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(() -> AppFrameworkConfig.isSyncFieldGrayEi(TENANT_ID)).thenReturn(true);

            when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockSourceDescribe);
            when(describeLogicService.findObject("target1", DESCRIBE_API_NAME)).thenReturn(mockTargetDescribe);
            when(licenseService.isOpenCRM("target1")).thenReturn(true);

            // Act
            SyncField.Result result = objectDataSyncService.syncField(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getErrorInfo());
            assertTrue(result.getErrorInfo().isEmpty());
            verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
            verify(describeLogicService, times(1)).findObject("target1", DESCRIBE_API_NAME);
            verify(licenseService, times(1)).isOpenCRM("target1");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试syncField多个租户场景
     */
    @Test
    @DisplayName("GenerateByAI - 测试syncField多个租户场景")
    void testSyncField_MultipleTenants() {
        // Arrange
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet("target1", "target2"));
        arg.setFieldApiNames(Sets.newHashSet("field1"));
        arg.setLockDownstram(true);

        // Mock对象描述和字段描述
        IObjectDescribe mockSourceDescribe = TestDataFactory.createObjectDescribe(DESCRIBE_API_NAME);
        IObjectDescribe mockTargetDescribe1 = TestDataFactory.createObjectDescribe(DESCRIBE_API_NAME);
        IObjectDescribe mockTargetDescribe2 = TestDataFactory.createObjectDescribe(DESCRIBE_API_NAME);

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(() -> AppFrameworkConfig.isSyncFieldGrayEi(TENANT_ID)).thenReturn(true);

            when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockSourceDescribe);
            when(describeLogicService.findObject("target1", DESCRIBE_API_NAME)).thenReturn(mockTargetDescribe1);
            when(describeLogicService.findObject("target2", DESCRIBE_API_NAME)).thenReturn(mockTargetDescribe2);
            when(licenseService.isOpenCRM("target1")).thenReturn(true);
            when(licenseService.isOpenCRM("target2")).thenReturn(true);

            // Act
            SyncField.Result result = objectDataSyncService.syncField(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getErrorInfo());
            verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
            verify(describeLogicService, times(1)).findObject("target1", DESCRIBE_API_NAME);
            verify(describeLogicService, times(1)).findObject("target2", DESCRIBE_API_NAME);
            verify(licenseService, times(1)).isOpenCRM("target1");
            verify(licenseService, times(1)).isOpenCRM("target2");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试syncField租户未开启CRM异常场景
     */
    @Test
    @DisplayName("GenerateByAI - 测试syncField租户未开启CRM异常场景")
    void testSyncField_TenantNotOpenCRM() {
        // Arrange
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet("target1"));
        arg.setFieldApiNames(Sets.newHashSet("field1"));
        arg.setLockDownstram(false);

        // Mock对象描述
        IObjectDescribe mockSourceDescribe = TestDataFactory.createObjectDescribe(DESCRIBE_API_NAME);

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(() -> AppFrameworkConfig.isSyncFieldGrayEi(TENANT_ID)).thenReturn(true);

            when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockSourceDescribe);
            when(licenseService.isOpenCRM("target1")).thenReturn(false); // 未开启CRM

            // Act
            SyncField.Result result = objectDataSyncService.syncField(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getErrorInfo());
            assertEquals(1, result.getErrorInfo().size());
            assertEquals("target1", result.getErrorInfo().get(0).getTenantId());
            verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
            verify(licenseService, times(1)).isOpenCRM("target1");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试syncField目标对象不存在异常场景
     */
    @Test
    @DisplayName("GenerateByAI - 测试syncField目标对象不存在异常场景")
    void testSyncField_TargetObjectNotExist() {
        // Arrange
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet("target1"));
        arg.setFieldApiNames(Sets.newHashSet("field1"));
        arg.setLockDownstram(false);

        // Mock对象描述
        IObjectDescribe mockSourceDescribe = TestDataFactory.createObjectDescribe(DESCRIBE_API_NAME);

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(() -> AppFrameworkConfig.isSyncFieldGrayEi(TENANT_ID)).thenReturn(true);

            when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockSourceDescribe);
            when(describeLogicService.findObject("target1", DESCRIBE_API_NAME)).thenReturn(null); // 目标对象不存在
            when(licenseService.isOpenCRM("target1")).thenReturn(true);

            // Act
            SyncField.Result result = objectDataSyncService.syncField(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getErrorInfo());
            assertEquals(1, result.getErrorInfo().size());
            assertEquals("target1", result.getErrorInfo().get(0).getTenantId());
            verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
            verify(describeLogicService, times(1)).findObject("target1", DESCRIBE_API_NAME);
            verify(licenseService, times(1)).isOpenCRM("target1");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试syncField系统异常场景
     */
    @Test
    @DisplayName("GenerateByAI - 测试syncField系统异常场景")
    void testSyncField_SystemError() {
        // Arrange
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet("target1"));
        arg.setFieldApiNames(Sets.newHashSet("field1"));
        arg.setLockDownstram(false);

        // Mock对象描述
        IObjectDescribe mockSourceDescribe = TestDataFactory.createObjectDescribe(DESCRIBE_API_NAME);

        try (MockedStatic<AppFrameworkConfig> configMock = Mockito.mockStatic(AppFrameworkConfig.class)) {
            configMock.when(() -> AppFrameworkConfig.isSyncFieldGrayEi(TENANT_ID)).thenReturn(true);

            when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockSourceDescribe);
            when(licenseService.isOpenCRM("target1")).thenThrow(new RuntimeException("System error")); // 系统异常

            // Act
            SyncField.Result result = objectDataSyncService.syncField(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getErrorInfo());
            assertEquals(1, result.getErrorInfo().size());
            assertEquals("target1", result.getErrorInfo().get(0).getTenantId());
            verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
            verify(licenseService, times(1)).isOpenCRM("target1");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试SyncErrorInfo的构造和方法
     */
    @Test
    @DisplayName("GenerateByAI - 测试SyncErrorInfo构造和方法")
    void testSyncErrorInfo_Construction() {
        // Arrange & Act
        SyncField.SyncErrorInfo errorInfo = SyncField.SyncErrorInfo.of("tenant123", SyncField.SyncError.TENANT_NOT_EXIT);

        // Assert
        assertNotNull(errorInfo);
        assertEquals("tenant123", errorInfo.getTenantId());
        assertEquals("4001", errorInfo.getCode());
        assertEquals("tenant_not_exit", errorInfo.getMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试SyncError枚举的所有值
     */
    @Test
    @DisplayName("GenerateByAI - 测试SyncError枚举值")
    void testSyncError_EnumValues() {
        // Assert
        assertEquals("4001", SyncField.SyncError.TENANT_NOT_EXIT.getCode());
        assertEquals("tenant_not_exit", SyncField.SyncError.TENANT_NOT_EXIT.getMessage());

        assertEquals("4002", SyncField.SyncError.OBJECT_NOT_EXIT.getCode());
        assertEquals("object_not_exit", SyncField.SyncError.OBJECT_NOT_EXIT.getMessage());

        assertEquals("5003", SyncField.SyncError.UPDATE_OBJECT_FIL.getCode());
        assertEquals("update_object_fil", SyncField.SyncError.UPDATE_OBJECT_FIL.getMessage());

        assertEquals("403", SyncField.SyncError.APP_BUSINESS_ERROR.getCode());
        assertEquals("app_business_error", SyncField.SyncError.APP_BUSINESS_ERROR.getMessage());

        assertEquals("500", SyncField.SyncError.SYSTEM_ERROR.getCode());
        assertEquals("system_error", SyncField.SyncError.SYSTEM_ERROR.getMessage());
    }
}
