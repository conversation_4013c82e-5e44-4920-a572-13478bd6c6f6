package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.resource.*;
import com.facishare.paas.appframework.metadata.mtresource.ConfigurationPackageResourceLogicService;
import com.facishare.paas.appframework.metadata.mtresource.IMtResourceService;
import com.facishare.paas.appframework.metadata.mtresource.model.ConfigurationPackageResource;
import com.facishare.paas.appframework.metadata.mtresource.model.FieldControlInfoHelper;
import com.facishare.paas.appframework.metadata.mtresource.model.ResourceQuery;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectResourceService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectResourceService单元测试")
class ObjectResourceServiceTest {

    @Mock
    private ConfigurationPackageResourceLogicService configurationPackageResourceLogicService;
    
    @Mock
    private IMtResourceService mtResourceService;
    
    @Mock
    private ServiceContext serviceContext;
    
    @InjectMocks
    private ObjectResourceService service;
    
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        when(serviceContext.getUser()).thenReturn(user);
        when(serviceContext.getTenantId()).thenReturn(TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试find方法查找资源
     */
    @Test
    @DisplayName("测试find方法查找资源")
    void testFind() {
        // Arrange
        FindObjectResource.Arg arg = new FindObjectResource.Arg();
        arg.setResourceParentValue("parent_value");
        arg.setResourceType("mt_field");
        arg.setResourceQuery(mock(ResourceQuery.class));
        arg.setSourceType("brush_data");
        arg.setSourceValue("source_value");

        ConfigurationPackageResource mockResource = new ConfigurationPackageResource();
        mockResource.setResourceType("mt_field");
        mockResource.setControlLevel("0");
        mockResource.setSourceType("brush_data");
        
        List<ConfigurationPackageResource> mockResources = Arrays.asList(mockResource);
        when(configurationPackageResourceLogicService.find(
                any(User.class), eq("parent_value"), eq("mt_field"), 
                any(ResourceQuery.class), eq("brush_data"), eq("source_value")))
                .thenReturn(mockResources);

        // Act
        FindObjectResource.Result result = service.find(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(1, result.getResult().size());
        assertEquals("mt_field", result.getResult().get(0).getResourceType());
        verify(configurationPackageResourceLogicService).find(
                any(User.class), eq("parent_value"), eq("mt_field"), 
                any(ResourceQuery.class), eq("brush_data"), eq("source_value"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试modifyResource方法修改资源
     */
    @Test
    @DisplayName("测试modifyResource方法修改资源")
    void testModifyResource() {
        // Arrange
        ModifyResource.Arg arg = new ModifyResource.Arg();
        
        ConfigurationPackageResource resource1 = new ConfigurationPackageResource();
        resource1.setResourceType("mt_field");
        resource1.setControlLevel("0");
        resource1.setSourceType("brush_data");
        
        ConfigurationPackageResource resource2 = new ConfigurationPackageResource();
        resource2.setResourceType("mt_describe");
        resource2.setControlLevel("0");
        resource2.setSourceType("brush_data");
        
        List<ConfigurationPackageResource> resources = Arrays.asList(resource1, resource2);
        arg.setResources(resources);

        // Act
        ModifyResource.Result result = service.modifyResource(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(configurationPackageResourceLogicService).modifyResource(any(User.class), eq(resources));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试countByResourceType方法统计资源数量
     */
    @Test
    @DisplayName("测试countByResourceType方法统计资源数量")
    void testCountByResourceType() {
        // Arrange
        CountByResourceType.Arg arg = new CountByResourceType.Arg();
        arg.setResourceParentValue("parent_value");
        arg.setResourceType("mt_field");

        when(configurationPackageResourceLogicService.countByResourceType(
                any(User.class), eq("parent_value"), eq("mt_field")))
                .thenReturn(5);

        // Act
        CountByResourceType.Result result = service.countByResourceType(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertEquals(Integer.valueOf(5), result.getTotal());
        verify(configurationPackageResourceLogicService).countByResourceType(
                any(User.class), eq("parent_value"), eq("mt_field"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findFieldControlConfigs方法查找字段控制配置
     */
    @Test
    @DisplayName("测试findFieldControlConfigs方法查找字段控制配置")
    void testFindFieldControlConfigs() {
        // Arrange
        FindFieldControlConfigs.Arg arg = spy(new FindFieldControlConfigs.Arg());
        arg.setDescribeApiName("AccountObj");

        MtResource mockMtResource = new MtResource();
        mockMtResource.setResourceType("mt_field");
        mockMtResource.setResourceValue("field_value");
        mockMtResource.setControlLevel("0");

        List<MtResource> mockMtResources = Arrays.asList(mockMtResource);
        when(configurationPackageResourceLogicService.findFieldControlConfigs(
                any(User.class), eq("AccountObj")))
                .thenReturn(mockMtResources);

        // Act
        FindFieldControlConfigs.Result result = service.findFieldControlConfigs(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(arg).validate();
        verify(configurationPackageResourceLogicService).findFieldControlConfigs(
                any(User.class), eq("AccountObj"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试updateFieldControlConfigs方法更新字段控制配置
     */
    @Test
    @DisplayName("测试updateFieldControlConfigs方法更新字段控制配置")
    void testUpdateFieldControlConfigs() {
        // Arrange
        UpdateFieldControlConfigs.Arg arg = mock(UpdateFieldControlConfigs.Arg.class);
        FieldControlInfoHelper mockHelper = mock(FieldControlInfoHelper.class);
        
        when(arg.getDescribeApiName()).thenReturn("AccountObj");
        when(arg.toFieldControlInfoHelper()).thenReturn(mockHelper);

        // Act
        UpdateFieldControlConfigs.Result result = service.updateFieldControlConfigs(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(arg).toFieldControlInfoHelper();
        verify(configurationPackageResourceLogicService).updateFieldControlConfigs(
                any(User.class), eq("AccountObj"), eq(mockHelper));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建ModifyResource.Arg的工具方法
     */
    @Test
    @DisplayName("测试构建ModifyResource.Arg的工具方法")
    void testBuildModifyResourceArg() {
        // Arrange
        String tenantId = "74255";
        String describeApiName = "object_2mmga__c";
        List<String> fieldNames = Arrays.asList("name", "field_jH19d__c");

        // Act - 模拟Groovy测试中的构建逻辑
        List<ConfigurationPackageResource> fieldResources = makeFieldResources(tenantId, describeApiName, fieldNames);
        List<ConfigurationPackageResource> describeResources = makeDescribeResources(tenantId, describeApiName);
        
        ModifyResource.Arg arg = new ModifyResource.Arg();
        fieldResources.addAll(describeResources);
        arg.setResources(fieldResources);

        // Assert
        assertNotNull(arg);
        assertNotNull(arg.getResources());
        assertEquals(3, arg.getResources().size()); // 2 field resources + 1 describe resource
        
        // 验证字段资源
        long fieldResourceCount = arg.getResources().stream()
                .filter(r -> "mt_field".equals(r.getResourceType()))
                .count();
        assertEquals(2, fieldResourceCount);
        
        // 验证描述资源
        long describeResourceCount = arg.getResources().stream()
                .filter(r -> "mt_describe".equals(r.getResourceType()))
                .count();
        assertEquals(1, describeResourceCount);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(configurationPackageResourceLogicService);
        assertNotNull(mtResourceService);
    }

    // 辅助方法，模拟Groovy测试中的构建逻辑
    private List<ConfigurationPackageResource> makeFieldResources(String tenantId, String describeApiName, List<String> fieldNames) {
        return fieldNames.stream()
                .map(fieldName -> {
                    List<ConfigurationPackageResource.ResourceValue> resourceValues = makeResourceValues(tenantId, describeApiName, fieldName);
                    return makeConfigurationPackageResource("mt_field", resourceValues);
                })
                .collect(java.util.stream.Collectors.toList());
    }

    private List<ConfigurationPackageResource> makeDescribeResources(String tenantId, String describeApiName) {
        List<ConfigurationPackageResource.ResourceValue> resourceValues = makeResourceValues(tenantId, describeApiName, null);
        return Arrays.asList(makeConfigurationPackageResource("mt_describe", resourceValues));
    }

    private ConfigurationPackageResource makeConfigurationPackageResource(String resourceType, List<ConfigurationPackageResource.ResourceValue> resourceValues) {
        ConfigurationPackageResource resource = new ConfigurationPackageResource();
        resource.setResourceType(resourceType);
        resource.setResourceValues(resourceValues);
        resource.setControlLevel("0");
        resource.setSourceType("brush_data");
        return resource;
    }

    private List<ConfigurationPackageResource.ResourceValue> makeResourceValues(String tenantId, String describeApiName, String fieldName) {
        List<ConfigurationPackageResource.ResourceValue> values = Arrays.asList(
                ConfigurationPackageResource.ResourceValue.of("tenant_id", tenantId),
                ConfigurationPackageResource.ResourceValue.of("describe_api_name", describeApiName)
        );
        
        if (fieldName != null && !fieldName.isEmpty()) {
            values = new java.util.ArrayList<>(values);
            values.add(ConfigurationPackageResource.ResourceValue.of("api_name", fieldName));
        }
        
        return values;
    }
}
