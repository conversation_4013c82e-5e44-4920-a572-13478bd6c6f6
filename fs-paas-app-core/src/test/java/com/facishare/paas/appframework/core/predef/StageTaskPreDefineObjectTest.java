package com.facishare.paas.appframework.core.predef;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StageTaskPreDefineObjectTest {

  @BeforeEach
  void setUp() {
    // 测试前的准备工作
  }

  @Test
  void testGetApiName() {
    // Given & When & Then
    assertEquals("StageTaskObj", StageTaskPreDefineObject.StageTaskObj.getApiName());
  }

  @Test
  void testGetPackageName() {
    // Given & When
    String packageName = StageTaskPreDefineObject.StageTaskObj.getPackageName();

    // Then
    assertEquals("com.facishare.paas.appframework.core.predef", packageName);
  }

  @Test
  void testGetDefaultActionClassInfo() {
    // Given
    String actionCode = "Add";

    // When
    ActionClassInfo actionClassInfo = StageTaskPreDefineObject.StageTaskObj.getDefaultActionClassInfo(actionCode);

    // Then
    assertNotNull(actionClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.action.StageTaskObjAddAction", 
                 actionClassInfo.getClassName());
  }

  @Test
  void testGetControllerClassInfo() {
    // Given
    String methodName = "List";

    // When
    ControllerClassInfo controllerClassInfo = StageTaskPreDefineObject.StageTaskObj.getControllerClassInfo(methodName);

    // Then
    assertNotNull(controllerClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.controller.StageTaskObjListController", 
                 controllerClassInfo.getClassName());
  }

  @Test
  void testInit() {
    // Given
    try (MockedStatic<PreDefineObjectRegistry> mockedRegistry = mockStatic(PreDefineObjectRegistry.class)) {
      
      // When
      StageTaskPreDefineObject.init();

      // Then
      mockedRegistry.verify(() -> PreDefineObjectRegistry.register(StageTaskPreDefineObject.StageTaskObj));
    }
  }

  @Test
  void testEnumValues() {
    // Given & When
    StageTaskPreDefineObject[] values = StageTaskPreDefineObject.values();

    // Then
    assertEquals(1, values.length);
    assertEquals(StageTaskPreDefineObject.StageTaskObj, values[0]);
  }

  @Test
  void testValueOf() {
    // Given & When
    StageTaskPreDefineObject object = StageTaskPreDefineObject.valueOf("StageTaskObj");

    // Then
    assertEquals(StageTaskPreDefineObject.StageTaskObj, object);
  }

  @Test
  void testValueOfWithInvalidName() {
    // Given & When & Then
    assertThrows(IllegalArgumentException.class, () -> {
      StageTaskPreDefineObject.valueOf("InvalidName");
    });
  }

  @Test
  void testGetDefaultActionClassInfoWithDifferentActionCodes() {
    // Test with different action codes
    String[] actionCodes = {"Add", "Edit", "Delete", "View", "List"};
    
    for (String actionCode : actionCodes) {
      // When
      ActionClassInfo actionClassInfo = StageTaskPreDefineObject.StageTaskObj.getDefaultActionClassInfo(actionCode);
      
      // Then
      assertNotNull(actionClassInfo);
      String expectedClassName = "com.facishare.paas.appframework.core.predef.action.StageTaskObj" + actionCode + "Action";
      assertEquals(expectedClassName, actionClassInfo.getClassName());
    }
  }

  @Test
  void testGetControllerClassInfoWithDifferentMethodNames() {
    // Test with different method names
    String[] methodNames = {"List", "Add", "Edit", "Delete", "View"};
    
    for (String methodName : methodNames) {
      // When
      ControllerClassInfo controllerClassInfo = StageTaskPreDefineObject.StageTaskObj.getControllerClassInfo(methodName);
      
      // Then
      assertNotNull(controllerClassInfo);
      String expectedClassName = "com.facishare.paas.appframework.core.predef.controller.StageTaskObj" + methodName + "Controller";
      assertEquals(expectedClassName, controllerClassInfo.getClassName());
    }
  }

  @Test
  void testGetDefaultActionClassInfoWithNullActionCode() {
    // Given
    String actionCode = null;

    // When
    ActionClassInfo actionClassInfo = StageTaskPreDefineObject.StageTaskObj.getDefaultActionClassInfo(actionCode);

    // Then
    assertNotNull(actionClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.action.StageTaskObjnullAction", 
                 actionClassInfo.getClassName());
  }

  @Test
  void testGetControllerClassInfoWithNullMethodName() {
    // Given
    String methodName = null;

    // When
    ControllerClassInfo controllerClassInfo = StageTaskPreDefineObject.StageTaskObj.getControllerClassInfo(methodName);

    // Then
    assertNotNull(controllerClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.controller.StageTaskObjnullController", 
                 controllerClassInfo.getClassName());
  }

  @Test
  void testGetDefaultActionClassInfoWithEmptyActionCode() {
    // Given
    String actionCode = "";

    // When
    ActionClassInfo actionClassInfo = StageTaskPreDefineObject.StageTaskObj.getDefaultActionClassInfo(actionCode);

    // Then
    assertNotNull(actionClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.action.StageTaskObjAction", 
                 actionClassInfo.getClassName());
  }

  @Test
  void testGetControllerClassInfoWithEmptyMethodName() {
    // Given
    String methodName = "";

    // When
    ControllerClassInfo controllerClassInfo = StageTaskPreDefineObject.StageTaskObj.getControllerClassInfo(methodName);

    // Then
    assertNotNull(controllerClassInfo);
    assertEquals("com.facishare.paas.appframework.core.predef.controller.StageTaskObjController", 
                 controllerClassInfo.getClassName());
  }

  @Test
  void testInitRegistersAllEnumValues() {
    // Given
    try (MockedStatic<PreDefineObjectRegistry> mockedRegistry = mockStatic(PreDefineObjectRegistry.class)) {
      
      // When
      StageTaskPreDefineObject.init();

      // Then
      // 验证所有枚举值都被注册
      for (StageTaskPreDefineObject object : StageTaskPreDefineObject.values()) {
        mockedRegistry.verify(() -> PreDefineObjectRegistry.register(object));
      }
    }
  }

  @Test
  void testPackageNameConsistency() {
    // Given & When
    String packageName1 = StageTaskPreDefineObject.StageTaskObj.getPackageName();
    String packageName2 = StageTaskPreDefineObject.class.getPackage().getName();

    // Then
    assertEquals(packageName2, packageName1);
  }

  @Test
  void testActionClassInfoFormat() {
    // Given
    String actionCode = "TestAction";

    // When
    ActionClassInfo actionClassInfo = StageTaskPreDefineObject.StageTaskObj.getDefaultActionClassInfo(actionCode);

    // Then
    String expectedPattern = "com.facishare.paas.appframework.core.predef.action.StageTaskObjTestActionAction";
    assertEquals(expectedPattern, actionClassInfo.getClassName());
  }

  @Test
  void testControllerClassInfoFormat() {
    // Given
    String methodName = "TestMethod";

    // When
    ControllerClassInfo controllerClassInfo = StageTaskPreDefineObject.StageTaskObj.getControllerClassInfo(methodName);

    // Then
    String expectedPattern = "com.facishare.paas.appframework.core.predef.controller.StageTaskObjTestMethodController";
    assertEquals(expectedPattern, controllerClassInfo.getClassName());
  }
}
