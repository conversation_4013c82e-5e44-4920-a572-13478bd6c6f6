package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ParamsIdempotentServiceTest {

    @Mock
    private RedissonServiceImpl redissonService;
    
    @Mock
    private RLock rLock;
    
    @InjectMocks
    private ParamsIdempotentService paramsIdempotentService;
    
    private User testUser;
    private ObjectDataDocument testObjectData;
    private Map<String, List<ObjectDataDocument>> testDetails;

    @BeforeEach
    void setUp() {
        testUser = new User("test-tenant", "test-user");
        
        testObjectData = new ObjectDataDocument();
        testObjectData.put("field1", "value1");
        testObjectData.put("field2", "value2");
        
        testDetails = new HashMap<>();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取分布式锁的功能，验证能正确生成key并获取锁
     */
    @Test
    @DisplayName("测试获取锁 - 正常情况")
    void testGetLock_Success() {
        // Arrange
        String expectedKey = anyString();
        when(redissonService.getLock(expectedKey)).thenReturn(rLock);

        // Act
        RLock result = paramsIdempotentService.getLock(testUser, testObjectData, testDetails);

        // Assert
        assertNotNull(result);
        assertSame(rLock, result);
        verify(redissonService).getLock(anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取锁功能，验证传入null参数时的处理
     */
    @Test
    @DisplayName("测试获取锁 - 空详情Map")
    void testGetLock_WithEmptyDetails() {
        // Arrange
        when(redissonService.getLock(anyString())).thenReturn(rLock);

        // Act
        RLock result = paramsIdempotentService.getLock(testUser, testObjectData, null);

        // Assert
        assertNotNull(result);
        verify(redissonService).getLock(anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试加锁功能，验证成功获取锁的情况
     */
    @Test
    @DisplayName("测试加锁 - 成功获取锁")
    void testLock_Success() throws InterruptedException {
        // Arrange
        when(rLock.tryLock(eq(-1L), eq(60L), eq(TimeUnit.SECONDS))).thenReturn(true);

        // Act
        boolean result = paramsIdempotentService.lock(rLock);

        // Assert
        assertTrue(result);
        verify(rLock).tryLock(eq(-1L), eq(60L), eq(TimeUnit.SECONDS));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试加锁功能，验证获取锁失败的情况
     */
    @Test
    @DisplayName("测试加锁 - 获取锁失败")
    void testLock_Failure() throws InterruptedException {
        // Arrange
        when(rLock.tryLock(eq(-1L), eq(60L), eq(TimeUnit.SECONDS))).thenReturn(false);

        // Act
        boolean result = paramsIdempotentService.lock(rLock);

        // Assert
        assertFalse(result);
        verify(rLock).tryLock(eq(-1L), eq(60L), eq(TimeUnit.SECONDS));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试加锁功能，验证发生中断异常时的处理
     */
    @Test
    @DisplayName("测试加锁 - 中断异常")
    void testLock_InterruptedException() throws InterruptedException {
        // Arrange
        when(rLock.tryLock(eq(-1L), eq(60L), eq(TimeUnit.SECONDS)))
                .thenThrow(new InterruptedException("Test interruption"));

        // Act
        boolean result = paramsIdempotentService.lock(rLock);

        // Assert
        assertFalse(result);
        assertTrue(Thread.currentThread().isInterrupted());
        verify(rLock).tryLock(eq(-1L), eq(60L), eq(TimeUnit.SECONDS));
        
        // 清除中断状态，避免影响其他测试
        Thread.interrupted();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解锁功能，验证正常解锁的情况
     */
    @Test
    @DisplayName("测试解锁 - 正常情况")
    void testUnlock_Success() {
        // Arrange
        doNothing().when(rLock).unlock();

        // Act
        paramsIdempotentService.unlock(rLock);

        // Assert
        verify(rLock).unlock();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解锁功能，验证锁为null时的处理
     */
    @Test
    @DisplayName("测试解锁 - 锁为null")
    void testUnlock_NullLock() {
        // Act
        paramsIdempotentService.unlock(null);

        // Assert - 不应该抛出异常，方法应该正常返回
        // 由于是null，不应该有任何方法调用
        verifyNoInteractions(rLock);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解锁功能，验证解锁时发生异常的处理
     */
    @Test
    @DisplayName("测试解锁 - 解锁异常")
    void testUnlock_Exception() {
        // Arrange
        doThrow(new RuntimeException("Test unlock exception")).when(rLock).unlock();

        // Act - 不应该抛出异常
        assertDoesNotThrow(() -> paramsIdempotentService.unlock(rLock));

        // Assert
        verify(rLock).unlock();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数变化对key生成的影响，验证不同参数生成不同的锁
     */
    @Test
    @DisplayName("测试不同参数生成不同锁")
    void testGetLock_DifferentParameters() {
        // Arrange
        ObjectDataDocument differentObjectData = new ObjectDataDocument();
        differentObjectData.put("field1", "different_value");
        
        when(redissonService.getLock(anyString())).thenReturn(rLock);

        // Act
        RLock lock1 = paramsIdempotentService.getLock(testUser, testObjectData, testDetails);
        RLock lock2 = paramsIdempotentService.getLock(testUser, differentObjectData, testDetails);

        // Assert
        assertNotNull(lock1);
        assertNotNull(lock2);
        verify(redissonService, times(2)).getLock(anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试包含requestId字段的对象数据处理
     */
    @Test
    @DisplayName("测试包含requestId的对象数据")
    void testGetLock_WithRequestId() {
        // Arrange
        testObjectData.put("requestId", "test-request-id");
        when(redissonService.getLock(anyString())).thenReturn(rLock);

        // Act
        RLock result = paramsIdempotentService.getLock(testUser, testObjectData, testDetails);

        // Assert
        assertNotNull(result);
        // 验证requestId在处理后仍然存在
        assertTrue(testObjectData.containsKey("requestId"));
        assertEquals("test-request-id", testObjectData.get("requestId"));
        verify(redissonService).getLock(anyString());
    }
}