package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.language.Edit;
import com.facishare.paas.appframework.core.predef.service.dto.language.GetList;
import com.facishare.paas.appframework.core.predef.service.dto.language.LanguageInfo;
import com.facishare.paas.appframework.core.predef.service.dto.language.Update;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * MultiLanguageService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("MultiLanguageService单元测试")
class MultiLanguageServiceTest {

    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private ObjectDescribeServiceImpl objectDescribeService;

    @InjectMocks
    private MultiLanguageService multiLanguageService;

    private ServiceContext serviceContext;
    private User user;

    private static final String TENANT_ID = "78057";
    private static final String USER_ID = "1000";
    private static final String OBJECT_API_NAME = "TestObj";
    private static final String FIELD_API_NAME = "test_field__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .user(user)
                .tenantId(TENANT_ID)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "language", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象列表成功的场景
     */
    @Test
    @DisplayName("测试获取对象列表成功")
    void testGetObjectListSuccess() {
        // 使用MockedStatic来模拟I18nClient
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // Arrange
            GetList.Arg arg = new GetList.Arg();
            
            List<IObjectDescribe> mockDescribes = createMockObjectDescribes();
            I18nClient mockClient = mock(I18nClient.class);
            Map<String, Localization> mockMultiLanguageMap = createMockLocalizationMap();
            Map<String, Localization> mockPreDefineMap = createMockPreDefineMap();
            
            when(serviceFacade.findObjectsByTenantId(eq(TENANT_ID), eq(false), eq(false), eq(false), eq(false), any()))
                    .thenReturn(mockDescribes);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockClient);
            when(mockClient.get(anyList(), eq(Long.parseLong(TENANT_ID))))
                    .thenReturn(mockMultiLanguageMap);
            when(mockClient.getCommon(anyList()))
                    .thenReturn(mockPreDefineMap);

            // Act
            GetList.Result result = multiLanguageService.getObjectList(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getDescribes());
            assertEquals(1, result.getDescribes().size());
            assertEquals(OBJECT_API_NAME, result.getDescribes().get(0).getApiName());
            
            verify(serviceFacade).findObjectsByTenantId(eq(TENANT_ID), eq(false), eq(false), eq(false), eq(false), any());
            mockedI18nClient.verify(I18nClient::getInstance, times(2));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象列表时过滤特定对象的场景
     */
    @Test
    @DisplayName("测试获取对象列表时过滤特定对象")
    void testGetObjectListWithFiltering() {
        // 使用MockedStatic来模拟I18nClient
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // Arrange
            GetList.Arg arg = new GetList.Arg();
            
            List<IObjectDescribe> mockDescribes = Lists.newArrayList();
            // 添加需要过滤的对象
            IObjectDescribe filteredDescribe = mock(IObjectDescribe.class);
            when(filteredDescribe.getApiName()).thenReturn("ApprovalTaskObj");
            mockDescribes.add(filteredDescribe);
            
            // 添加正常对象
            mockDescribes.addAll(createMockObjectDescribes());
            
            I18nClient mockClient = mock(I18nClient.class);
            Map<String, Localization> mockMultiLanguageMap = createMockLocalizationMap();
            Map<String, Localization> mockPreDefineMap = createMockPreDefineMap();
            
            when(serviceFacade.findObjectsByTenantId(eq(TENANT_ID), eq(false), eq(false), eq(false), eq(false), any()))
                    .thenReturn(mockDescribes);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockClient);
            when(mockClient.get(anyList(), eq(Long.parseLong(TENANT_ID))))
                    .thenReturn(mockMultiLanguageMap);
            when(mockClient.getCommon(anyList()))
                    .thenReturn(mockPreDefineMap);

            // Act
            GetList.Result result = multiLanguageService.getObjectList(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getDescribes());
            // 应该只有1个对象，过滤掉了ApprovalTaskObj
            assertEquals(1, result.getDescribes().size());
            assertEquals(OBJECT_API_NAME, result.getDescribes().get(0).getApiName());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试编辑对象语言信息成功的场景
     */
    @Test
    @DisplayName("测试编辑对象语言信息成功")
    void testEditObjectLanguageSuccess() {
        // 使用MockedStatic来模拟I18nClient
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // Arrange
            Edit.Arg arg = new Edit.Arg();
            arg.setDescribeApiName(OBJECT_API_NAME);
            
            IObjectDescribe mockDescribe = createMockObjectDescribe();
            I18nClient mockClient = mock(I18nClient.class);
            
            when(serviceFacade.findObject(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                    .thenReturn(mockDescribe);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockClient);
            lenient().when(mockClient.get(anyString(), eq(Long.parseLong(TENANT_ID))))
                    .thenReturn(createMockLocalization());
            lenient().when(mockClient.getCommon(anyString()))
                    .thenReturn(createMockLocalization());
            when(mockClient.get(anyList(), eq(Long.parseLong(TENANT_ID))))
                    .thenReturn(createMockLocalizationMap());
            when(mockClient.getCommon(anyList()))
                    .thenReturn(createMockPreDefineMap());

            // Act
            Edit.Result result = multiLanguageService.edit(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getDescribe());
            assertNotNull(result.getFields());
            assertEquals(OBJECT_API_NAME, result.getDescribe().getApiName());
            assertEquals(1, result.getFields().size());
            
            verify(serviceFacade).findObject(eq(TENANT_ID), eq(OBJECT_API_NAME));
            mockedI18nClient.verify(I18nClient::getInstance, atLeastOnce());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新语言信息成功的场景
     */
    @Test
    @DisplayName("测试更新语言信息成功")
    void testUpdateLanguageSuccess() {
        // 使用MockedStatic来模拟I18nClient
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // Arrange
            Update.Arg arg = new Update.Arg();
            LanguageInfo describeInfo = LanguageInfo.builder()
                    .apiName(OBJECT_API_NAME)
                    .defineType("custom")
                    .preSimpleChinese("测试对象")
                    .traditional("測試對象")
                    .english("Test Object")
                    .build();
            
            LanguageInfo fieldInfo = LanguageInfo.builder()
                    .apiName(FIELD_API_NAME)
                    .defineType("custom")
                    .preSimpleChinese("测试字段")
                    .traditional("測試字段")
                    .english("Test Field")
                    .build();
            
            arg.setDescribe(describeInfo);
            arg.setFields(Lists.newArrayList(fieldInfo));
            
            I18nClient mockClient = mock(I18nClient.class);
            IObjectDescribe mockDescribe = createMockObjectDescribe();
            
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockClient);
            try {
                when(objectDescribeService.findByTenantIdAndDescribeApiName(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                        .thenReturn(mockDescribe);
            } catch (Exception e) {
                // Mock不会真正抛出异常
            }

            // Act
            Update.Result result = multiLanguageService.update(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            
            mockedI18nClient.verify(I18nClient::getInstance, atLeastOnce());
            verify(mockClient).build(any(Localization.class), eq(Lang.zh_CN.getValue()), eq("测试对象"));
            verify(mockClient).build(any(Localization.class), eq(Lang.zh_TW.getValue()), eq("測試對象"));
            verify(mockClient).build(any(Localization.class), eq(Lang.en.getValue()), eq("Test Object"));
            verify(mockClient).save(eq(Long.parseLong(TENANT_ID)), anyList(), eq(true));
            try {
                verify(objectDescribeService).touchDescribe(eq(mockDescribe), any());
            } catch (Exception e) {
                // Mock不会真正抛出异常
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新语言信息时发生MetadataServiceException的场景
     */
    @Test
    @DisplayName("测试更新语言信息时发生MetadataServiceException")
    void testUpdateLanguageWithMetadataServiceException() {
        // 使用MockedStatic来模拟I18nClient
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // Arrange
            Update.Arg arg = new Update.Arg();
            LanguageInfo describeInfo = LanguageInfo.builder()
                    .apiName(OBJECT_API_NAME)
                    .defineType("custom")
                    .preSimpleChinese("测试对象")
                    .traditional("測試對象")
                    .english("Test Object")
                    .build();
            
            arg.setDescribe(describeInfo);
            arg.setFields(Lists.newArrayList());
            
            I18nClient mockClient = mock(I18nClient.class);
            
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockClient);
            try {
                when(objectDescribeService.findByTenantIdAndDescribeApiName(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                        .thenThrow(new RuntimeException("测试异常"));
            } catch (Exception e) {
                // 处理异常
            }

            // Act & Assert
            assertThrows(RuntimeException.class, () -> {
                multiLanguageService.update(arg, serviceContext);
            });

            try {
                verify(objectDescribeService).findByTenantIdAndDescribeApiName(eq(TENANT_ID), eq(OBJECT_API_NAME));
            } catch (Exception e) {
                // 处理异常
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象列表时国际化数据为空的场景
     */
    @Test
    @DisplayName("测试获取对象列表时国际化数据为空")
    void testGetObjectListWithEmptyLocalization() {
        // 使用MockedStatic来模拟I18nClient
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // Arrange
            GetList.Arg arg = new GetList.Arg();
            
            List<IObjectDescribe> mockDescribes = createMockObjectDescribes();
            I18nClient mockClient = mock(I18nClient.class);
            Map<String, Localization> emptyMap = Maps.newHashMap();
            
            when(serviceFacade.findObjectsByTenantId(eq(TENANT_ID), eq(false), eq(false), eq(false), eq(false), any()))
                    .thenReturn(mockDescribes);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockClient);
            when(mockClient.get(anyList(), eq(Long.parseLong(TENANT_ID))))
                    .thenReturn(emptyMap);
            when(mockClient.getCommon(anyList()))
                    .thenReturn(emptyMap);

            // Act
            GetList.Result result = multiLanguageService.getObjectList(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getDescribes());
            assertEquals(1, result.getDescribes().size());
            // 当国际化数据为空时，应该使用对象的displayName
            assertEquals("测试对象", result.getDescribes().get(0).getSimpleChinese());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试编辑对象时国际化数据为空的场景
     */
    @Test
    @DisplayName("测试编辑对象时国际化数据为空")
    void testEditObjectWithEmptyLocalization() {
        // 使用MockedStatic来模拟I18nClient
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // Arrange
            Edit.Arg arg = new Edit.Arg();
            arg.setDescribeApiName(OBJECT_API_NAME);
            
            IObjectDescribe mockDescribe = createMockObjectDescribe();
            I18nClient mockClient = mock(I18nClient.class);
            Map<String, Localization> emptyMap = Maps.newHashMap();
            
            when(serviceFacade.findObject(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                    .thenReturn(mockDescribe);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockClient);
            lenient().when(mockClient.get(anyString(), eq(Long.parseLong(TENANT_ID))))
                    .thenReturn(null);
            lenient().when(mockClient.getCommon(anyString()))
                    .thenReturn(null);
            when(mockClient.get(anyList(), eq(Long.parseLong(TENANT_ID))))
                    .thenReturn(emptyMap);
            when(mockClient.getCommon(anyList()))
                    .thenReturn(emptyMap);

            // Act
            Edit.Result result = multiLanguageService.edit(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getDescribe());
            assertNotNull(result.getFields());
            // 当国际化数据为空时，应该使用对象的displayName
            assertEquals("测试对象", result.getDescribe().getSimpleChinese());
            assertEquals("测试字段", result.getFields().get(0).getSimpleChinese());
        }
    }

    // Helper methods for creating test data
    private List<IObjectDescribe> createMockObjectDescribes() {
        IObjectDescribe describe = createMockObjectDescribe();
        return Lists.newArrayList(describe);
    }

    private IObjectDescribe createMockObjectDescribe() {
        IObjectDescribe describe = mock(IObjectDescribe.class);
        lenient().when(describe.getApiName()).thenReturn(OBJECT_API_NAME);
        lenient().when(describe.getDisplayName()).thenReturn("测试对象");
        lenient().when(describe.getDefineType()).thenReturn("custom");

        IFieldDescribe field = createMockFieldDescribe();
        lenient().when(describe.getFieldDescribes()).thenReturn(Lists.newArrayList(field));

        return describe;
    }

    private IFieldDescribe createMockFieldDescribe() {
        IFieldDescribe field = mock(IFieldDescribe.class);
        lenient().when(field.getApiName()).thenReturn(FIELD_API_NAME);
        lenient().when(field.getLabel()).thenReturn("测试字段");
        lenient().when(field.getDefineType()).thenReturn("custom");
        lenient().when(field.getType()).thenReturn(IFieldType.TEXT);
        lenient().when(field.getConfig()).thenReturn(Maps.newHashMap());
        return field;
    }

    private Map<String, Localization> createMockLocalizationMap() {
        Map<String, Localization> map = Maps.newHashMap();
        String objectKey = "describe.displayName." + OBJECT_API_NAME;
        String fieldKey = "field.label." + OBJECT_API_NAME + "." + FIELD_API_NAME;
        
        map.put(objectKey, createMockLocalization());
        map.put(fieldKey, createMockLocalization());
        
        return map;
    }

    private Map<String, Localization> createMockPreDefineMap() {
        Map<String, Localization> map = Maps.newHashMap();
        String key = "describe.displayName." + OBJECT_API_NAME;
        map.put(key, createMockLocalization());
        return map;
    }

    private Localization createMockLocalization() {
        return mock(Localization.class);
    }
}