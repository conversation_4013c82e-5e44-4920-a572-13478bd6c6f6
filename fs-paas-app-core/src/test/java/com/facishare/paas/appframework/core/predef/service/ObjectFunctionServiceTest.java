package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.predef.service.dto.function.*;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.google.common.collect.Lists;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;


import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectFunctionService单元测试类
 * 测试函数管理服务的各种功能
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("ObjectFunctionService单元测试")
public class ObjectFunctionServiceTest extends BaseServiceTest {

    @Mock
    private FunctionLogicService functionLogicService;

    @Mock
    private FunctionPrivilegeService functionPrivilegeService;

    @InjectMocks
    private ObjectFunctionService objectFunctionService;

    private static final String FUNCTION_API_NAME = "testFunction";
    private static final String BINDING_OBJECT_API_NAME = "TestObj__c";
    private static final String FUNCTION_NAME = "Test Function";

    @Override
    protected String getServiceName() {
        return "function";
    }

    @BeforeEach
    void setUp() {
        // 基础设置已在BaseServiceTest中完成
    }


    @Test
    @DisplayName("GenerateByAI - 测试create方法成功场景")
    void testCreate_Success() {
        // Arrange
        CreateFunction.Arg arg = new CreateFunction.Arg();
        FunctionInfo functionInfo = new FunctionInfo();
        functionInfo.setApiName(FUNCTION_API_NAME);
        functionInfo.setFunctionName(FUNCTION_NAME);
        functionInfo.setBindingObjectApiName(BINDING_OBJECT_API_NAME);
        functionInfo.setIsActive(true);
        functionInfo.setBody("return 'test';");
        arg.setFunction(functionInfo);

        IUdefFunction mockFunction = mock(IUdefFunction.class);
        when(mockFunction.getApiName()).thenReturn(FUNCTION_API_NAME);
        when(functionLogicService.createFunctionByRest(eq(testUser), any(IUdefFunction.class)))
                .thenReturn(mockFunction);

        // Act
        CreateFunction.Result result = objectFunctionService.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getFunction());
        verify(functionLogicService, times(1)).createFunctionByRest(eq(testUser), any(IUdefFunction.class));
    }

    @Test
    @DisplayName("GenerateByAI - 测试update方法成功场景")
    void testUpdate_Success() {
        // Arrange
        UpdateFunction.Arg arg = new UpdateFunction.Arg();
        FunctionInfo functionInfo = new FunctionInfo();
        functionInfo.setApiName(FUNCTION_API_NAME);
        functionInfo.setFunctionName(FUNCTION_NAME);
        functionInfo.setBindingObjectApiName(BINDING_OBJECT_API_NAME);
        functionInfo.setIsActive(true);
        functionInfo.setBody("return 'test';");
        arg.setFunction(functionInfo);

        IUdefFunction mockFunction = mock(IUdefFunction.class);
        when(mockFunction.getApiName()).thenReturn(FUNCTION_API_NAME);
        when(functionLogicService.updateFunctionByRest(eq(testUser), any(IUdefFunction.class)))
                .thenReturn(mockFunction);

        // Act
        UpdateFunction.Result result = objectFunctionService.update(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getFunction());
        verify(functionLogicService, times(1)).updateFunctionByRest(eq(testUser), any(IUdefFunction.class));
    }

    @Test
    @DisplayName("GenerateByAI - 测试find方法成功场景")
    void testFind_Success() {
        // Arrange
        FindFunction.Arg arg = new FindFunction.Arg();
        arg.setApiName(FUNCTION_API_NAME);
        arg.setBindingObjectAPIName(BINDING_OBJECT_API_NAME);

        IUdefFunction mockFunction = mock(IUdefFunction.class);
        when(mockFunction.getApiName()).thenReturn(FUNCTION_API_NAME);
        when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, BINDING_OBJECT_API_NAME))
                .thenReturn(mockFunction);

        // Act
        FindFunction.Result result = objectFunctionService.find(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getFunction());
        verify(functionLogicService, times(1)).findUDefFunction(testUser, FUNCTION_API_NAME, BINDING_OBJECT_API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试delete方法成功场景")
    void testDelete_Success() {
        // Arrange
        DeleteFunction.Arg arg = new DeleteFunction.Arg();
        arg.setApiName(FUNCTION_API_NAME);
        arg.setBindingObjectAPIName(BINDING_OBJECT_API_NAME);

        when(functionLogicService.deleteUDefFunction(testUser, BINDING_OBJECT_API_NAME, FUNCTION_API_NAME))
                .thenReturn(true);

        // Act
        DeleteFunction.Result result = objectFunctionService.delete(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getSuccess());
        verify(functionLogicService, times(1)).deleteUDefFunction(testUser, BINDING_OBJECT_API_NAME, FUNCTION_API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试isActive方法成功场景")
    void testIsActive_Success() {
        // Arrange
        ActiveRule.Arg arg = new ActiveRule.Arg();
        arg.setApiName(FUNCTION_API_NAME);
        arg.setBindingObjectAPIName(BINDING_OBJECT_API_NAME);
        arg.setIsActive(true);

        when(functionLogicService.setIsActive(testUser, BINDING_OBJECT_API_NAME, FUNCTION_API_NAME, true))
                .thenReturn(true);

        // Act
        ActiveRule.Result result = objectFunctionService.isActive(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getSuccess());
        verify(functionLogicService, times(1)).setIsActive(testUser, BINDING_OBJECT_API_NAME, FUNCTION_API_NAME, true);
    }

    @Test
    @DisplayName("GenerateByAI - 测试funExist方法成功场景")
    void testFunExist_Success() {
        // Arrange
        FunctionExist.Arg arg = new FunctionExist.Arg();
        arg.setNameSpace(Lists.newArrayList("namespace1"));
        arg.setReturnTypeList(Lists.newArrayList("String"));
        arg.setBindingObjectApiName(BINDING_OBJECT_API_NAME);

        when(functionLogicService.funcIsExist(eq(testUser), any(List.class), any(List.class), eq(BINDING_OBJECT_API_NAME)))
                .thenReturn(true);

        // Act
        FunctionExist.Result result = objectFunctionService.funExist(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getIsExist());
        verify(functionLogicService, times(1)).funcIsExist(eq(testUser), any(List.class), any(List.class), eq(BINDING_OBJECT_API_NAME));
    }

    @Test
    @DisplayName("GenerateByAI - 测试analyze方法成功场景")
    void testAnalyze_Success() {
        // Arrange
        AnalyzeFunction.Arg arg = new AnalyzeFunction.Arg();
        FunctionInfo functionInfo = new FunctionInfo();
        functionInfo.setApiName(FUNCTION_API_NAME);
        functionInfo.setFunctionName(FUNCTION_NAME);
        functionInfo.setBindingObjectApiName(BINDING_OBJECT_API_NAME);
        functionInfo.setIsActive(true); // 设置isActive属性
        functionInfo.setBody("return 'test';");
        arg.setFunction(functionInfo);

        com.facishare.paas.appframework.function.dto.Analyze.Result mockAnalyzeResult =
            com.facishare.paas.appframework.function.dto.Analyze.Result.builder()
                .success(true)
                .build();

        when(functionLogicService.analyze(eq(testUser), any(IUdefFunction.class)))
                .thenReturn(mockAnalyzeResult);

        // Act
        com.facishare.paas.appframework.function.dto.Analyze.Result result = objectFunctionService.analyze(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(functionLogicService, times(1)).analyze(eq(testUser), any(IUdefFunction.class));
    }

    @Test
    @DisplayName("GenerateByAI - 测试checkFunctionCountLimit方法成功场景")
    void testCheckFunctionCountLimit_Success() {
        // Arrange
        doNothing().when(functionLogicService).checkFunctionCountLimit(testUser);

        // Act
        CheckFunctionCountLimit.Result result = objectFunctionService.checkFunctionCountLimit(serviceContext);

        // Assert
        assertNotNull(result);
        verify(functionLogicService, times(1)).checkFunctionCountLimit(testUser);
    }

    @Test
    @DisplayName("GenerateByAI - 测试batchObjectActionCodesPrivilegeCheck方法空参数场景")
    void testBatchObjectActionCodesPrivilegeCheck_NullParam() {
        // Arrange
        BatchObjectActionCodesPrivilegeCheck.Arg arg = new BatchObjectActionCodesPrivilegeCheck.Arg();
        arg.setApiName2ActionCodes(null); // 设置为null来测试验证逻辑

        // Act
        Object result = objectFunctionService.batchObjectActionCodesPrivilegeCheck(arg);

        // Assert
        assertNotNull(result);
        // 验证返回的是PrivilegeResult.ofFail
    }

    @Test
    @DisplayName("GenerateByAI - 测试batchFuncCodePrivilegeCheck方法空参数场景")
    void testBatchFuncCodePrivilegeCheck_EmptyParam() {
        // Arrange
        BatchFuncCodePrivilegeCheck.Arg arg = new BatchFuncCodePrivilegeCheck.Arg();
        arg.setFuncCodes(Lists.newArrayList()); // 设置为空列表

        // Act
        Object result = objectFunctionService.batchFuncCodePrivilegeCheck(arg);

        // Assert
        assertNotNull(result);
        // 当funcCodes为空时，应该返回空对象
    }

    @Test
    @DisplayName("GenerateByAI - 测试batchFunPrivilegeCheck方法空参数场景")
    void testBatchFunPrivilegeCheck_EmptyParam() {
        // Arrange
        BatchFunPrivilegeCheck.Arg arg = new BatchFunPrivilegeCheck.Arg();
        arg.setObjectApiNames(Lists.newArrayList()); // 设置为空列表
        arg.setActionCodes(Lists.newArrayList()); // 设置为空列表

        // Act
        Object result = objectFunctionService.batchFunPrivilegeCheck(arg);

        // Assert
        assertNotNull(result);
        // 当参数为空时，应该返回空对象
    }

    @Test
    @DisplayName("GenerateByAI - 测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectFunctionService);
        assertNotNull(functionLogicService);
        assertNotNull(functionPrivilegeService);
    }
}
