package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * WXOfficialAccountService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("WXOfficialAccountService单元测试")
class WXOfficialAccountServiceTest {

    @Mock
    private ServiceFacade serviceFacade;
    
    @InjectMocks
    private WXOfficialAccountService wxOfficialAccountService;
    
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取所有微信模板列表成功的场景
     */
    @Test
    @DisplayName("测试getAllWXTemplatedList成功")
    void testGetAllWXTemplatedListSuccess() {
        // Arrange
        IObjectData mockTemplate1 = mock(IObjectData.class);
        when(mockTemplate1.getId()).thenReturn("template1");
        
        IObjectData mockTemplate2 = mock(IObjectData.class);
        when(mockTemplate2.getId()).thenReturn("template2");
        
        List<IObjectData> mockTemplateList = Arrays.asList(mockTemplate1, mockTemplate2);
        
        QueryResult<IObjectData> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(mockTemplateList);

        when(serviceFacade.findBySearchQueryIgnoreAll(eq(user), any(String.class), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // Act
        List<IObjectData> result = wxOfficialAccountService.getAllWXTemplatedList(user);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("template1", result.get(0).getId());
        assertEquals("template2", result.get(1).getId());
        verify(serviceFacade).findBySearchQueryIgnoreAll(eq(user), any(String.class), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取所有微信模板列表时返回空列表的场景
     */
    @Test
    @DisplayName("测试getAllWXTemplatedList返回空列表")
    void testGetAllWXTemplatedListReturnsEmptyList() {
        // Arrange
        QueryResult<IObjectData> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(Arrays.asList());

        when(serviceFacade.findBySearchQueryIgnoreAll(eq(user), any(String.class), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // Act
        List<IObjectData> result = wxOfficialAccountService.getAllWXTemplatedList(user);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(serviceFacade).findBySearchQueryIgnoreAll(eq(user), any(String.class), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用自定义查询获取微信模板列表成功的场景
     */
    @Test
    @DisplayName("测试getlWXTemplatedList成功")
    void testGetlWXTemplatedListSuccess() {
        // Arrange
        SearchTemplateQuery customQuery = new SearchTemplateQuery();
        customQuery.setLimit(500);
        
        IObjectData mockTemplate = mock(IObjectData.class);
        when(mockTemplate.getId()).thenReturn("customTemplate");
        
        List<IObjectData> mockTemplateList = Arrays.asList(mockTemplate);
        
        QueryResult<IObjectData> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(mockTemplateList);

        when(serviceFacade.findBySearchQueryIgnoreAll(eq(user), any(String.class), eq(customQuery)))
                .thenReturn(mockQueryResult);

        // Act
        List<IObjectData> result = wxOfficialAccountService.getlWXTemplatedList(user, customQuery);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("customTemplate", result.get(0).getId());
        verify(serviceFacade).findBySearchQueryIgnoreAll(eq(user), any(String.class), eq(customQuery));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用自定义查询获取微信模板列表时查询为null的场景
     */
    @Test
    @DisplayName("测试getlWXTemplatedList查询为null")
    void testGetlWXTemplatedListWithNullQuery() {
        // Arrange
        QueryResult<IObjectData> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(Arrays.asList());

        when(serviceFacade.findBySearchQueryIgnoreAll(eq(user), any(String.class), eq(null)))
                .thenReturn(mockQueryResult);

        // Act
        List<IObjectData> result = wxOfficialAccountService.getlWXTemplatedList(user, null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(serviceFacade).findBySearchQueryIgnoreAll(eq(user), any(String.class), eq(null));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取微信模板列表时用户为null的场景
     */
    @Test
    @DisplayName("测试getAllWXTemplatedList用户为null")
    void testGetAllWXTemplatedListWithNullUser() {
        // Arrange
        QueryResult<IObjectData> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(Arrays.asList());

        when(serviceFacade.findBySearchQueryIgnoreAll(eq(null), any(String.class), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // Act
        List<IObjectData> result = wxOfficialAccountService.getAllWXTemplatedList(null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(serviceFacade).findBySearchQueryIgnoreAll(eq(null), any(String.class), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取微信模板列表时ServiceFacade返回null的场景
     */
    @Test
    @DisplayName("测试getAllWXTemplatedList ServiceFacade返回null")
    void testGetAllWXTemplatedListWithNullResult() {
        // Arrange
        when(serviceFacade.findBySearchQueryIgnoreAll(eq(user), any(String.class), any(SearchTemplateQuery.class)))
                .thenReturn(null);

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            wxOfficialAccountService.getAllWXTemplatedList(user);
        });
        
        verify(serviceFacade).findBySearchQueryIgnoreAll(eq(user), any(String.class), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(wxOfficialAccountService);
        assertNotNull(serviceFacade);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Assert
        assertNotNull(user);
        assertEquals(TENANT_ID, user.getTenantId());
        assertEquals(USER_ID, user.getUserId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试默认查询限制设置
     */
    @Test
    @DisplayName("测试默认查询限制设置")
    void testDefaultQueryLimitSetting() {
        // Arrange
        QueryResult<IObjectData> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(Arrays.asList());

        when(serviceFacade.findBySearchQueryIgnoreAll(eq(user), any(String.class), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // Act
        wxOfficialAccountService.getAllWXTemplatedList(user);

        // Assert - 验证查询限制被设置为1000
        verify(serviceFacade).findBySearchQueryIgnoreAll(eq(user), any(String.class), argThat(query -> 
            query != null && query.getLimit() == 1000));
    }
}
