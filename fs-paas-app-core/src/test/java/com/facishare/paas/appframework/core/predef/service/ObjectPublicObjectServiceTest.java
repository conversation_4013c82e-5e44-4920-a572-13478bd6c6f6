package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.publicobject.*;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectLogicService;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicFieldDTO;

import com.facishare.paas.appframework.metadata.publicobject.module.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectPublicObjectService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectPublicObjectService单元测试")
class ObjectPublicObjectServiceTest {

    @Mock
    private PublicObjectLogicService publicObjectLogicService;

    @InjectMocks
    private ObjectPublicObjectService objectPublicObjectService;

    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OBJECT_API_NAME = "test_object__c";
    private final String JOB_ID = "test_job_id";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);

        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();

        serviceContext = new ServiceContext(requestContext, "public_object", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询公共对象状态成功的场景
     */
    @Test
    @DisplayName("测试查询公共对象状态成功")
    void testQueryStatusSuccess() {
        // Arrange
        QueryPublicObjectStatus.Arg arg = new QueryPublicObjectStatus.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);

        PublicObjectStatusResult mockResult = PublicObjectStatusResult.builder()
                .publicObjectStatus(PublicObjectStatusType.ENABLE)
                .jobId(JOB_ID)
                .jobType("open_job")
                .build();

        when(publicObjectLogicService.queryStatus(user, OBJECT_API_NAME)).thenReturn(mockResult);

        // Act
        QueryPublicObjectStatus.Result result = objectPublicObjectService.queryStatus(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals("enable", result.getStatus());
        assertEquals(JOB_ID, result.getJobId());
        assertEquals("open_job", result.getJobType());
        verify(publicObjectLogicService).queryStatus(user, OBJECT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找设计器资源成功的场景
     */
    @Test
    @DisplayName("测试查找设计器资源成功")
    void testFindDesignerResourceSuccess() {
        // Arrange
        FindDesignerResource.Arg arg = new FindDesignerResource.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);

        DesignerResourceResult mockResult = mock(DesignerResourceResult.class);

        when(publicObjectLogicService.findDesignerResource(user, OBJECT_API_NAME)).thenReturn(mockResult);

        // Act
        FindDesignerResource.Result result = objectPublicObjectService.findDesignerResource(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(publicObjectLogicService).findDesignerResource(user, OBJECT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建开启任务成功的场景
     */
    @Test
    @DisplayName("测试创建开启任务成功")
    void testCreateOpenJobSuccess() {
        // Arrange
        CreatePublicObjectJob.Arg arg = new CreatePublicObjectJob.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setFields(Collections.emptyList());
        arg.setEnterpriseInfos(Collections.emptyList());

        when(publicObjectLogicService.createJob(eq(user), any())).thenReturn(JOB_ID);

        // Act
        CreatePublicObjectJob.Result result = objectPublicObjectService.createOpenJob(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(JOB_ID, result.getJobId());
        verify(publicObjectLogicService).createJob(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证任务成功的场景
     */
    @Test
    @DisplayName("测试创建验证任务成功")
    void testCreateVerifyJobSuccess() {
        // Arrange
        CreatePublicObjectJob.Arg arg = new CreatePublicObjectJob.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setFields(Collections.emptyList());
        arg.setEnterpriseInfos(Collections.emptyList());

        when(publicObjectLogicService.createJob(eq(user), any())).thenReturn(JOB_ID);

        // Act
        CreatePublicObjectJob.Result result = objectPublicObjectService.createVerifyJob(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(JOB_ID, result.getJobId());
        verify(publicObjectLogicService).createJob(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询任务成功的场景
     */
    @Test
    @DisplayName("测试查询任务成功")
    void testQueryJobSuccess() {
        // Arrange
        QueryPublicObjectJob.Arg arg = new QueryPublicObjectJob.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setJobId(JOB_ID);

        PublicObjectJobResult mockResult = PublicObjectJobResult.builder()
                .jobType(PublicObjectJobType.OPEN_JOB)
                .jobStatus(PublicObjectJobStatus.SUCCESS)
                .build();

        when(publicObjectLogicService.queryJob(user, OBJECT_API_NAME, JOB_ID)).thenReturn(mockResult);

        // Act
        QueryPublicObjectJob.Result result = objectPublicObjectService.queryJob(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(publicObjectLogicService).queryJob(user, OBJECT_API_NAME, JOB_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建启用任务成功的场景
     */
    @Test
    @DisplayName("测试创建启用任务成功")
    void testCreateEnableJobSuccess() {
        // Arrange
        CreatePublicObjectJob.Arg arg = new CreatePublicObjectJob.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setFields(Collections.emptyList());
        arg.setEnterpriseInfos(Collections.emptyList());

        when(publicObjectLogicService.createJob(eq(user), any())).thenReturn(JOB_ID);

        // Act
        CreatePublicObjectJob.Result result = objectPublicObjectService.createEnableJob(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(JOB_ID, result.getJobId());
        verify(publicObjectLogicService).createJob(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建禁用任务成功的场景
     */
    @Test
    @DisplayName("测试创建禁用任务成功")
    void testCreateDisableJobSuccess() {
        // Arrange
        CreatePublicObjectJob.Arg arg = new CreatePublicObjectJob.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setFields(Collections.emptyList());
        arg.setEnterpriseInfos(Collections.emptyList());

        when(publicObjectLogicService.createJob(eq(user), any())).thenReturn(JOB_ID);

        // Act
        CreatePublicObjectJob.Result result = objectPublicObjectService.createDisableJob(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(JOB_ID, result.getJobId());
        verify(publicObjectLogicService).createJob(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询状态时对象不存在的场景
     */
    @Test
    @DisplayName("测试查询状态 - 对象不存在")
    void testQueryStatusObjectNotFound() {
        // Arrange
        QueryPublicObjectStatus.Arg arg = new QueryPublicObjectStatus.Arg();
        arg.setObjectApiName("nonexistent_object__c");

        PublicObjectStatusResult mockResult = PublicObjectStatusResult.builder()
                .publicObjectStatus(PublicObjectStatusType.DISABLE)
                .build();

        when(publicObjectLogicService.queryStatus(user, "nonexistent_object__c")).thenReturn(mockResult);

        // Act
        QueryPublicObjectStatus.Result result = objectPublicObjectService.queryStatus(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals("disable", result.getStatus());
        verify(publicObjectLogicService).queryStatus(user, "nonexistent_object__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试追加公共字段成功的场景
     */
    @Test
    @DisplayName("测试追加公共字段成功")
    void testAppendPublicFieldsSuccess() {
        // Arrange
        AppendPublicFields.Arg arg = new AppendPublicFields.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);

        PublicFieldDTO field = new PublicFieldDTO();
        field.setFieldApiName("test_field__c");
        arg.setFields(Arrays.asList(field));

        // Act
        AppendPublicFields.Result result = objectPublicObjectService.appendPublicFields(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(publicObjectLogicService).appendPublicFields(user, OBJECT_API_NAME, arg.getFields());
    }
}
