package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.button.FilterButtonsManager;
import com.facishare.paas.appframework.metadata.button.FilterButtonsProvider;
import com.facishare.paas.appframework.metadata.button.FilterButtonsProviderProxy;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.UdefButton;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectFilterButtonsProviderService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectFilterButtonsProviderService单元测试")
class ObjectFilterButtonsProviderServiceTest {

    @Mock
    private FilterButtonsManager findButtonsByDescribeApiNameManager;
    
    @Mock
    private FilterButtonsProvider filterButtonsProvider;
    
    @InjectMocks
    private ObjectFilterButtonsProviderService objectFilterButtonsProviderService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "FilterButtonsProvider", "test");
    }

    /**
     * 创建测试用的UdefButton对象
     * 使用真实的UdefButton实例而非Mock对象，避免ClassCastException
     */
    private IUdefButton createTestButton(String apiName, String label) {
        Map<String, Object> buttonMap = new HashMap<>();
        buttonMap.put("apiName", apiName);
        buttonMap.put("label", label);
        buttonMap.put("buttonType", "common");
        buttonMap.put("isActive", true);
        buttonMap.put("isDeleted", false);
        buttonMap.put("describeApiName", DESCRIBE_API_NAME);
        return new UdefButton(buttonMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找过滤按钮成功的场景
     */
    @Test
    @DisplayName("测试findButtons成功")
    void testFindButtonsSuccess() {
        // Arrange
        FilterButtonsProviderProxy.Arg arg = new FilterButtonsProviderProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        
        Map<String, Object> buttonMap1 = new HashMap<>();
        buttonMap1.put("apiName", "filter1");
        buttonMap1.put("label", "Filter 1");
        
        Map<String, Object> buttonMap2 = new HashMap<>();
        buttonMap2.put("apiName", "filter2");
        buttonMap2.put("label", "Filter 2");
        
        arg.setButtonList(Arrays.asList(buttonMap1, buttonMap2));

        IUdefButton testButton1 = createTestButton("filter_button_1", "Filter Button 1");
        IUdefButton testButton2 = createTestButton("filter_button_2", "Filter Button 2");
        List<IUdefButton> testResultButtons = Arrays.asList(testButton1, testButton2);

        when(findButtonsByDescribeApiNameManager.getLocalProvider(DESCRIBE_API_NAME))
                .thenReturn(filterButtonsProvider);
        when(filterButtonsProvider.getButtons(eq(user), any()))
                .thenReturn(testResultButtons);

        // Act
        FilterButtonsProviderProxy.Result result = objectFilterButtonsProviderService.findButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(2, result.getResult().size());
        verify(findButtonsByDescribeApiNameManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(filterButtonsProvider).getButtons(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找过滤按钮时按钮列表为空的场景
     */
    @Test
    @DisplayName("测试findButtons按钮列表为空")
    void testFindButtonsWithEmptyButtonList() {
        // Arrange
        FilterButtonsProviderProxy.Arg arg = new FilterButtonsProviderProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setButtonList(Arrays.asList());

        when(findButtonsByDescribeApiNameManager.getLocalProvider(DESCRIBE_API_NAME))
                .thenReturn(filterButtonsProvider);
        when(filterButtonsProvider.getButtons(eq(user), any()))
                .thenReturn(Arrays.asList());

        // Act
        FilterButtonsProviderProxy.Result result = objectFilterButtonsProviderService.findButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertTrue(result.getResult().isEmpty());
        verify(findButtonsByDescribeApiNameManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(filterButtonsProvider).getButtons(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找过滤按钮时按钮列表为null的场景
     */
    @Test
    @DisplayName("测试findButtons按钮列表为null")
    void testFindButtonsWithNullButtonList() {
        // Arrange
        FilterButtonsProviderProxy.Arg arg = new FilterButtonsProviderProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setButtonList(null);

        when(findButtonsByDescribeApiNameManager.getLocalProvider(DESCRIBE_API_NAME))
                .thenReturn(filterButtonsProvider);
        when(filterButtonsProvider.getButtons(eq(user), any()))
                .thenReturn(Arrays.asList());

        // Act
        FilterButtonsProviderProxy.Result result = objectFilterButtonsProviderService.findButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertTrue(result.getResult().isEmpty());
        verify(findButtonsByDescribeApiNameManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(filterButtonsProvider).getButtons(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找过滤按钮时返回结果为空的场景
     */
    @Test
    @DisplayName("测试findButtons返回结果为空")
    void testFindButtonsReturnsEmptyResult() {
        // Arrange
        FilterButtonsProviderProxy.Arg arg = new FilterButtonsProviderProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        
        Map<String, Object> buttonMap = new HashMap<>();
        buttonMap.put("apiName", "filter1");
        arg.setButtonList(Arrays.asList(buttonMap));

        when(findButtonsByDescribeApiNameManager.getLocalProvider(DESCRIBE_API_NAME))
                .thenReturn(filterButtonsProvider);
        when(filterButtonsProvider.getButtons(eq(user), any()))
                .thenReturn(Arrays.asList());

        // Act
        FilterButtonsProviderProxy.Result result = objectFilterButtonsProviderService.findButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertTrue(result.getResult().isEmpty());
        verify(findButtonsByDescribeApiNameManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(filterButtonsProvider).getButtons(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找过滤按钮时返回结果为null的场景
     */
    @Test
    @DisplayName("测试findButtons返回结果为null")
    void testFindButtonsReturnsNullResult() {
        // Arrange
        FilterButtonsProviderProxy.Arg arg = new FilterButtonsProviderProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        
        Map<String, Object> buttonMap = new HashMap<>();
        buttonMap.put("apiName", "filter1");
        arg.setButtonList(Arrays.asList(buttonMap));

        when(findButtonsByDescribeApiNameManager.getLocalProvider(DESCRIBE_API_NAME))
                .thenReturn(filterButtonsProvider);
        when(filterButtonsProvider.getButtons(eq(user), any()))
                .thenReturn(null);

        // Act
        FilterButtonsProviderProxy.Result result = objectFilterButtonsProviderService.findButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertTrue(result.getResult().isEmpty());
        verify(findButtonsByDescribeApiNameManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(filterButtonsProvider).getButtons(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找过滤按钮时Provider为null的场景
     */
    @Test
    @DisplayName("测试findButtons时Provider为null")
    void testFindButtonsWithNullProvider() {
        // Arrange
        FilterButtonsProviderProxy.Arg arg = new FilterButtonsProviderProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setButtonList(Arrays.asList());

        when(findButtonsByDescribeApiNameManager.getLocalProvider(DESCRIBE_API_NAME))
                .thenReturn(null);

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            objectFilterButtonsProviderService.findButtons(arg, serviceContext);
        });

        verify(findButtonsByDescribeApiNameManager).getLocalProvider(DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找过滤按钮时包含复杂按钮数据的场景
     */
    @Test
    @DisplayName("测试findButtons包含复杂按钮数据")
    void testFindButtonsWithComplexButtonData() {
        // Arrange
        FilterButtonsProviderProxy.Arg arg = new FilterButtonsProviderProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        
        Map<String, Object> complexButtonMap = new HashMap<>();
        complexButtonMap.put("apiName", "complexFilter");
        complexButtonMap.put("label", "Complex Filter");
        complexButtonMap.put("type", "custom");
        complexButtonMap.put("enabled", true);
        complexButtonMap.put("visible", true);
        complexButtonMap.put("filterType", "advanced");
        
        arg.setButtonList(Arrays.asList(complexButtonMap));

        IUdefButton testComplexButton = createTestButton("complexFilter", "Complex Filter");
        List<IUdefButton> testResultButtons = Arrays.asList(testComplexButton);

        when(findButtonsByDescribeApiNameManager.getLocalProvider(DESCRIBE_API_NAME))
                .thenReturn(filterButtonsProvider);
        when(filterButtonsProvider.getButtons(eq(user), any()))
                .thenReturn(testResultButtons);

        // Act
        FilterButtonsProviderProxy.Result result = objectFilterButtonsProviderService.findButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(1, result.getResult().size());
        verify(findButtonsByDescribeApiNameManager).getLocalProvider(DESCRIBE_API_NAME);
        verify(filterButtonsProvider).getButtons(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找过滤按钮时参数为空的场景
     */
    @Test
    @DisplayName("测试findButtons参数为空")
    void testFindButtonsWithEmptyDescribeApiName() {
        // Arrange
        FilterButtonsProviderProxy.Arg arg = new FilterButtonsProviderProxy.Arg();
        arg.setDescribeApiName("");
        arg.setButtonList(Arrays.asList());

        when(findButtonsByDescribeApiNameManager.getLocalProvider(""))
                .thenReturn(filterButtonsProvider);
        when(filterButtonsProvider.getButtons(eq(user), any()))
                .thenReturn(Arrays.asList());

        // Act
        FilterButtonsProviderProxy.Result result = objectFilterButtonsProviderService.findButtons(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertTrue(result.getResult().isEmpty());
        verify(findButtonsByDescribeApiNameManager).getLocalProvider("");
        verify(filterButtonsProvider).getButtons(eq(user), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectFilterButtonsProviderService);
        assertNotNull(findButtonsByDescribeApiNameManager);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("FilterButtonsProvider", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }
}
