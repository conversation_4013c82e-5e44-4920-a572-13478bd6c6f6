package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.fieldshowname.FindFieldShowName;
import com.facishare.paas.appframework.core.predef.service.dto.fieldshowname.FindRecordFieldMapping;
import com.facishare.paas.appframework.core.predef.service.dto.fieldshowname.UpsertFieldShowName;
import com.facishare.paas.appframework.metadata.FieldShowNameLogicService;
import com.facishare.paas.appframework.metadata.repository.model.FieldShowName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectFieldShowNameService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectFieldShowNameService单元测试")
class ObjectFieldShowNameServiceTest {

    @Mock
    private FieldShowNameLogicService fieldShowNameLogicService;
    
    @InjectMocks
    private ObjectFieldShowNameService objectFieldShowNameService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "test_object__c";
    private final String RECORD_API_NAME = "test_record__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "field_show_name", "test_method");
    }    /**
     * GenerateByAI
     * 测试内容描述：测试查找字段显示名称成功的正常场景
     */
    @Test
    @DisplayName("测试查找字段显示名称成功")
    void testFindFieldShowNameSuccess() {
        // Arrange
        FindFieldShowName.Arg arg = new FindFieldShowName.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        FieldShowName fieldShowName1 = new FieldShowName();
        fieldShowName1.setFieldApiName("field1");
        fieldShowName1.setName("字段1");

        FieldShowName fieldShowName2 = new FieldShowName();
        fieldShowName2.setFieldApiName("field2");
        fieldShowName2.setName("字段2");
        
        List<FieldShowName> mockFieldShowNames = Arrays.asList(fieldShowName1, fieldShowName2);

        when(fieldShowNameLogicService.findFieldShowNamesByApiName(eq(user), eq(DESCRIBE_API_NAME)))
                .thenReturn(mockFieldShowNames);

        // Act
        FindFieldShowName.Result result = objectFieldShowNameService.findFieldShowName(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getFieldShowNames());
        assertEquals(2, result.getFieldShowNames().size());
        assertEquals("field1", result.getFieldShowNames().get(0).getFieldApiName());
        assertEquals("字段1", result.getFieldShowNames().get(0).getName());
        verify(fieldShowNameLogicService).findFieldShowNamesByApiName(eq(user), eq(DESCRIBE_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新字段显示名称成功的正常场景
     */
    @Test
    @DisplayName("测试更新字段显示名称成功")
    void testUpsertFieldShowNameSuccess() {
        // Arrange
        UpsertFieldShowName.Arg arg = new UpsertFieldShowName.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setRecordApiName(RECORD_API_NAME);
        
        FieldShowName fieldShowName1 = new FieldShowName();
        fieldShowName1.setFieldApiName("field1");
        fieldShowName1.setName("字段1");
        
        arg.setFieldShowNames(Arrays.asList(fieldShowName1));

        doNothing().when(fieldShowNameLogicService).upsertFieldShowName(eq(user), eq(DESCRIBE_API_NAME), eq(RECORD_API_NAME), eq(arg.getFieldShowNames()));

        // Act
        UpsertFieldShowName.Result result = objectFieldShowNameService.upsertFieldShowName(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(fieldShowNameLogicService).upsertFieldShowName(eq(user), eq(DESCRIBE_API_NAME), eq(RECORD_API_NAME), eq(arg.getFieldShowNames()));
    }
}