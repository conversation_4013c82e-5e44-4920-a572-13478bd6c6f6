package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataManager;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataProvider;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataProxy;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.UdefButton;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectFindAndFilterButtonsByDataProviderService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectFindAndFilterButtonsByDataProviderService单元测试")
class ObjectFindAndFilterButtonsByDataProviderServiceTest {

    @Mock
    private FindAndFilterButtonsByDataManager manager;
    
    @Mock
    private FindAndFilterButtonsByDataProvider provider;
    
    @InjectMocks
    private ObjectFindAndFilterButtonsByDataProviderService service;
    
    @Mock
    private ServiceContext serviceContext;
    
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";
    private final String DATA_ID = "test-data-id";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        when(serviceContext.getUser()).thenReturn(user);
        when(serviceContext.getTenantId()).thenReturn(TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findAndFilterButtonsByData方法 - 正常场景
     */
    @Test
    @DisplayName("测试findAndFilterButtonsByData方法 - 正常场景")
    void testFindAndFilterButtonsByData_Success() {
        // Arrange
        FindAndFilterButtonsByDataProxy.Arg arg = new FindAndFilterButtonsByDataProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setUsePageType(ButtonUsePageType.Detail);
        
        // 创建按钮数据
        Map<String, Object> buttonMap = new HashMap<>();
        buttonMap.put("apiName", "testButton");
        buttonMap.put("name", "Test Button");
        arg.setButtons(Arrays.asList(buttonMap));

        // Mock provider
        when(manager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(provider);
        
        // Mock provider返回的按钮
        IUdefButton mockButton = new UdefButton();
        mockButton.setApiName("testButton");
        List<IUdefButton> mockButtons = Arrays.asList(mockButton);
        
        when(provider.findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class)))
                .thenReturn(mockButtons);

        // Act
        FindAndFilterButtonsByDataProxy.Result result = service.findAndFilterButtonsByData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtons());
        assertEquals(1, result.getButtons().size());
        
        // 验证调用
        verify(manager).getLocalProvider(DESCRIBE_API_NAME);
        verify(provider).findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findAndFilterButtonsByData方法 - 空按钮列表
     */
    @Test
    @DisplayName("测试findAndFilterButtonsByData方法 - 空按钮列表")
    void testFindAndFilterButtonsByData_EmptyButtons() {
        // Arrange
        FindAndFilterButtonsByDataProxy.Arg arg = new FindAndFilterButtonsByDataProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setUsePageType(ButtonUsePageType.DataList);
        arg.setButtons(Collections.emptyList());

        // Mock provider
        when(manager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(provider);
        when(provider.findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class)))
                .thenReturn(Collections.emptyList());

        // Act
        FindAndFilterButtonsByDataProxy.Result result = service.findAndFilterButtonsByData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getButtons() == null || result.getButtons().isEmpty());
        
        // 验证调用
        verify(manager).getLocalProvider(DESCRIBE_API_NAME);
        verify(provider).findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findAndFilterButtonsByData方法 - null按钮列表
     */
    @Test
    @DisplayName("测试findAndFilterButtonsByData方法 - null按钮列表")
    void testFindAndFilterButtonsByData_NullButtons() {
        // Arrange
        FindAndFilterButtonsByDataProxy.Arg arg = new FindAndFilterButtonsByDataProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setUsePageType(ButtonUsePageType.Detail);
        arg.setButtons(null);

        // Mock provider
        when(manager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(provider);
        when(provider.findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class)))
                .thenReturn(Collections.emptyList());

        // Act
        FindAndFilterButtonsByDataProxy.Result result = service.findAndFilterButtonsByData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getButtons() == null || result.getButtons().isEmpty());
        
        // 验证调用
        verify(manager).getLocalProvider(DESCRIBE_API_NAME);
        verify(provider).findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findAndFilterButtonsByData方法 - 多个按钮
     */
    @Test
    @DisplayName("测试findAndFilterButtonsByData方法 - 多个按钮")
    void testFindAndFilterButtonsByData_MultipleButtons() {
        // Arrange
        FindAndFilterButtonsByDataProxy.Arg arg = new FindAndFilterButtonsByDataProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setUsePageType(ButtonUsePageType.Detail);
        
        // 创建多个按钮数据
        Map<String, Object> button1 = new HashMap<>();
        button1.put("apiName", "button1");
        button1.put("name", "Button 1");
        
        Map<String, Object> button2 = new HashMap<>();
        button2.put("apiName", "button2");
        button2.put("name", "Button 2");
        
        arg.setButtons(Arrays.asList(button1, button2));

        // Mock provider
        when(manager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(provider);
        
        // Mock provider返回的按钮
        IUdefButton mockButton1 = new UdefButton();
        mockButton1.setApiName("button1");

        IUdefButton mockButton2 = new UdefButton();
        mockButton2.setApiName("button2");
        
        List<IUdefButton> mockButtons = Arrays.asList(mockButton1, mockButton2);
        
        when(provider.findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class)))
                .thenReturn(mockButtons);

        // Act
        FindAndFilterButtonsByDataProxy.Result result = service.findAndFilterButtonsByData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtons());
        assertEquals(2, result.getButtons().size());
        
        // 验证调用
        verify(manager).getLocalProvider(DESCRIBE_API_NAME);
        verify(provider).findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findAndFilterButtonsByData方法 - 不同的UsePageType
     */
    @Test
    @DisplayName("测试findAndFilterButtonsByData方法 - 不同的UsePageType")
    void testFindAndFilterButtonsByData_DifferentUsePageTypes() {
        // Arrange
        FindAndFilterButtonsByDataProxy.Arg arg = new FindAndFilterButtonsByDataProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setUsePageType(ButtonUsePageType.DataList);
        arg.setButtons(Collections.emptyList());

        // Mock provider
        when(manager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(provider);
        when(provider.findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class)))
                .thenReturn(Collections.emptyList());

        // Act
        FindAndFilterButtonsByDataProxy.Result result = service.findAndFilterButtonsByData(arg, serviceContext);

        // Assert
        assertNotNull(result);

        // 验证调用时传递了正确的UsePageType
        verify(provider).findAndFilterButtonsByData(eq(user), argThat(providerArg ->
                providerArg.getUsePageType() == ButtonUsePageType.DataList));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(manager);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数转换的正确性
     */
    @Test
    @DisplayName("测试参数转换的正确性")
    void testArgumentConversion() {
        // Arrange
        FindAndFilterButtonsByDataProxy.Arg arg = new FindAndFilterButtonsByDataProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setUsePageType(ButtonUsePageType.Detail);
        
        Map<String, Object> buttonMap = new HashMap<>();
        buttonMap.put("apiName", "testButton");
        buttonMap.put("name", "Test Button");
        arg.setButtons(Arrays.asList(buttonMap));

        // Mock provider
        when(manager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(provider);
        when(provider.findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class)))
                .thenReturn(Collections.emptyList());

        // Act
        service.findAndFilterButtonsByData(arg, serviceContext);

        // Assert - 验证传递给provider的参数是否正确
        verify(provider).findAndFilterButtonsByData(eq(user), argThat(providerArg -> {
            return DESCRIBE_API_NAME.equals(providerArg.getDescribeApiName()) &&
                   DATA_ID.equals(providerArg.getDataId()) &&
                   ButtonUsePageType.Detail.equals(providerArg.getUsePageType()) &&
                   providerArg.getButtons() != null;
        }));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试manager获取provider的调用
     */
    @Test
    @DisplayName("测试manager获取provider的调用")
    void testManagerGetLocalProvider() {
        // Arrange
        FindAndFilterButtonsByDataProxy.Arg arg = new FindAndFilterButtonsByDataProxy.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setUsePageType(ButtonUsePageType.Detail);
        arg.setButtons(Collections.emptyList());

        when(manager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(provider);
        when(provider.findAndFilterButtonsByData(eq(user), any(FindAndFilterButtonsByDataProvider.Arg.class)))
                .thenReturn(Collections.emptyList());

        // Act
        service.findAndFilterButtonsByData(arg, serviceContext);

        // Assert
        verify(manager, times(1)).getLocalProvider(DESCRIBE_API_NAME);
    }
}
