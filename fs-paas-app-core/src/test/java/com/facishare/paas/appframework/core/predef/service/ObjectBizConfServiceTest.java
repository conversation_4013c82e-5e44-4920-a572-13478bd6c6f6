package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.bizconf.*;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.bizconf.bean.BizType;
import com.fxiaoke.bizconf.bean.ConfigDefPojo;
import com.fxiaoke.bizconf.bean.ConfigPojo;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectBizConfService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectBizConfService单元测试")
class ObjectBizConfServiceTest {

    @Mock
    private BizConfClient bizConfClient;
    
    @Mock
    private RecordTypeLogicService recordTypeLogicService;
    
    @Mock
    private BizConfService bizConfService;
    
    @Mock
    private MessagePollingService messagePollingService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @InjectMocks
    private ObjectBizConfService objectBizConfService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "test_object__c";
    private final String CONFIG_CODE = "test_config";
    private final String BIZ_TYPE = "record_type";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "biz_conf", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询配置数据成功的正常场景
     */
    @Test
    @DisplayName("测试查询配置数据成功")
    void testQueryConfigDataSuccess() throws Exception {
        // Arrange
        QueryConfigData.Arg arg = new QueryConfigData.Arg();
        arg.setConfigCode(CONFIG_CODE);
        arg.setBizTypeValueList(Arrays.asList("value1", "value2"));

        ConfigPojo configPojo = ConfigPojo.builder()
                .key(CONFIG_CODE)
                .assistantKey("value1")
                .configValue("test_value")
                .build();
        
        when(bizConfClient.queryConfigByMultipleKey(eq(TENANT_ID), eq("CRM"), eq(CONFIG_CODE), any()))
                .thenReturn(Arrays.asList(configPojo));

        // Act
        QueryConfigData.Result result = objectBizConfService.queryConfigData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfigDataList());
        assertEquals(1, result.getConfigDataList().size());
        assertEquals(CONFIG_CODE, result.getConfigDataList().get(0).getConfigCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询配置数据异常处理
     */
    @Test
    @DisplayName("测试查询配置数据异常处理")
    void testQueryConfigDataException() throws Exception {
        // Arrange
        QueryConfigData.Arg arg = new QueryConfigData.Arg();
        arg.setConfigCode(CONFIG_CODE);
        arg.setBizTypeValueList(Arrays.asList("value1"));

        FRestClientException mockException = mock(FRestClientException.class);
        when(mockException.getMessage()).thenReturn("Internal Server Error");
        when(bizConfClient.queryConfigByMultipleKey(eq(TENANT_ID), eq("CRM"), eq(CONFIG_CODE), any()))
                .thenThrow(mockException);

        // Act & Assert
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, 
                () -> objectBizConfService.queryConfigData(arg, serviceContext));
        assertEquals("Internal Server Error", exception.getMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存配置数据成功的正常场景
     */
    @Test
    @DisplayName("测试保存配置数据成功")
    void testSaveConfigDataSuccess() throws Exception {
        // Arrange
        SaveConfigData.Arg arg = new SaveConfigData.Arg();
        ConfigData configData = ConfigData.builder()
                .configCode(CONFIG_CODE)
                .bizTypeValue("value1")
                .configValue("test_value")
                .build();
        arg.setConfigDataList(Arrays.asList(configData));

        when(bizConfClient.batchUpsertMultipleConfig(eq(TENANT_ID), any())).thenReturn(0);

        // Act
        SaveConfigData.Result result = objectBizConfService.saveConfigData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(bizConfClient).batchUpsertMultipleConfig(eq(TENANT_ID), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存配置数据异常处理
     */
    @Test
    @DisplayName("测试保存配置数据异常处理")
    void testSaveConfigDataException() throws Exception {
        // Arrange
        SaveConfigData.Arg arg = new SaveConfigData.Arg();
        ConfigData configData = ConfigData.builder()
                .configCode(CONFIG_CODE)
                .bizTypeValue("value1")
                .configValue("test_value")
                .build();
        arg.setConfigDataList(Arrays.asList(configData));

        FRestClientException mockException2 = mock(FRestClientException.class);
        when(mockException2.getMessage()).thenReturn("Internal Server Error");
        doThrow(mockException2)
                .when(bizConfClient).batchUpsertMultipleConfig(eq(TENANT_ID), any());

        // Act & Assert
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, 
                () -> objectBizConfService.saveConfigData(arg, serviceContext));
        assertEquals("Internal Server Error", exception.getMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询配置定义成功的正常场景
     */
    @Test
    @DisplayName("测试查询配置定义成功")
    void testQueryConfigDefSuccess() {
        // Arrange
        QueryConfigDef.Arg arg = new QueryConfigDef.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setBizType(BIZ_TYPE);

        ConfigDefPojo configDefPojo = new ConfigDefPojo();
        configDefPojo.setConfigCode(CONFIG_CODE);
        
        when(bizConfService.queryConfigDef(TENANT_ID, DESCRIBE_API_NAME, BIZ_TYPE))
                .thenReturn(Arrays.asList(configDefPojo));

        // Act
        QueryConfigDef.Result result = objectBizConfService.queryConfigDef(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfigDefList());
        assertEquals(1, result.getConfigDefList().size());
        assertEquals(CONFIG_CODE, result.getConfigDefList().get(0).getConfigCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存配置定义成功的正常场景
     */
    @Test
    @DisplayName("测试保存配置定义成功")
    void testSaveConfigDefSuccess() throws Exception {
        // Arrange
        SaveConfigDef.Arg arg = new SaveConfigDef.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setBizType(BIZ_TYPE);
        
        ConfigDefPojo configDefPojo = new ConfigDefPojo();
        configDefPojo.setConfigCode(CONFIG_CODE);
        arg.setConfigDefList(Arrays.asList(configDefPojo));

        when(bizConfClient.batchUpsertConfigDef(eq(TENANT_ID), any())).thenReturn(0);

        // Act
        SaveConfigDef.Result result = objectBizConfService.saveConfigDef(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(bizConfClient).batchUpsertConfigDef(eq(TENANT_ID), any());
        assertEquals(TENANT_ID, configDefPojo.getTenantId());
        assertEquals(DESCRIBE_API_NAME, configDefPojo.getDescribeApiName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存配置定义空列表处理
     */
    @Test
    @DisplayName("测试保存配置定义空列表处理")
    void testSaveConfigDefEmptyList() throws Exception {
        // Arrange
        SaveConfigDef.Arg arg = new SaveConfigDef.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setBizType(BIZ_TYPE);
        arg.setConfigDefList(Lists.newArrayList());

        // Act
        SaveConfigDef.Result result = objectBizConfService.saveConfigDef(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(bizConfClient, never()).batchUpsertConfigDef(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存配置定义异常处理
     */
    @Test
    @DisplayName("测试保存配置定义异常处理")
    void testSaveConfigDefException() throws Exception {
        // Arrange
        SaveConfigDef.Arg arg = new SaveConfigDef.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setBizType(BIZ_TYPE);

        ConfigDefPojo configDefPojo = new ConfigDefPojo();
        configDefPojo.setConfigCode(CONFIG_CODE);
        arg.setConfigDefList(Arrays.asList(configDefPojo));

        FRestClientException mockException3 = mock(FRestClientException.class);
        when(mockException3.getMessage()).thenReturn("Internal Server Error");
        doThrow(mockException3)
                .when(bizConfClient).batchUpsertConfigDef(eq(TENANT_ID), any());

        // Act & Assert
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class,
                () -> objectBizConfService.saveConfigDef(arg, serviceContext));
        assertEquals("Internal Server Error", exception.getMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除配置定义成功的正常场景
     */
    @Test
    @DisplayName("测试删除配置定义成功")
    void testDeleteConfigDefSuccess() throws Exception {
        // Arrange
        DeleteConfigDef.Arg arg = new DeleteConfigDef.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setBizType(BIZ_TYPE);
        arg.setConfigCodeList(Arrays.asList(CONFIG_CODE));

        when(bizConfClient.batchDeleteConfigDef(eq(TENANT_ID), eq(DESCRIBE_API_NAME), any(BizType.class), any())).thenReturn(0);

        // Act
        DeleteConfigDef.Result result = objectBizConfService.deleteConfigDef(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(bizConfClient).batchDeleteConfigDef(eq(TENANT_ID), eq(DESCRIBE_API_NAME), any(BizType.class), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除配置定义空列表处理
     */
    @Test
    @DisplayName("测试删除配置定义空列表处理")
    void testDeleteConfigDefEmptyList() throws Exception {
        // Arrange
        DeleteConfigDef.Arg arg = new DeleteConfigDef.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setBizType(BIZ_TYPE);
        arg.setConfigCodeList(Lists.newArrayList());

        // Act
        DeleteConfigDef.Result result = objectBizConfService.deleteConfigDef(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(bizConfClient, never()).batchDeleteConfigDef(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除配置定义异常处理
     */
    @Test
    @DisplayName("测试删除配置定义异常处理")
    void testDeleteConfigDefException() throws Exception {
        // Arrange
        DeleteConfigDef.Arg arg = new DeleteConfigDef.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setBizType(BIZ_TYPE);
        arg.setConfigCodeList(Arrays.asList(CONFIG_CODE));

        FRestClientException mockException4 = mock(FRestClientException.class);
        when(mockException4.getMessage()).thenReturn("Internal Server Error");
        doThrow(mockException4)
                .when(bizConfClient).batchDeleteConfigDef(eq(TENANT_ID), eq(DESCRIBE_API_NAME), any(BizType.class), any());

        // Act & Assert
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class,
                () -> objectBizConfService.deleteConfigDef(arg, serviceContext));
        assertEquals("Internal Server Error", exception.getMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量保存配置数据成功的正常场景
     */
    @Test
    @DisplayName("测试批量保存配置数据成功")
    void testSaveAllConfigDataSuccess() throws Exception {
        // Arrange
        SaveAllConfigData.Arg arg = new SaveAllConfigData.Arg();
        Map<String, List<ConfigData>> configDataMap = Maps.newHashMap();
        ConfigData configData = ConfigData.builder()
                .configCode(CONFIG_CODE)
                .bizTypeValue("value1")
                .configValue("test_value")
                .build();
        configDataMap.put(CONFIG_CODE, Arrays.asList(configData));
        arg.setConfigDataMap(configDataMap);

        when(bizConfClient.batchUpsertMultipleConfig(eq(TENANT_ID), any())).thenReturn(0);

        // Act
        SaveAllConfigData.Result result = objectBizConfService.saveAllConfigData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(bizConfClient).batchUpsertMultipleConfig(eq(TENANT_ID), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量保存配置数据空Map处理
     */
    @Test
    @DisplayName("测试批量保存配置数据空Map处理")
    void testSaveAllConfigDataEmptyMap() throws Exception {
        // Arrange
        SaveAllConfigData.Arg arg = new SaveAllConfigData.Arg();
        arg.setConfigDataMap(Maps.newHashMap());

        // Act
        SaveAllConfigData.Result result = objectBizConfService.saveAllConfigData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(bizConfClient, never()).batchUpsertMultipleConfig(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量保存配置数据异常处理
     */
    @Test
    @DisplayName("测试批量保存配置数据异常处理")
    void testSaveAllConfigDataException() throws Exception {
        // Arrange
        SaveAllConfigData.Arg arg = new SaveAllConfigData.Arg();
        Map<String, List<ConfigData>> configDataMap = Maps.newHashMap();
        ConfigData configData = ConfigData.builder()
                .configCode(CONFIG_CODE)
                .bizTypeValue("value1")
                .configValue("test_value")
                .build();
        configDataMap.put(CONFIG_CODE, Arrays.asList(configData));
        arg.setConfigDataMap(configDataMap);

        FRestClientException mockException5 = mock(FRestClientException.class);
        when(mockException5.getMessage()).thenReturn("Internal Server Error");
        doThrow(mockException5)
                .when(bizConfClient).batchUpsertMultipleConfig(eq(TENANT_ID), any());

        // Act & Assert
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class,
                () -> objectBizConfService.saveAllConfigData(arg, serviceContext));
        assertEquals("Internal Server Error", exception.getMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Arrange & Act & Assert
        assertNotNull(objectBizConfService);
        assertNotNull(bizConfClient);
        assertNotNull(recordTypeLogicService);
        assertNotNull(bizConfService);
        assertNotNull(messagePollingService);
        assertNotNull(describeLogicService);
    }
}
