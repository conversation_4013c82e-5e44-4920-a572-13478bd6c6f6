package com.facishare.paas.appframework.core.predef.service.onlinedoc;

import com.facishare.paas.appframework.core.predef.service.BaseServiceTest;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.*;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginAppType;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginDefineType;
import com.facishare.paas.appframework.metadata.FileStoreService;
import com.facishare.paas.appframework.metadata.onlinedoc.PackagePluginLogicService;
import com.facishare.paas.appframework.metadata.repository.model.PackagePluginEntity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * PackagePluginService单元测试类
 * 测试包插件服务的所有@ServiceMethod方法
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("PackagePluginService单元测试")
public class PackagePluginServiceTest extends BaseServiceTest {

    @Mock
    private PackagePluginLogicService packagePluginLogicService;

    @Mock
    private FileStoreService fileStoreService;

    @InjectMocks
    private PackagePluginService packagePluginService;

    private static final String PLUGIN_API_NAME = "test_plugin";
    private static final String PLUGIN_NAME = "Test Plugin";
    private static final String PLUGIN_ICON = "test_icon.png";
    private static final String PLUGIN_ICON_SIGN = "test_sign";
    private static final String PLUGIN_ICON_URL = "http://example.com/icon.png";
    private static final String APP_TYPE = PackagePluginAppType.ONLINE_DOC.getType();
    private static final String DEFINE_TYPE = PackagePluginDefineType.CUSTOM.getType();

    @Override
    protected String getServiceName() {
        return "packagePlugin";
    }

    @BeforeEach
    void setUp() {
        // 基础设置已在BaseServiceTest中完成
    }

    @Test
    @DisplayName("GenerateByAI - 测试getAllPlugin成功场景")
    void testGetAllPlugin_Success() {
        // Arrange
        GetAllPluginVo.Arg arg = new GetAllPluginVo.Arg();
        arg.setPageNumber(0);
        arg.setPageSize(10);
        arg.setAppType(APP_TYPE);

        // 构造插件实体
        PackagePluginEntity plugin1 = createTestPluginEntity("plugin1", "Plugin 1", true, true);
        PackagePluginEntity plugin2 = createTestPluginEntity("plugin2", "Plugin 2", false, false);
        List<PackagePluginEntity> pluginList = Lists.newArrayList(plugin1, plugin2);

        when(packagePluginLogicService.getPluginList(testUser, APP_TYPE, 0, 11))
                .thenReturn(pluginList);

        when(fileStoreService.generateCpathAccessUrl(eq(testUser), eq(PLUGIN_ICON), eq(PLUGIN_ICON_SIGN)))
                .thenReturn(PLUGIN_ICON_URL);

        // Act
        GetAllPluginVo.Result result = packagePluginService.getAllPlugin(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPluginList());
        assertEquals(2, result.getPluginList().size());
        assertFalse(result.isHasMore()); // 2个插件，小于pageSize+1

        GetAllPluginVo.PluginItem item1 = result.getPluginList().get(0);
        assertEquals("plugin1", item1.getPluginApiName());
        assertEquals("Plugin 1", item1.getPluginName());
        assertEquals(DEFINE_TYPE, item1.getDefineType());
        assertEquals(APP_TYPE, item1.getAppType());
        assertTrue(item1.isActive());
        assertTrue(item1.isBinding());

        verify(packagePluginLogicService, times(1)).getPluginList(testUser, APP_TYPE, 0, 11);
        verify(fileStoreService, times(2)).generateCpathAccessUrl(eq(testUser), eq(PLUGIN_ICON), eq(PLUGIN_ICON_SIGN));
    }

    @Test
    @DisplayName("GenerateByAI - 测试getAllPlugin分页场景")
    void testGetAllPlugin_WithPagination() {
        // Arrange
        GetAllPluginVo.Arg arg = new GetAllPluginVo.Arg();
        arg.setPageNumber(0);
        arg.setPageSize(2);
        arg.setAppType(APP_TYPE);

        // 构造3个插件实体，超过pageSize
        PackagePluginEntity plugin1 = createTestPluginEntity("plugin1", "Plugin 1", true, true);
        PackagePluginEntity plugin2 = createTestPluginEntity("plugin2", "Plugin 2", false, false);
        PackagePluginEntity plugin3 = createTestPluginEntity("plugin3", "Plugin 3", true, false);
        List<PackagePluginEntity> pluginList = Lists.newArrayList(plugin1, plugin2, plugin3);

        when(packagePluginLogicService.getPluginList(testUser, APP_TYPE, 0, 3))
                .thenReturn(pluginList);

        when(fileStoreService.generateCpathAccessUrl(eq(testUser), eq(PLUGIN_ICON), eq(PLUGIN_ICON_SIGN)))
                .thenReturn(PLUGIN_ICON_URL);

        // Act
        GetAllPluginVo.Result result = packagePluginService.getAllPlugin(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPluginList());
        assertEquals(2, result.getPluginList().size()); // 只返回pageSize个
        assertTrue(result.isHasMore()); // 有更多数据

        verify(packagePluginLogicService, times(1)).getPluginList(testUser, APP_TYPE, 0, 3);
    }

    @Test
    @DisplayName("GenerateByAI - 测试getAllPlugin空结果场景")
    void testGetAllPlugin_EmptyResult() {
        // Arrange
        GetAllPluginVo.Arg arg = new GetAllPluginVo.Arg();
        arg.setPageNumber(0);
        arg.setPageSize(10);
        arg.setAppType(APP_TYPE);

        when(packagePluginLogicService.getPluginList(testUser, APP_TYPE, 0, 11))
                .thenReturn(Lists.newArrayList());

        // Act
        GetAllPluginVo.Result result = packagePluginService.getAllPlugin(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPluginList());
        assertEquals(0, result.getPluginList().size());
        assertFalse(result.isHasMore());

        verify(packagePluginLogicService, times(1)).getPluginList(testUser, APP_TYPE, 0, 11);
        verify(fileStoreService, never()).generateCpathAccessUrl(any(), any(), any());
    }

    @Test
    @DisplayName("GenerateByAI - 测试getUsingPlugin成功场景")
    void testGetUsingPlugin_Success() {
        // Arrange
        GetUsingPluginVo.Arg arg = new GetUsingPluginVo.Arg();
        arg.setPageNumber(0);
        arg.setPageSize(10);
        arg.setAppType(APP_TYPE);

        // 构造正在使用的插件实体
        PackagePluginEntity plugin1 = createTestPluginEntity("plugin1", "Plugin 1", true, true);
        List<PackagePluginEntity> pluginList = Lists.newArrayList(plugin1);

        when(packagePluginLogicService.getUsingPluginList(testUser, APP_TYPE, 0, 11))
                .thenReturn(pluginList);

        when(fileStoreService.generateCpathAccessUrl(eq(testUser), eq(PLUGIN_ICON), eq(PLUGIN_ICON_SIGN)))
                .thenReturn(PLUGIN_ICON_URL);

        // Act
        GetUsingPluginVo.Result result = packagePluginService.getUsingPlugin(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPluginList());
        assertEquals(1, result.getPluginList().size());
        assertFalse(result.isHasMore());

        GetAllPluginVo.PluginItem item = result.getPluginList().get(0);
        assertEquals("plugin1", item.getPluginApiName());
        assertTrue(item.isActive());
        assertTrue(item.isBinding());

        verify(packagePluginLogicService, times(1)).getUsingPluginList(testUser, APP_TYPE, 0, 11);
        verify(fileStoreService, times(1)).generateCpathAccessUrl(eq(testUser), eq(PLUGIN_ICON), eq(PLUGIN_ICON_SIGN));
    }

    @Test
    @DisplayName("GenerateByAI - 测试getUsingPlugin分页场景")
    void testGetUsingPlugin_WithPagination() {
        // Arrange
        GetUsingPluginVo.Arg arg = new GetUsingPluginVo.Arg();
        arg.setPageNumber(0);
        arg.setPageSize(1);
        arg.setAppType(APP_TYPE);

        // 构造2个正在使用的插件实体，超过pageSize
        PackagePluginEntity plugin1 = createTestPluginEntity("plugin1", "Plugin 1", true, true);
        PackagePluginEntity plugin2 = createTestPluginEntity("plugin2", "Plugin 2", true, true);
        List<PackagePluginEntity> pluginList = Lists.newArrayList(plugin1, plugin2);

        when(packagePluginLogicService.getUsingPluginList(testUser, APP_TYPE, 0, 2))
                .thenReturn(pluginList);

        when(fileStoreService.generateCpathAccessUrl(eq(testUser), eq(PLUGIN_ICON), eq(PLUGIN_ICON_SIGN)))
                .thenReturn(PLUGIN_ICON_URL);

        // Act
        GetUsingPluginVo.Result result = packagePluginService.getUsingPlugin(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPluginList());
        assertEquals(1, result.getPluginList().size()); // 只返回pageSize个
        assertTrue(result.isHasMore()); // 有更多数据

        verify(packagePluginLogicService, times(1)).getUsingPluginList(testUser, APP_TYPE, 0, 2);
    }

    @Test
    @DisplayName("GenerateByAI - 测试enablePlugin成功场景")
    void testEnablePlugin_Success() {
        // Arrange
        EnablePluginVo.Arg arg = new EnablePluginVo.Arg();
        arg.setPluginApiName(PLUGIN_API_NAME);

        // 构造已绑定的插件实体
        PackagePluginEntity plugin = createTestPluginEntity(PLUGIN_API_NAME, PLUGIN_NAME, false, true);

        when(packagePluginLogicService.findPluginByApiName(testUser, PLUGIN_API_NAME))
                .thenReturn(plugin);

        when(packagePluginLogicService.updatePlugin(eq(testUser), any(PackagePluginEntity.class), anyList()))
                .thenReturn(plugin);

        // Act
        EnablePluginVo.Result result = packagePluginService.enablePlugin(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());

        verify(packagePluginLogicService, times(1)).findPluginByApiName(testUser, PLUGIN_API_NAME);
        verify(packagePluginLogicService, times(1)).updatePlugin(eq(testUser), any(PackagePluginEntity.class), anyList());
    }

    /**
     * 创建测试用的插件实体
     */
    private PackagePluginEntity createTestPluginEntity(String apiName, String name, boolean active, boolean binding) {
        PackagePluginEntity entity = new PackagePluginEntity();
        entity.setPluginApiName(apiName);
        entity.setName(name);
        entity.setDefineType(DEFINE_TYPE);
        entity.setAppType(APP_TYPE);
        entity.setIcon(PLUGIN_ICON);
        entity.setIconSign(PLUGIN_ICON_SIGN);
        entity.setActive(active);
        entity.setBinding(binding);
        // HttpIcon字段可能是计算字段，不需要设置
        
        Map<String, String> extraInfo = Maps.newHashMap();
        extraInfo.put("key1", "value1");
        entity.setExtraInfo(extraInfo);
        
        return entity;
    }
}
