package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.PlatServiceProxy;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.CustomButtonService;
import com.facishare.paas.appframework.metadata.MultiCurrencyLogicService;
import com.facishare.paas.appframework.metadata.PostActionService;
import com.facishare.paas.appframework.metadata.ReferenceLogicService;
import com.facishare.paas.appframework.metadata.multicurrency.RefreshInternational;
import com.facishare.paas.appframework.metadata.sandbox.ChangeSetFindByQuery;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.service.IMetadataMultiOrgService;
import com.facishare.paas.metadata.impl.UdefAction;
import com.facishare.paas.metadata.impl.UdefButton;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectSandboxService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectSandboxService单元测试")
class ObjectSandboxServiceTest {

    @Mock
    private MultiCurrencyLogicService multiCurrencyLogicService;
    
    @Mock
    private IMetadataMultiOrgService metadataMultiOrgService;
    
    @Mock
    private PlatServiceProxy platServiceProxy;
    
    @Mock
    private CustomButtonService customButtonService;
    
    @Mock
    private PostActionService postActionService;
    
    @Mock
    private ReferenceLogicService referenceLogicService;
    
    @Mock
    private ServiceContext serviceContext;
    
    @InjectMocks
    private ObjectSandboxService service;
    
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        when(serviceContext.getUser()).thenReturn(user);
        when(serviceContext.getTenantId()).thenReturn(TENANT_ID);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试refreshMultiCurrency方法
     */
    @Test
    @DisplayName("测试refreshMultiCurrency方法")
    void testRefreshMultiCurrency() {
        // Arrange
        RefreshInternational.Arg arg = new RefreshInternational.Arg();
        arg.setObjectApiNames(Arrays.asList("AccountObj", "OpportunityObj"));

        // Act
        RefreshInternational.Result result = service.refreshMultiCurrency(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(multiCurrencyLogicService).refreshMultiCurrency(eq(arg), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试refreshMultiOrganization方法
     */
    @Test
    @DisplayName("测试refreshMultiOrganization方法")
    void testRefreshMultiOrganization() {
        // Arrange
        RefreshInternational.Arg arg = new RefreshInternational.Arg();
        arg.setObjectApiNames(Arrays.asList("AccountObj", "OpportunityObj"));

        // Mock organization status
        com.facishare.paas.appframework.common.service.dto.OrganizationStatus.Result orgResult =
                new com.facishare.paas.appframework.common.service.dto.OrganizationStatus.Result();
        orgResult.setOpenOrganization(true);
        when(platServiceProxy.openOrganization(any())).thenReturn(orgResult);

        // Act
        RefreshInternational.Result result = service.refreshMultiOrganization(arg, serviceContext);

        // Assert
        assertNotNull(result);
        // 由于方法内部有复杂的逻辑和异步处理，这里主要验证方法能正常执行
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buttonReference方法 - 有效输入场景
     */
    @Test
    @DisplayName("测试buttonReference方法 - 有效输入场景")
    void testButtonReference_ValidInputs() {
        // Arrange
        ChangeSetFindByQuery.Arg arg = new ChangeSetFindByQuery.Arg();
        ChangeSetFindByQuery.SearchQueryInfo searchQueryInfo = new ChangeSetFindByQuery.SearchQueryInfo();
        
        List<ChangeSetFindByQuery.Filter> filters = Arrays.asList(
                createFilter("api_name", "testApiName"),
                createFilter("describe_api_name", "testDescribeApiName"),
                createFilter("tenant_id", "74255")
        );
        searchQueryInfo.setFilters(filters);
        arg.setSearchQueryInfo(searchQueryInfo);
        arg.setEnterpriseId(74255L);

        // Mock button
        IUdefButton mockButton = new UdefButton();
        when(customButtonService.findButtonByApiNameForDesigner(any(User.class), eq("testApiName"), eq("testDescribeApiName")))
                .thenReturn(mockButton);

        // Mock action
        IUdefAction mockAction = new UdefAction();
        mockAction.setActionParamter("{\"functionApiName\":\"testFunction\"}");
        when(postActionService.findActionListForDesigner(any(User.class), any(IUdefButton.class), eq("testDescribeApiName")))
                .thenReturn(Arrays.asList(mockAction));

        // Mock reference data
        ReferenceData mockReferenceData = ReferenceData.builder()
                .sourceValue("testApiName")
                .sourceType("button")
                .sourceLabel("Test Button")
                .targetType("function")
                .targetValue("testFunction")
                .build();
        when(referenceLogicService.queryByTargetList(eq("74255"), anyList()))
                .thenReturn(Arrays.asList(mockReferenceData));

        // Act
        ChangeSetFindByQuery.Result result = service.buttonReference(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getCount());
        assertEquals(1, result.getDataList().size());
        
        // 验证返回的数据结构
        Map<String, Object> dataMap = result.getDataList().get(0);
        assertEquals("button", dataMap.get("source_type"));
        assertEquals("Test Button", dataMap.get("source_label"));
        assertEquals("testApiName", dataMap.get("source_value"));
        assertEquals("function", dataMap.get("target_type"));
        assertEquals("testFunction", dataMap.get("target_value"));
        assertEquals("74255", dataMap.get("tenant_id"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buttonReference方法 - 无效输入场景
     */
    @ParameterizedTest
    @MethodSource("provideInvalidInputs")
    @DisplayName("测试buttonReference方法 - 无效输入场景")
    void testButtonReference_InvalidInputs(ChangeSetFindByQuery.Arg arg, String testCase) {
        // Act
        ChangeSetFindByQuery.Result result = service.buttonReference(arg, serviceContext);

        // Assert
        assertNotNull(result, "Result should not be null for test case: " + testCase);
        assertEquals(0, result.getCount(), "Count should be 0 for test case: " + testCase);
        assertTrue(result.getDataList().isEmpty(), "DataList should be empty for test case: " + testCase);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buttonReference方法 - 按钮不存在场景
     */
    @Test
    @DisplayName("测试buttonReference方法 - 按钮不存在场景")
    void testButtonReference_ButtonNotFound() {
        // Arrange
        ChangeSetFindByQuery.Arg arg = createValidArg();
        
        // Mock button not found
        when(customButtonService.findButtonByApiNameForDesigner(any(User.class), eq("testApiName"), eq("testDescribeApiName")))
                .thenReturn(null);
        
        // Mock empty action list
        when(postActionService.findActionListForDesigner(any(User.class), any(IUdefButton.class), eq("testDescribeApiName")))
                .thenReturn(Collections.emptyList());

        // Act
        ChangeSetFindByQuery.Result result = service.buttonReference(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getCount());
        assertTrue(result.getDataList().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(multiCurrencyLogicService);
        assertNotNull(metadataMultiOrgService);
        assertNotNull(platServiceProxy);
        assertNotNull(customButtonService);
        assertNotNull(postActionService);
        assertNotNull(referenceLogicService);
    }

    // 辅助方法
    private ChangeSetFindByQuery.Filter createFilter(String fieldName, String value) {
        ChangeSetFindByQuery.Filter filter = new ChangeSetFindByQuery.Filter();
        filter.setFieldName(fieldName);
        filter.setValues(Arrays.asList(value));
        return filter;
    }

    private ChangeSetFindByQuery.Arg createValidArg() {
        ChangeSetFindByQuery.Arg arg = new ChangeSetFindByQuery.Arg();
        ChangeSetFindByQuery.SearchQueryInfo searchQueryInfo = new ChangeSetFindByQuery.SearchQueryInfo();
        
        List<ChangeSetFindByQuery.Filter> filters = Arrays.asList(
                createFilter("api_name", "testApiName"),
                createFilter("describe_api_name", "testDescribeApiName"),
                createFilter("tenant_id", "74255")
        );
        searchQueryInfo.setFilters(filters);
        arg.setSearchQueryInfo(searchQueryInfo);
        arg.setEnterpriseId(74255L);
        return arg;
    }



    // 提供无效输入的测试数据
    private static Stream<Arguments> provideInvalidInputs() {
        return Stream.of(
                Arguments.of(null, "null argument"),
                Arguments.of(new ChangeSetFindByQuery.Arg(), "empty SearchQueryInfo"),
                Arguments.of(createStaticArgWithMissingFilter("api_name"), "missing api_name"),
                Arguments.of(createStaticArgWithMissingFilter("describe_api_name"), "missing describe_api_name"),
                Arguments.of(createStaticArgWithMissingFilter("tenant_id"), "missing tenant_id"),
                Arguments.of(createStaticArgWithEmptyFilters(), "empty filters")
        );
    }

    // 静态辅助方法用于参数化测试
    private static ChangeSetFindByQuery.Arg createStaticArgWithMissingFilter(String missingField) {
        ChangeSetFindByQuery.Arg arg = new ChangeSetFindByQuery.Arg();
        ChangeSetFindByQuery.SearchQueryInfo searchQueryInfo = new ChangeSetFindByQuery.SearchQueryInfo();

        List<ChangeSetFindByQuery.Filter> filters = Arrays.asList(
                createStaticFilter("api_name", "testApiName"),
                createStaticFilter("describe_api_name", "testDescribeApiName"),
                createStaticFilter("tenant_id", "74255")
        );

        // 移除指定的过滤器
        filters = filters.stream()
                .filter(filter -> !missingField.equals(filter.getFieldName()))
                .collect(java.util.stream.Collectors.toList());

        searchQueryInfo.setFilters(filters);
        arg.setSearchQueryInfo(searchQueryInfo);
        arg.setEnterpriseId(74255L);
        return arg;
    }

    private static ChangeSetFindByQuery.Arg createStaticArgWithEmptyFilters() {
        ChangeSetFindByQuery.Arg arg = new ChangeSetFindByQuery.Arg();
        ChangeSetFindByQuery.SearchQueryInfo searchQueryInfo = new ChangeSetFindByQuery.SearchQueryInfo();
        searchQueryInfo.setFilters(Collections.emptyList());
        arg.setSearchQueryInfo(searchQueryInfo);
        arg.setEnterpriseId(74255L);
        return arg;
    }

    private static ChangeSetFindByQuery.Filter createStaticFilter(String fieldName, String value) {
        ChangeSetFindByQuery.Filter filter = new ChangeSetFindByQuery.Filter();
        filter.setFieldName(fieldName);
        filter.setValues(Arrays.asList(value));
        return filter;
    }
}
